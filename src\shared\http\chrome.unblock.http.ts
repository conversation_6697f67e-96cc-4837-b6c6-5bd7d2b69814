import { ChromeStealthBrowser } from '../../browsers/chrome.stealth.js';
import { EnterpriseRoutes } from '../../paths.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';
import {
  BodySchema,
  QuerySchema,
  ResponseSchema,
  default as ChromiumUnblockRoute,
} from './chromium.unblock.http.js';

export default class ChromeUnblockPostRoute extends ChromiumUnblockRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromeUnblockPostRoute;
  browser = ChromeStealthBrowser;
  path = [EnterpriseRoutes.chromeUnblock];
}

export { BodySchema, QuerySchema, ResponseSchema };

// CapSolver SDK - Comprehensive captcha solving service
// Based on https://docs.capsolver.com/
interface CapSolverConfig {
  apiKey: string;
  baseUrl?: string;
  pollingInterval?: number;
  maxRetries?: number;
}

interface CapSolverTask {
  type: string;
  [key: string]: any;
}

interface CapSolverResponse {
  errorId: number;
  errorCode?: string;
  errorDescription?: string;
  status: 'idle' | 'processing' | 'ready' | 'failed';
  taskId?: string;
  solution?: any;
}

interface ProxyConfig {
  proxyType?: string;
  proxyAddress?: string;
  proxyPort?: string | number;
  proxyLogin?: string;
  proxyPassword?: string;
}

// ImageToText specific interfaces
interface ImageToTextOptions {
  websiteURL?: string;
  module?: 'common' | 'number';
  score?: number;
  images?: string[]; // For batch processing (up to 9 images)
}

interface ImageToTextResponse {
  text: string;
  answers?: string[]; // For number module batch processing
}

class CapSolverClient {
  private apiKey: string;
  private baseUrl: string;
  private pollingInterval: number;
  private maxRetries: number;

  constructor(config: CapSolverConfig) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://api.capsolver.com';
    this.pollingInterval = config.pollingInterval || 1000;
    this.maxRetries = config.maxRetries || 60;
  }

  private async createTask(task: CapSolverTask): Promise<CapSolverResponse> {
    console.log('Creating CapSolver task:', JSON.stringify(task, null, 2));

    const response = await fetch(`${this.baseUrl}/createTask`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientKey: this.apiKey,
        task,
      }),
    });

    const data: CapSolverResponse = await response.json();
    console.log(
      'CapSolver createTask response:',
      JSON.stringify(data, null, 2),
    );

    if (data.errorId !== 0) {
      throw new Error(
        `CapSolver createTask error: ${data.errorDescription || 'Unknown error'}`,
      );
    }

    return data;
  }

  private async getTaskResult(taskId: string): Promise<CapSolverResponse> {
    const response = await fetch(`${this.baseUrl}/getTaskResult`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientKey: this.apiKey,
        taskId,
      }),
    });

    const data: CapSolverResponse = await response.json();
    console.log(
      'CapSolver getTaskResult response:',
      JSON.stringify(data, null, 2),
    );

    if (data.errorId !== 0) {
      throw new Error(
        `CapSolver getTaskResult error: ${data.errorDescription || 'Unknown error'}`,
      );
    }

    return data;
  }

  private async waitForResult(taskId: string): Promise<any> {
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      await new Promise((resolve) => setTimeout(resolve, this.pollingInterval));

      const result = await this.getTaskResult(taskId);

      if (result.status === 'ready' && result.solution) {
        return result.solution;
      }

      if (result.status === 'failed') {
        throw new Error(
          `Task failed: ${result.errorDescription || 'Unknown error'}`,
        );
      }

      // Continue polling if status is 'processing' or 'idle'
    }

    throw new Error(
      `Task timed out after ${(this.maxRetries * this.pollingInterval) / 1000} seconds`,
    );
  }

  private formatProxy(proxy?: ProxyConfig): string | undefined {
    if (!proxy) return undefined;

    const { proxyAddress, proxyPort, proxyLogin, proxyPassword } = proxy;
    if (!proxyAddress || !proxyPort) return undefined;

    if (proxyLogin && proxyPassword) {
      return `${proxyAddress}:${proxyPort}:${proxyLogin}:${proxyPassword}`;
    }

    return `${proxyAddress}:${proxyPort}`;
  }

  // Enhanced Image Recognition Tasks
  async solveImageToText(
    imageData: string,
    options?: ImageToTextOptions
  ): Promise<ImageToTextResponse> {
    const { websiteURL, module = 'common', score, images } = options || {};

    const task: CapSolverTask = {
      type: 'ImageToTextTask',
      body: imageData,
      module,
    };

    // Add optional parameters
    if (websiteURL) task.websiteURL = websiteURL;
    if (score !== undefined) task.score = score;
    if (images && images.length > 0) {
      task.images = images;
      // Remove body when using images array
      delete task.body;
    }

    const response = await this.createTask(task);

    // ImageToTextTask returns results directly, no need for polling
    if (response.status === 'ready' && response.solution) {
      return {
        text: response.solution.text,
        answers: response.solution.answers,
      };
    }

    // Fallback to polling if needed
    const solution = await this.waitForResult(response.taskId!);
    return {
      text: solution.text,
      answers: solution.answers,
    };
  }

  async solveNormalCaptcha(
    imageData: string,
    options?: ImageToTextOptions
  ): Promise<{ data: string }> {
    const result = await this.solveImageToText(imageData, options);
    return { data: result.text };
  }

  // Batch image processing for number module
  async solveBatchNumbers(
    images: string[],
    options?: Omit<ImageToTextOptions, 'images'>
  ): Promise<{ answers: string[] }> {
    if (images.length > 9) {
      throw new Error('Maximum 9 images allowed for batch processing');
    }

    const result = await this.solveImageToText('', {
      ...options,
      module: 'number',
      images,
    });

    if (!result.answers) {
      throw new Error('No answers received from batch number processing');
    }

    return { answers: result.answers };
  }

  async solveTextCaptcha(textQuestion: string): Promise<{ data: string }> {
    const task: CapSolverTask = {
      type: 'TextTask',
      text: textQuestion,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { data: solution.text };
  }

  // reCAPTCHA Tasks
  async solveRecaptcha(
    pageurl: string,
    googlekey: string,
    userAgent: string,
    options?: {
      datas?: string;
      action?: string;
      enterprise?: boolean;
      invisible?: boolean;
      minScore?: number;
    },
  ): Promise<{ data: string }> {
    const { datas, action, enterprise, invisible, minScore } = options || {};

    const task: CapSolverTask = {
      type: 'ReCaptchaV2Task',
      websiteURL: pageurl,
      websiteKey: googlekey,
      userAgent,
    };

    if (datas) task.dataS = datas;
    if (action) task.action = action;
    if (enterprise) task.isInvisible = true;
    if (invisible) task.isInvisible = true;
    if (minScore) task.minScore = minScore;

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { data: solution.gRecaptchaResponse };
  }

  /**
   * Solve reCAPTCHA v2 image classification challenges
   * This method classifies objects in reCAPTCHA images instead of solving the full challenge
   *
   * @param image - Base64 encoded image string
   * @param question - Question type (e.g., "/m/0k4j" for cars, "/m/015qff" for traffic lights)
   * @param websiteURL - Optional website URL to improve accuracy
   * @param websiteKey - Optional website key to improve accuracy
   * @returns Classification result with type and identified objects
   */
  async solveRecaptchaV2Classification(
    image: string,
    question: string,
    websiteURL?: string,
    websiteKey?: string,
  ): Promise<{
    type: 'single' | 'multi';
    hasObject?: boolean;
    objects?: number[];
    size?: number;
  }> {
    const task: CapSolverTask = {
      type: 'ReCaptchaV2Classification',
      image,
      question,
    };

    if (websiteURL) task.websiteURL = websiteURL;
    if (websiteKey) task.websiteKey = websiteKey;

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return {
      type: solution.type,
      hasObject: solution.hasObject,
      objects: solution.objects,
      size: solution.size,
    };
  }

  async solveRecaptchaV3(
    pageurl: string,
    googlekey: string,
    userAgent: string,
    action: string = 'submit',
    minScore: number = 0.7,
  ): Promise<{ data: string }> {
    const task: CapSolverTask = {
      type: 'ReCaptchaV3Task',
      websiteURL: pageurl,
      websiteKey: googlekey,
      userAgent,
      action,
      minScore,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { data: solution.gRecaptchaResponse };
  }

  // hCaptcha Tasks
  async solveHCaptcha(
    pageurl: string,
    sitekey: string,
    userAgent: string,
  ): Promise<{ data: string }> {
    const task: CapSolverTask = {
      type: 'HCaptchaTask',
      websiteURL: pageurl,
      websiteKey: sitekey,
      userAgent,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { data: solution.gRecaptchaResponse };
  }

  // Cloudflare Turnstile
  async solveCloudflare(
    pageurl: string,
    sitekey: string,
  ): Promise<{ data: string }> {
    const task: CapSolverTask = {
      type: 'TurnstileTask',
      websiteURL: pageurl,
      websiteKey: sitekey,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { data: solution.token };
  }

  // Friendly Captcha
  async solveFriendlyCaptcha(
    pageurl: string,
    sitekey: string,
  ): Promise<{ data: string }> {
    const task: CapSolverTask = {
      type: 'FriendlyCaptchaTask',
      websiteURL: pageurl,
      websiteKey: sitekey,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { data: solution.token };
  }

  // GeeTest
  async solveGeeTest(
    pageurl: string,
    gt: string,
    challenge: string,
  ): Promise<{
    geetest_challenge: string;
    geetest_validate: string;
    geetest_seccode: string;
  }> {
    const task: CapSolverTask = {
      type: 'GeeTestTask',
      websiteURL: pageurl,
      gt,
      challenge,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return {
      geetest_challenge: solution.challenge,
      geetest_validate: solution.validate,
      geetest_seccode: solution.seccode,
    };
  }

  // MTCaptcha
  async solveMTCaptcha(
    pageurl: string,
    sitekey: string,
  ): Promise<{ token: string }> {
    const task: CapSolverTask = {
      type: 'MTCaptchaTask',
      websiteURL: pageurl,
      websiteKey: sitekey,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { token: solution.token };
  }

  // DataDome
  async solveDataDome(
    captchaUrl: string,
    userAgent: string,
    proxy?: ProxyConfig,
  ): Promise<{ cookie: string }> {
    const task: CapSolverTask = {
      type: 'DatadomeSliderTask',
      captchaURL: captchaUrl,
      userAgent,
    };

    const proxyString = this.formatProxy(proxy);
    if (proxyString) {
      task.proxy = proxyString;
    }

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { cookie: solution.cookie };
  }

  // AWS WAF
  async solveAmazonWAF({
    website,
    captchaScript,
    challengeScript,
    context,
    iv,
    key,
  }: {
    website: string;
    captchaScript: string;
    challengeScript: string;
    context: string;
    iv: string;
    key: string;
  }): Promise<{
    data: {
      captcha_voucher: string;
      existing_token: string;
    };
  }> {
    const task: CapSolverTask = {
      type: 'AwsWafTask',
      websiteURL: website,
      awsKey: key,
      awsIv: iv,
      awsContext: context,
      awsCaptchaScript: captchaScript,
      awsChallengeScript: challengeScript,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return {
      data: {
        captcha_voucher: solution.captchaVoucher,
        existing_token: solution.existingToken,
      },
    };
  }

  // Vision Engine
  async solveVisionEngine(
    imageData: string,
    question: string,
  ): Promise<{ data: string }> {
    const task: CapSolverTask = {
      type: 'VisionEngineTask',
      image: imageData,
      question,
    };

    const response = await this.createTask(task);
    const solution = await this.waitForResult(response.taskId!);

    return { data: solution.text };
  }

  // Get Balance
  async getBalance(): Promise<{ balance: number }> {
    const response = await fetch(`${this.baseUrl}/getBalance`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientKey: this.apiKey,
      }),
    });

    const data = await response.json();

    if (data.errorId !== 0) {
      throw new Error(
        `CapSolver getBalance error: ${data.errorDescription || 'Unknown error'}`,
      );
    }

    return { balance: data.balance };
  }
}

// Default configuration
const defaultApiKey = process.env.CAPSOLVER_API_KEY || 'your-api-key-here';
const defaultClient = new CapSolverClient({ apiKey: defaultApiKey });

// Export individual functions for backward compatibility
export const solveHCaptcha = (
  pageurl: string,
  sitekey: string,
  userAgent: string,
) => defaultClient.solveHCaptcha(pageurl, sitekey, userAgent);

export const solveRecaptcha = (
  pageurl: string,
  googlekey: string,
  userAgent: string,
  options?: {
    datas?: string;
    action?: string;
    enterprise?: boolean;
    invisible?: boolean;
    minScore?: number;
  },
) => defaultClient.solveRecaptcha(pageurl, googlekey, userAgent, options);

export const solveRecaptchaV3 = (
  pageurl: string,
  googlekey: string,
  userAgent: string,
  action: string = 'submit',
  minScore: number = 0.7,
) =>
  defaultClient.solveRecaptchaV3(
    pageurl,
    googlekey,
    userAgent,
    action,
    minScore,
  );

export const solveRecaptchaV2Classification = (
  image: string,
  question: string,
  websiteURL?: string,
  websiteKey?: string,
) => defaultClient.solveRecaptchaV2Classification(image, question, websiteURL, websiteKey);

export const solveCloudflare = (pageurl: string, sitekey: string) =>
  defaultClient.solveCloudflare(pageurl, sitekey);

export const solveFriendlyCaptcha = (pageurl: string, sitekey: string) =>
  defaultClient.solveFriendlyCaptcha(pageurl, sitekey);

export const solveImageToText = (
  imageData: string,
  options?: ImageToTextOptions
) => defaultClient.solveImageToText(imageData, options);

export const solveNormalCaptcha = (
  imageData: string,
  options?: ImageToTextOptions
) => defaultClient.solveNormalCaptcha(imageData, options);

export const solveBatchNumbers = (
  images: string[],
  options?: Omit<ImageToTextOptions, 'images'>
) => defaultClient.solveBatchNumbers(images, options);

export const solveTextCaptcha = (textQuestion: string) =>
  defaultClient.solveTextCaptcha(textQuestion);

export const solveGeeTest = (pageurl: string, gt: string, challenge: string) =>
  defaultClient.solveGeeTest(pageurl, gt, challenge);

export const solveAmazonWAF = ({
  website,
  captchaScript,
  challengeScript,
  context,
  iv,
  key,
}: {
  website: string;
  captchaScript: string;
  challengeScript: string;
  context: string;
  iv: string;
  key: string;
}) =>
  defaultClient.solveAmazonWAF({
    website,
    captchaScript,
    challengeScript,
    context,
    iv,
    key,
  });

export const solveDataDome = (
  captchaUrl: string,
  userAgent: string,
  proxy?: ProxyConfig,
) => defaultClient.solveDataDome(captchaUrl, userAgent, proxy);

export const solveMTCaptcha = (pageurl: string, sitekey: string) =>
  defaultClient.solveMTCaptcha(pageurl, sitekey);

export const solveVisionEngine = (imageData: string, question: string) =>
  defaultClient.solveVisionEngine(imageData, question);

export const getBalance = () => defaultClient.getBalance();

// Export the client class for advanced usage
export { CapSolverClient };
export type { CapSolverConfig, ProxyConfig, ImageToTextOptions, ImageToTextResponse };

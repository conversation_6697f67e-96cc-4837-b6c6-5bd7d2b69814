import { ISharedFleetPutPayload } from '../types.js';

export class EnterpriseAPI {
  constructor(
    private baseURL?: string,
    private token?: string,
  ) {}

  public async saveEnterpriseMetrics(
    stats: ISharedFleetPutPayload,
  ): Promise<boolean> {
    if (this.baseURL && this.token) {
      return fetch(new URL('/v2/enterprise/usage', this.baseURL).href, {
        method: 'PUT',
        body: JSON.stringify(stats),
        headers: {
          Authorization: this.token,
          'Content-Type': 'application/json',
        },
      }).then(async (r) => {
        if (r.ok) {
          return r.ok;
        }
        throw new Error(`${r.status}: ${r.statusText}`);
      });
    }
    return Promise.resolve(true);
  }
}

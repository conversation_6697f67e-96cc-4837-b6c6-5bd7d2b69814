// Files here are for SDK purposes to turn off certain parts of browserless
// and should not necessarily be imported into other modules.
import { BrowserlessRoutes } from '@browserless.io/browserless';
import { BrowserlessEnterpriseRoutes } from '../types.js';

export default [
  // OpenSource Routes
  BrowserlessRoutes.ConfigGetRoute,
  BrowserlessRoutes.SessionsGetRoute,
  BrowserlessRoutes.MetricsGetRoute,
  BrowserlessRoutes.MetricsTotalGetRoute,
  BrowserlessRoutes.PressureGetRoute,

  // Enterprise Routes
  BrowserlessEnterpriseRoutes.EnterpriseTokenDeleteRoute,
  BrowserlessEnterpriseRoutes.EnterpriseTokenGetRoute,
  BrowserlessEnterpriseRoutes.EnterpriseTokenPostRoute,
];

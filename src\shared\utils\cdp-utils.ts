import {
  getBoundingRect,
  selectorIsInteractable,
} from '../browserql/browser.utils.js';

import { getRandomNegativeInt, Logger } from '@browserless.io/browserless';
import { NodeWebSocketTransport } from 'puppeteer-core/lib/esm/puppeteer/node/NodeWebSocketTransport.js';
import WebSocket from 'ws';
import type { ProtocolMapping } from 'devtools-protocol/types/protocol-mapping.js';
import { Connection } from 'puppeteer-core';
import { BrowserQLAPI } from '../browserql/browser-api.js';
import { match, parse } from '../browserql/deep-selector.js';
import { getRandomArbitrary, isMatch } from '../../utils.js';
import { getDebuggerURL } from '../browserql/utils.js';
import {
  logCaptchaSolveError,
  logCaptchaSolveFailure,
  logCaptchaSolveSuccess,
} from '../browserql/utils/url-logging.js';
import { logCaptchaSolveAttempt } from '../browserql/utils/url-logging.js';
import { SharedConfig } from '../../config.js';

/**
 * Extracts captcha subtype from detailed captcha type strings
 * Provides granular categorization for better analytics
 */
export const extractCaptchaSubtype = (
  captchaType: string,
): string | undefined => {
  const lowerType = captchaType.toLowerCase();

  // reCAPTCHA variants
  if (lowerType.includes('recaptcha')) {
    if (lowerType.includes('v3') && lowerType.includes('enterprise')) {
      return 'v3-enterprise';
    }
    if (lowerType.includes('v3')) {
      return 'v3';
    }
    if (lowerType.includes('invisible') && lowerType.includes('enterprise')) {
      return 'invisible-enterprise';
    }
    if (lowerType.includes('invisible')) {
      return 'invisible';
    }
    if (lowerType.includes('enterprise')) {
      return 'v2-enterprise';
    }
    return 'v2-checkbox';
  }

  // Cloudflare variants
  if (lowerType.includes('cloudflare')) {
    if (lowerType.includes('turnstile')) {
      return 'turnstile';
    }
    return 'challenge';
  }

  // For other types, return undefined for now (can be expanded later)
  // Future subtypes could include:
  // - hCaptcha: 'standard', 'invisible', 'enterprise'
  // - Normal: 'image', 'math', 'text'
  // - GeeTest: 'v3', 'v4'
  return undefined;
};

export const sendWSMessage = (
  ws: WebSocket,
  message: string,
): Promise<void> => {
  return new Promise((resolve, reject) => {
    ws.send(message, (err) => {
      if (err) {
        return reject(err);
      }
      resolve();
    });
  });
};

export function sendCDPMessage<T extends keyof ProtocolMapping.Commands>(
  ws: WebSocket,
  method: T,
  id: number,
  params: unknown,
  sessionId?: string,
  timeout: number = 30000, // Default 30 second timeout
): Promise<ProtocolMapping.Commands[T]['returnType']> {
  const stringifiedMessage = JSON.stringify({
    id,
    method,
    params,
    sessionId,
  });
  return new Promise((resolve, reject) => {
    // Declare messageHandler function first
    const messageHandler = function (text: WebSocket.RawData) {
      const response = JSON.parse(text.toString());
      if (response.id === id) {
        clearTimeout(timeoutId);
        ws.removeListener('message', messageHandler);
        if (response.error) {
          return reject(response.error.message);
        }
        resolve(response.result);
      }
    };

    // Add timeout to prevent hanging
    const timeoutId = setTimeout(() => {
      ws.removeListener('message', messageHandler);
      reject(
        new Error(
          `CDP message timeout after ${timeout}ms for method: ${method}`,
        ),
      );
    }, timeout);

    sendWSMessage(ws, stringifiedMessage).catch((error) => {
      clearTimeout(timeoutId);
      reject(error);
    });

    ws.on('message', messageHandler);
  });
}

export const evalInBrowser = async <T>({
  browser,
  sessionId,
  expression,
  logger,
}: {
  browser: WebSocket | Connection;
  sessionId?: string;
  expression: string;
  logger: Logger;
}): Promise<{
  result: T;
  error: string | null;
}> => {
  logger.debug('evalInBrowser', {
    sessionId,
    expression,
  });
  if (browser instanceof Connection) {
    const evaluateResponse = await browser.send('Runtime.evaluate', {
      returnByValue: true,
      awaitPromise: true,
      expression,
    });
    return {
      result: evaluateResponse.result.value,
      error: evaluateResponse?.exceptionDetails?.text || null,
    };
  }
  const evaluateResponse = await sendCDPMessage(
    browser!,
    'Runtime.evaluate',
    getRandomNegativeInt(),
    {
      returnByValue: true,
      awaitPromise: true,
      expression,
    },
    sessionId,
  );
  return {
    result: evaluateResponse.result.value,
    error: evaluateResponse?.exceptionDetails?.text || null,
  };
};

export const waitForSelector = async ({
  selector,
  timeout,
  visible,
  browser,
  sessionId,
  checkInteractable,
  logger,
  browserAPI,
}: {
  selector: string;
  timeout?: number;
  visible: boolean;
  browser: WebSocket | Connection;
  sessionId?: string;
  logger: Logger;
  checkInteractable?: boolean;
  browserAPI?: BrowserQLAPI;
}): Promise<{
  x: number;
  y: number;
  width: number;
  height: number;
  cdp: WebSocket;
}> => {
  logger.debug('waitForSelector', {
    selector,
    timeout,
    visible,
  });
  const timers = [];
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(
        new Error(
          `Timed out waiting for selector "${selector}" after ${timeout}ms`,
        ),
      );
    }, timeout);
    timers.push(timer);

    const checkForSelector = async (): Promise<unknown> => {
      let res;
      // FIXME: This should be a fn.bind, but for some reason it's not working
      if (browserAPI && 'getBoundingClientRect' in browserAPI) {
        res = await browserAPI.getBoundingClientRect({
          selector,
          visible,
        });
      } else {
        res = await getBoundingClientRect({
          browser,
          sessionId,
          selector,
          visible,
          logger,
        }).catch((e) => e);
      }
      if (res instanceof Error) {
        clearTimeout(timer);
        return reject(res);
      }
      if (res === null) {
        return timers.push(setTimeout(checkForSelector, 250));
      }
      if (visible && (res.width <= 0 || res.height <= 0)) {
        return timers.push(setTimeout(checkForSelector, 100));
      }

      if (checkInteractable) {
        const escaped = selector.replace(/\"/gi, '\\"');
        const { result: interactable } = await evalInBrowser({
          browser,
          sessionId,
          expression: `(${selectorIsInteractable.toString()})("${escaped}")`,
          logger,
        });
        if (!interactable) {
          return timers.push(setTimeout(checkForSelector, 100));
        }
      }

      return resolve({
        cdp: res.cdp || browser,
        y: res.y,
        x: res.x,
        width: res.width,
        height: res.height,
      });
    };

    checkForSelector();
  });
};

export const getBoundingClientRect = async ({
  browser,
  selector,
  visible,
  sessionId,
  logger,
}: {
  browser: WebSocket | Connection;
  sessionId?: string;
  selector: string;
  visible: boolean;
  logger: Logger;
}): Promise<{
  x: number;
  y: number;
  width: number;
  height: number;
  cdp: WebSocket | Connection;
} | null> => {
  const escaped = selector.replace(/\"/gi, '\\"') || '';

  const evaluateResponse = await evalInBrowser<string>({
    browser,
    sessionId,
    expression: `(${getBoundingRect.toString()})("${escaped}", ${visible})`,
    logger,
  });

  logger.trace('getBoundingClientRect', evaluateResponse);

  const val = JSON.parse(evaluateResponse.result);
  if (!val) {
    return null;
  }
  const { top, left, width, height } = val;
  return {
    cdp: browser!,
    y: top,
    x: left,
    width,
    height,
  };
};

export const getDeepClientRect = async ({
  browser,
  selector,
  sessionId,
  port,
}: {
  browser: WebSocket;
  selector: string;
  sessionId?: string;
  port: number;
}): Promise<{
  x: number;
  y: number;
  width: number;
  height: number;
  cdp: WebSocket;
} | null> => {
  const query = parse(selector);

  const urlPattern = query.urlPattern;

  const { targetInfos } = await sendCDPMessage(
    browser,
    'Target.getTargets',
    getRandomNegativeInt(),
    {},
    sessionId,
  );
  const t = targetInfos.reverse().find((t) => isMatch(t.url, urlPattern!));

  if (!t) {
    return null;
  }

  const wsURL = getDebuggerURL(t.targetId, port as any);
  const connection = await new Connection(
    wsURL,
    await NodeWebSocketTransport.create(wsURL),
  );

  await connection.send('DOM.enable');
  const { nodes } = await connection.send('DOM.getFlattenedDocument', {
    pierce: true,
    depth: -1,
  });

  const node = match(query, nodes);
  if (!node) {
    connection.dispose();
    return null;
  }
  const box = await connection
    .send('DOM.getBoxModel', {
      nodeId: node.nodeId,
      backendNodeId: node.backendNodeId,
    })
    .catch(() => null);

  if (!box) {
    connection.dispose();
    return box;
  }

  // Get the parent node
  await sendCDPMessage(
    browser,
    'DOM.enable',
    getRandomNegativeInt(),
    {},
    sessionId,
  );

  const a = await sendCDPMessage(
    browser,
    'DOM.getFlattenedDocument',
    getRandomNegativeInt(),
    {
      pierce: true,
      depth: -1,
    },
    sessionId,
  );
  const parentNode = a.nodes.find((n) => n.frameId === t.targetId);
  const parentBox = await sendCDPMessage(
    browser,
    'DOM.getBoxModel',
    getRandomNegativeInt(),
    {
      nodeId: parentNode!.nodeId,
      backendNodeId: parentNode!.backendNodeId,
    },
    sessionId,
  );

  const xOffset = getRandomArbitrary(7, 14);
  const yOffset = getRandomArbitrary(14, 21);

  return {
    cdp: connection as any,
    // Sum the x and y of the parent and child
    x: box.model.content[0] + parentBox.model.content[0] + xOffset,
    y: box.model.content[1] + parentBox.model.content[1] + yOffset,
    width: box.model.width,
    height: box.model.height,
  };
};

export const captchaLoggerFactory = (
  apiKey: string,
  captchaType: string,
  url: string,
  logger: Logger,
  config: SharedConfig,
  endpointType: 'baas' | 'bql',
  captchaSubtype?: string,
) => {
  return {
    attempt: () => {
      logCaptchaSolveAttempt({
        token: apiKey,
        captchaType,
        captchaSubtype,
        endpointType,
        config,
        logger,
        url,
      }).catch((error) => {
        logger.error('Error logging captcha solve attempt:', error);
      });
    },
    success: () => {
      logCaptchaSolveSuccess({
        token: apiKey,
        captchaType,
        captchaSubtype,
        endpointType,
        config,
        logger,
        url,
      }).catch((error) => {
        logger.error('Error logging captcha solve success:', error);
      });
    },
    error: (message: string) => {
      logCaptchaSolveError({
        token: apiKey,
        captchaType,
        captchaSubtype,
        endpointType,
        errorMessage: message,
        config,
        logger,
        url,
      }).catch((error) => {
        logger.error('Error logging captcha solve error:', error);
      });
    },
    failure: () => {
      logCaptchaSolveFailure({
        token: apiKey,
        captchaType,
        captchaSubtype,
        endpointType,
        config,
        logger,
        url,
      }).catch((error) => {
        logger.error('Error logging captcha solve failure:', error);
      });
    },
  };
};

export const setDownloadBehavior = async ({
  browser,
  sessionId,
  behavior,
  downloadPath,
}: {
  browser: WebSocket;
  sessionId?: string;
  behavior: 'allow' | 'deny' | 'allowAndName';
  downloadPath: string;
}) => {
  try {
    await sendCDPMessage(
      browser,
      'Browser.setDownloadBehavior',
      getRandomNegativeInt(),
      {
        behavior: behavior,
        downloadPath: downloadPath,
        eventsEnabled: true,
      },
      sessionId,
    );
    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Shared function for detecting Cloudflare captcha type and subtype
 * This function is used by both BQL and CDP implementations via fn.toString()
 */
export const detectCloudflareType = () => {
  // First check if this is a solvable challenge page using ray-id logic
  const hasChallengeResources =
    window.performance &&
    window.performance
      .getEntriesByType('resource')
      .filter((e: any) => e.name.includes('challenges.cloudflare.com')).length >
      0;
  const hasRayId = document.querySelector('.ray-id');

  // Only proceed if it's a solvable challenge page (has both challenges.cloudflare.com and .ray-id)
  if (hasChallengeResources && hasRayId) {
    // This is a solvable challenge page (interactive or JS challenge)
    return {
      type: 'challenge',
      hasRayId: true,
    };
  }

  // If no ray-id but has Turnstile elements, classify for logging but don't trigger solving
  const turnstileInput = document.querySelector(
    'input[name="cf-turnstile-response"]',
  );
  const turnstileWidget = document.querySelector('.cf-turnstile[data-sitekey]');

  if (turnstileInput || turnstileWidget) {
    const sitekey = turnstileWidget?.getAttribute('data-sitekey');
    const theme = turnstileWidget?.getAttribute('data-theme') || 'auto';
    const size = turnstileWidget?.getAttribute('data-size') || 'normal';
    return {
      type: 'turnstile',
      sitekey,
      theme,
      size,
      visible: size !== 'invisible',
      hasRayId: false,
    };
  }

  return null;
};

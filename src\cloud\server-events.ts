import EventSource from 'eventsource';

export class ServerEvents extends EventSource {
  constructor(url: string, authToken: string) {
    super(url, { headers: { Authorization: authToken } });

    this.addEventListener('open', () => {
      console.log('Server Events connection established');
    });

    this.addEventListener('error', (e) => {
      if (e.eventPhase === EventSource.CLOSED) {
        console.log('Server Events connection closed');
      } else {
        console.log('Server Events error', e);
      }
    });
  }
}

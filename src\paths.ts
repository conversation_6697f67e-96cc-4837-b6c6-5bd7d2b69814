export enum EnterpriseRoutes {
  // Extensions
  extension = '/extension',

  // Tokens
  token = '/token',
  tokenById = '/token/*',
  tokens = '/tokens',

  // Stealth Browser WS
  stealth = '/stealth',
  chromiumStealth = '/chromium/stealth',
  chromeStealth = '/chrome/stealth',

  // Unblock APIs
  unblock = '/unblock',
  chromiumUnblock = '/chromium/unblock',
  chromeUnblock = '/chrome/unblock',

  // Export APIs
  export = '/export',
  chromiumExport = '/chromium/export',
  chromeExport = '/chrome/export',

  // Live URL Handling
  live = '/live/*',
  chromiumLive = '/chromium/live/*',
  chromeLive = '/chrome/live/*',

  // Reconnection
  reconnect = '/reconnect/*',

  // Create
  chromiumStealthPost = '/chromium/stealth',

  // BrowserQL Requests
  chromiumBQL = '/chromium/bql?(/*)',
  chromeBQL = '/chrome/bql?(/*)',

  // Session Management
  sessionById = '/session/*',
  session = '/session',
  sessionConnect = '/session/connect/*',
}

import micromatch from 'micromatch';
import { Protocol } from 'puppeteer-core';
import { isDeepSelector } from './constants.js';
import { isMatch } from '../../utils.js';

// Helper function to convert attribute array to object
const dupleToObject = (arr: string[]) =>
  arr.reduce(
    (acc, key, idx, src) => {
      if (idx % 2 === 0) {
        const val = src[idx + 1];
        acc[key] = val === '' ? true : val;
      }
      return acc;
    },
    {} as Record<string, string | true>,
  );

type ParseResult = {
  urlPattern: string | null;
  selectors: Array<{
    tag: string | null;
    id: string | null;
    classes: string[];
    attributes: Record<string, string | boolean>;
  }>;
};

// Helper function to parse a single selector part
function parseSelectorPart(selector: string) {
  const result = {
    tag: null as string | null,
    id: null as string | null,
    classes: [] as string[],
    attributes: {} as Record<string, string | boolean>,
  };

  // Match the tag name (e.g., "input", "button")
  const tagMatch = selector.match(/^[a-zA-Z][a-zA-Z0-9-]*/);
  result.tag = tagMatch ? tagMatch[0] : null;

  // Match the ID (e.g., "#foo")
  const idMatch = selector.match(/#([a-zA-Z0-9_-]+)/);
  result.id = idMatch ? idMatch[1] : null;

  // Match classes (e.g., ".active.bar")
  const classMatches = [...selector.matchAll(/\.([a-zA-Z0-9_-]+)/g)];
  result.classes = classMatches.map((match) => match[1]);

  // Match attributes (e.g., `[data-test-id="wow"]`)
  const attributeMatches = [
    ...selector.matchAll(/\[([\^\*\$a-zA-Z0-9_-]+)(?:="([^"]*)")?\]/gi),
  ];

  for (const match of attributeMatches) {
    const attrName = match[1];
    const attrValue = match[2] || true;
    result.attributes[attrName.replace(/\^|\$|\*/gi, '')] = attrName.includes(
      '$',
    )
      ? `*${attrValue}`
      : attrName.includes('^')
        ? `${attrValue}*`
        : attrName.includes('*')
          ? `*${attrValue}*`
          : attrValue;
  }

  return result;
}

export function parse(selector: string): ParseResult {
  // Allow more CSS combinators and pseudo-classes while maintaining security
  const allowedCombinators = [' ', '>', '+', '~'];
  // Only disallow potentially dangerous characters that could lead to XSS or injection
  const disallowedCharacters = ['!', '@', '&', 'javascript:', 'vbscript:'];

  if (!isDeepSelector(selector)) {
    throw new Error('Deep selectors must start with a "<" symbol');
  }

  // Remove the initial '< ' and split by allowed combinators
  const parts = selector
    .replace('< ', '')
    .split(new RegExp(`[${allowedCombinators.join('')}]`));

  // Extract URL pattern if present (first part should be a URL pattern, not a CSS selector)
  let urlPattern: string | null = null;
  let selectorParts: string[] = parts;

  // A URL pattern should contain typical URL characters like protocol, domain patterns, etc.
  // Only treat the first part as a URL pattern if it looks like a URL (contains :// or domain-like patterns)
  // and there are additional parts that would be the actual selectors
  if (
    parts.length > 1 &&
    (parts[0].includes('://') ||
      (parts[0].includes('*') && !parts[0].startsWith('.')))
  ) {
    urlPattern = parts[0];
    selectorParts = parts.slice(1);
  }

  // Validate each selector part for security
  for (const part of selectorParts) {
    // Split the selector into base part and pseudo-classes
    const [basePart, ...pseudoParts] = part.split(/(?=::?[a-zA-Z-])/);

    // Split the base part into individual selector components
    const components = basePart.split(/(?=[.#])/);

    // Validate each component
    for (const component of components) {
      // Skip empty components
      if (!component) continue;

      // Check if it's a valid tag name, class, or ID
      const isValidComponent =
        /^[a-zA-Z][a-zA-Z0-9-]*$|^\.[a-zA-Z0-9_-]+$|^#[a-zA-Z0-9_-]+$/.test(
          component,
        );

      if (!isValidComponent) {
        // Check for disallowed characters only if the component isn't a valid selector part
        if (
          disallowedCharacters.some((char) =>
            component.toLowerCase().includes(char),
          )
        ) {
          throw new Error(
            `Un-allowed character found in selector part "${component}". Cannot use potentially dangerous characters: ${disallowedCharacters.join(', ')}`,
          );
        }
      }
    }

    // Then validate each pseudo-class
    for (const pseudo of pseudoParts) {
      // Allow common pseudo-classes and pseudo-elements, including those with parameters
      const allowedPseudo =
        /^::?(?:active|after|before|checked|disabled|empty|enabled|first-child|first-of-type|focus|hover|last-child|last-of-type|link|not|nth-child\(\d+\)|nth-last-child\(\d+\)|nth-last-of-type\(\d+\)|nth-of-type\(\d+\)|only-child|only-of-type|root|target|visited)$/;

      // Special case for nth-child and similar with parameters
      const pseudoName = pseudo.split('(')[0];
      const allowedPseudoNames = [
        'nth-child',
        'nth-last-child',
        'nth-last-of-type',
        'nth-of-type',
        'not',
      ];

      if (allowedPseudoNames.includes(pseudoName)) {
        // Validate the parameter is a simple number
        const param = pseudo.match(/\((.*)\)/)?.[1];
        if (param && /^\d+$/.test(param.trim())) {
          continue; // Valid nth-child with number parameter
        }
      }

      if (!allowedPseudo.test(pseudo)) {
        throw new Error(
          `Un-allowed pseudo-class/element found in selector: "${pseudo}"`,
        );
      }
    }
  }

  // Parse each selector part (normalize quotes first)
  const selectors = selectorParts.map((part) =>
    parseSelectorPart(part.replace(/\`|\'/gi, '"')),
  );

  return {
    urlPattern,
    selectors,
  };
}

export function match(query: ParseResult, nodes: Protocol.DOM.Node[]) {
  const isIframeContext =
    query.urlPattern && query.urlPattern.includes('iframe');

  // Helper function to match a single node against a selector
  function matchNode(
    node: Protocol.DOM.Node,
    selector: ParseResult['selectors'][0],
  ): boolean {
    if (selector.tag && selector.tag !== node.nodeName.toLocaleLowerCase()) {
      return false;
    }

    if (
      !node.attributes &&
      (selector.id ||
        selector.classes.length ||
        Object.keys(selector.attributes).length)
    ) {
      return false;
    }

    const attributeHash = dupleToObject(node.attributes ?? []);

    if (selector.id && attributeHash.id !== selector.id) {
      return false;
    }

    if (
      selector.classes.length &&
      typeof attributeHash.class === 'string' &&
      !selector.classes.every((c) =>
        (attributeHash.class as string).includes(c),
      )
    ) {
      return false;
    }

    if (
      Object.keys(selector.attributes).length &&
      !Object.entries(selector.attributes).every(([key, value]) => {
        const val = attributeHash[key];
        if (val === undefined) {
          return false;
        }

        if (typeof val === 'boolean' || typeof value === 'boolean') {
          return val === value;
        }

        return micromatch.isMatch(val, value, { bash: true });
      })
    ) {
      return false;
    }

    return true;
  }

  // Helper function to find a node that matches all selectors in sequence
  function findMatchingNode(
    currentNodes: Protocol.DOM.Node[],
    selectorIndex: number,
  ): Protocol.DOM.Node | null {
    if (selectorIndex >= query.selectors.length) {
      return null;
    }

    const currentSelector = query.selectors[selectorIndex];

    for (const node of currentNodes) {
      if (matchNode(node, currentSelector)) {
        if (selectorIndex === query.selectors.length - 1) {
          // This is the last selector, check iframe context if needed
          if (isIframeContext && query.urlPattern) {
            const parentFrame = findParentFrame(node, nodes);
            if (parentFrame) {
              const srcAttr = parentFrame.attributes?.find((attr) => {
                const [name] = attr.split('=');
                return name === 'src';
              });
              if (srcAttr) {
                const [, value] = srcAttr.split('=');
                if (!isMatch(value || '', query.urlPattern)) {
                  continue;
                }
              }
            }
          }
          return node;
        }

        // Get child nodes for next selector
        const childNodes = nodes.filter((n) => n.parentId === node.nodeId);
        const nextMatch = findMatchingNode(childNodes, selectorIndex + 1);
        if (nextMatch) {
          return nextMatch;
        }
      }
    }

    return null;
  }

  return findMatchingNode(nodes, 0);
}

// Helper function to find parent iframe of a node
function findParentFrame(
  node: Protocol.DOM.Node,
  nodes: Protocol.DOM.Node[],
): Protocol.DOM.Node | null {
  let current = node;
  while (current.parentId) {
    const parent = nodes.find((n) => n.nodeId === current.parentId);
    if (!parent) break;
    if (parent.nodeName.toLowerCase() === 'iframe') {
      return parent;
    }
    current = parent;
  }
  return null;
}

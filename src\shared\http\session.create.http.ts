import { ServerResponse } from 'http';
import {
  contentTypes,
  Methods,
  APITags,
  dedent,
  Logger,
  jsonResponse,
  BadRequest,
  getTokenFromRequest,
  makeExternalURL,
  HTTPRoute,
} from '@browserless.io/browserless';
import { EnterpriseRoutes } from '../../paths.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';
import { AugmentedRequest } from '../../types.js';
import { SharedConfig } from '../../config.js';
import { randomID } from '../../utils.js';
import { FileDatabase } from '../../file-system.js';
import { getBooleanRequestParameter } from '../../utils.js';

export interface BodySchema {
  /**
   * The time-to-live (TTL) for the session in milliseconds.
   */
  ttl: number;

  /**
   * Whether or not to enable advanced stealth mode. Defaults to false.
   */
  stealth?: boolean;

  /**
   * Whether or not to enable ad-blocking. Defaults to false.
   */
  blockAds?: boolean;

  /**
   * Whether the browser should be launched in headless mode.
   * Ignored if `stealth` is true, defaults to "true".
   */
  headless?: boolean;

  /**
   * An array of command-line arguments to pass to the browser.
   * Defaults to an empty array.
   */
  args?: string[];

  /**
   * The type of browser to use for the session.
   * Currently, only 'chrome' and 'chromium' are supported. Defaults to 'chromium'.
   */
  browser?: 'chrome' | 'chromium';

  /**
   * The underlying page URL you're attempting to automate. Some pages may
   * required special handling in order to work correctly or unblock.
   * This is not required, but can be useful or required for certain sites.
   */
  url?: string;

  /**
   * Proxy Parameters for the session if desired.
   * If not specified, the session will use the default network settings.
   */
  proxy?: {
    /**
     * The type of proxy to use, currently only 'residential' is supported.
     */
    type?: 'residential';

    /**
     * Whether or not to use the same IP address for all proxied requests. Defaults to true
     */
    sticky?: boolean;

    /**
     * The country to proxy through. Defaults to 'us' or United States
     */
    country?: string;

    /**
     * The city to proxy through.
     */
    city?: string;

    /**
     * The state to proxy through.
     */
    state?: string;
  };
}

export interface ResponseSchema {
  /**
   * The ID of the session
   */
  id: string;

  /**
   * The fully-qualified URL to connect CDP-based libraries to the session
   */
  connect: string;

  /**
   * The total time of life in milliseconds
   */
  ttl: number;

  /**
   * The fully qualified URL to stop and remove the session with a DELETE method.
   */
  stop: string;
}

export default class SessionCreateHTTPRoute extends HTTPRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseSessionCreateRoute;
  auth = true;
  accepts = [contentTypes.json];
  contentTypes = [contentTypes.json];
  concurrency = false;
  description = dedent(`
    > This API is only available for Enterprise and cloud plans. [Contact us for more information here.](https://www.browserless.io/contact/), or [sign-up here](https://www.browserless.io/pricing/).

    Creates a new browser session with the specified parameters. The session can be used for persistent browser connections
    with well-defined lifetimes and reconnection semantics.
  `);
  method = Methods.post;
  path = [EnterpriseRoutes.session];
  tags = [APITags.management];

  async handler(
    req: AugmentedRequest,
    res: ServerResponse,
    logger: Logger,
  ): Promise<void> {
    const body = req.body as BodySchema;
    const config = this.config() as SharedConfig;
    const fileDB = this.fileSystem() as FileDatabase;
    const token = getTokenFromRequest(req)!;
    const maxTTL = config.getMaxRetention(token);

    const { args = [], browser = 'chromium', proxy = {}, ttl, url } = body;

    if (typeof ttl !== 'number' || ttl < 0) {
      throw new BadRequest('ttl must be a non-negative number greater than 0');
    }

    if (ttl > maxTTL) {
      throw new BadRequest(
        `ttl cannot exceed the maximum retention time of ${maxTTL} milliseconds for your plan.`,
      );
    }

    if (args.some((arg) => arg.includes('--user-data-dir'))) {
      throw new BadRequest(
        'Argument "--user-data-dir" is not compatible with sessions.',
      );
    }

    const id = randomID(32);
    const blockAds = getBooleanRequestParameter(body.blockAds);
    const headless = getBooleanRequestParameter(body.headless);
    const stealth = getBooleanRequestParameter(body.stealth);

    await fileDB.createSession(id, {
      args,
      blockAds,
      browser,
      createdBy: token,
      headless,
      proxy,
      running: false,
      stealth,
      ttl,
      url,
    });

    // Generate connect and stop URLs
    const stopURL = new URL(
      makeExternalURL(config.getExternalAddress(), 'session', id),
    );
    const connectURL = new URL(
      makeExternalURL(
        config.getExternalWebSocketAddress(),
        'session',
        'connect',
        id,
      ),
    );

    connectURL.searchParams.set('token', token);
    stopURL.searchParams.set('token', token);

    if (
      proxy.type === 'residential' ||
      proxy.country ||
      proxy.state ||
      proxy.city
    ) {
      const sticky = proxy.sticky ?? true;
      connectURL.searchParams.set('proxy', 'residential');
      connectURL.searchParams.set('proxyCountry', proxy.country ?? 'us');

      if (proxy.city) {
        connectURL.searchParams.set('proxyCity', proxy.city);
      }

      if (proxy.state) {
        connectURL.searchParams.set('proxyState', proxy.state);
      }

      if (sticky) {
        connectURL.searchParams.set('proxySticky', sticky.toString());
      }
    }

    const connect = connectURL.href;
    const stop = stopURL.href;

    const response: ResponseSchema = {
      id,
      connect,
      ttl,
      stop,
    };

    logger.info(`Session ${id} created successfully`);
    return jsonResponse(res, 200, response);
  }
}

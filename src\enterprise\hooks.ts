import {
  Hooks,
  Request,
  getTokenFromRequest,
  writeR<PERSON>ponse,
  sleep,
  AfterResponse,
  PageHook,
  createLogger,
  BrowserHook,
} from '@browserless.io/browserless';
import { ConnectionStats, Server } from 'proxy-chain';
import micromatch from 'micromatch';

import {
  AugmentedRequest,
  BeforeHook,
  ISharedFleetPutPayload,
  SharedFleetStat,
} from '../types.js';
import {
  allowedCountries,
  buildBlessObject,
  convertMsToSeconds,
  getProxyUnitsUsed,
  isIntrospection,
} from '../utils.js';
import { EnterpriseAPI } from './api.js';
import { EnterpriseConfig } from './config.js';
import { Socket } from 'net';
import { ProxyingEngine } from '../shared/3rd-party/proxies/ProxyingEngine.js';

export default class EnterpriseHooks extends Hooks {
  protected log = createLogger('enterprise-hooks');
  protected callBasedSecondLimit = 30;
  protected currentUsageStats: ISharedFleetPutPayload = {};
  protected allowedProxyTypes = ['residential'];
  protected saveInterval: number = process.env.SAVE_INTERVAL
    ? +process.env.SAVE_INTERVAL
    : 5 * 60 * 1000;
  protected intervalId = setInterval(
    this.persistMetrics.bind(this),
    this.saveInterval,
  );
  protected statusToIndexMap = {
    successful: 1,
    timedout: 2,
    error: 3,
  };

  // https://en.wikipedia.org/wiki/Private_network
  // https://serverfault.com/questions/427018/what-is-this-ip-address-169-254-169-254
  protected blockedIPPatterns = [
    // Localhost/loopback/etc.
    'localhost',
    '127.',

    // https://www.iana.org/help/abuse-answers
    // Special-Use IPs
    '172.16.',
    '172.17.',
    '172.18.',
    '172.19.',
    '172.20.',
    '172.21.',
    '172.22.',
    '172.23.',
    '172.24.',
    '172.25.',
    '172.26.',
    '172.27.',
    '172.28.',
    '172.29.',
    '172.30.',
    '172.31.',

    // AWS and cloud-hosted IPs
    '169.254',
  ];

  protected blockedProtocols = ['file://', 'smtp://', 'ftp://'];

  protected urlDenyList = [
    ...this.blockedProtocols,
    ...this.blockedIPPatterns.flatMap((ip) => [
      `http://${ip}`,
      `https://${ip}`,
      `ws://${ip}`,
      `wss://${ip}`,
    ]),
  ];

  protected urlAllowList = [
    '/favicon.ico',
    '/function/index.html',
    '/function/client.js',
    '/function/favicon.',
    '/function/browserless-function-*.js',
  ];

  // These are not understood by downstream browserless core
  // and need to be remove prior to allowing the request to continue
  protected CLOUD_ONLY_QUERY_PARAMS = [
    'proxy',
    'proxyCountry',
    'proxySticky',
    'proxyCity',
    'proxyState',
  ];

  protected proxyingEngine: ProxyingEngine;

  constructor(
    protected config: EnterpriseConfig,
    protected api: EnterpriseAPI,
    protected proxyUsername?: string,
    protected proxyPassword?: string,
  ) {
    super();
    this.proxyingEngine = new ProxyingEngine(this.config);
  }

  protected async persistMetrics() {
    const start = Date.now();

    try {
      await this.api.saveEnterpriseMetrics(this.currentUsageStats);
      this.log(`Successfully saved usage in ${Date.now() - start}ms`);
      this.currentUsageStats = {};
    } catch (error: any) {
      this.log('Metrics failed to save', error.message);
      return;
    }
  }

  protected recordMetrics({
    token,
    status,
    seconds,
    captchas,
    proxy,
  }: {
    token: string;
    status?: AfterResponse['status'];
    seconds?: number;
    captchas?: number;
    proxy?: number;
  }) {
    const now = Date.now();

    // Initialize API-token if not present in stats payload
    this.currentUsageStats[token] =
      this.currentUsageStats[token] ??
      ([0, 0, 0, 0, 0, 0, 0, 0] as SharedFleetStat);

    // Set date
    this.currentUsageStats[token][4] = now;

    // Update status'
    if (status) {
      const statusIndex = this.statusToIndexMap[status];
      this.currentUsageStats[token][statusIndex]++;
    }

    // Update seconds used and units
    if (seconds !== undefined) {
      this.currentUsageStats[token][0] += seconds;
      this.currentUsageStats[token][5] += Math.ceil(
        seconds / this.callBasedSecondLimit,
      );
    }

    // Calculate units consumed for captchas a 10x multiple
    if (captchas !== undefined && captchas > 0) {
      this.currentUsageStats[token][7] += captchas * 10;
    }

    // Record proxy units consumed
    if (proxy) {
      this.currentUsageStats[token][6] += proxy;
    }

    this.log(`${token} current stats: ${this.currentUsageStats[token]}`);
  }

  protected logConnectionFailure(
    request: AugmentedRequest,
    writeable: any,
    statusCode: 400 | 401 | 404 | 408 | 429 | 500 | 200 | 204,
    errorMessage: string,
    errorType: string,
    errorDescription: string,
    _token?: string, // Unused parameter, prefixed with underscore
  ): boolean {
    writeResponse(writeable, statusCode, errorMessage);

    const url = request.url || `http://0.0.0.0${request.__bless__.originalUrl}`;
    const endpoint = new URL(url).pathname;

    this.log(
      `Connection failure: ${errorType} - ${errorDescription} for endpoint ${endpoint}`,
    );

    return false;
  }

  protected isURLAllowed = (path: string) =>
    this.urlAllowList.some((pattern) => micromatch.isMatch(path, pattern));

  async before({ req, res, socket }: BeforeHook): Promise<boolean> {
    const writeable = res || socket;
    const parsedURL = new URL(req.url || '', `http://${req.headers.host}`);
    let request = req as unknown as Request;
    request.parsed = parsedURL;
    request = await buildBlessObject(request);

    if (!writeable) {
      throw new Error(`Internal error: couldn't locate a writeable socket`);
    }

    // Handle Proxy stuff here
    if (parsedURL.searchParams.has('proxy')) {
      if (!this.proxyPassword || !this.proxyUsername) {
        return this.logConnectionFailure(
          req as unknown as AugmentedRequest,
          writeable,
          400,
          'No proxy password or user was set.',
          'MissingProxyCredentials',
          'Proxy username or password not configured',
          getTokenFromRequest(request) || undefined,
        );
      }

      if (parsedURL.searchParams.has('--proxy-server')) {
        return this.logConnectionFailure(
          req as unknown as AugmentedRequest,
          writeable,
          400,
          'Only one of "proxy" or "--proxy-server" are allowed.',
          'InvalidProxyConfig',
          'Multiple proxy configurations specified',
          getTokenFromRequest(request) || undefined,
        );
      }

      if (!this.allowedProxyTypes.includes(req.__bless__.proxyType)) {
        return this.logConnectionFailure(
          req as unknown as AugmentedRequest,
          writeable,
          400,
          `Only "${this.allowedProxyTypes.join(', ')}" are allowed.`,
          'InvalidProxyType',
          `Proxy type "${req.__bless__.proxyType}" is not allowed`,
          getTokenFromRequest(request) || undefined,
        );
      }

      if (req.__bless__.proxyCountry) {
        if (req.__bless__.proxyType !== 'residential') {
          return this.logConnectionFailure(
            req as unknown as AugmentedRequest,
            writeable,
            400,
            `'proxyCountry' is only allowed for residential proxying.`,
            'InvalidProxyConfig',
            'Country proxying only allowed for residential proxies',
            getTokenFromRequest(request) || undefined,
          );
        }

        if (
          !allowedCountries.includes(req.__bless__.proxyCountry.toLowerCase())
        ) {
          return this.logConnectionFailure(
            req as unknown as AugmentedRequest,
            writeable,
            400,
            `'proxyCountry' of "${req.__bless__.proxyCountry}" is not allowed.`,
            'InvalidProxyCountry',
            `Country "${req.__bless__.proxyCountry}" is not in allowed list`,
            getTokenFromRequest(request) || undefined,
          );
        }
      }

      if (req.__bless__.proxyCity && !this.config.allowCityProxying()) {
        return this.logConnectionFailure(
          req as unknown as AugmentedRequest,
          writeable,
          401,
          `Your plan doesn't support city-level proxying.`,
          'PlanLimitExceeded',
          'City-level proxying not supported by current plan',
          getTokenFromRequest(request) || undefined,
        );
      }

      if (req.__bless__.proxyState && !this.config.allowStateProxying()) {
        return this.logConnectionFailure(
          req as unknown as AugmentedRequest,
          writeable,
          401,
          `Your plan doesn't support state-level proxying.`,
          'PlanLimitExceeded',
          'State-level proxying not supported by current plan',
          getTokenFromRequest(request) || undefined,
        );
      }

      if (
        (req.__bless__.proxyState || req.__bless__.proxyCity) &&
        !req.__bless__.proxyCountry
      ) {
        return this.logConnectionFailure(
          req as unknown as AugmentedRequest,
          writeable,
          400,
          `In order to proxy by city or state you must specify a country with the "proxyCountry" query-parameter.`,
          'InvalidProxyConfig',
          'Missing country for city/state proxy configuration',
          getTokenFromRequest(request) || undefined,
        );
      }

      parsedURL.searchParams.set(
        '--proxy-server',
        `http://0.0.0.0:${req.__bless__.proxyPort}`,
      );
    }

    this.CLOUD_ONLY_QUERY_PARAMS.forEach((param) => {
      parsedURL.searchParams.delete(param);
    });

    // Finalize any query-parameters
    req.url = parsedURL.href;
    return true;
  }

  async after({ start, req, status }: AfterResponse) {
    const now = Date.now();
    const seconds = convertMsToSeconds(now - start);
    const request = req as unknown as AugmentedRequest;
    const token = getTokenFromRequest(request);
    const captchas = request.__bless__.captchasSolved;
    const isIntrospectionQuery = isIntrospection(request);

    if (!token) {
      return true;
    }

    if (isIntrospectionQuery) {
      this.log(`Ignoring introspection query. Not recording as metrics.`);
      return true;
    }

    if (request.__bless__.bqlProxyBytes) {
      this.recordMetrics({
        token,
        proxy: getProxyUnitsUsed(request.__bless__.bqlProxyBytes),
      });
    }

    this.recordMetrics({
      token,
      status,
      seconds,
      captchas,
    });

    return true;
  }

  public async browser({
    browser,
    req,
  }: {
    browser: BrowserHook['browser'];
    req: AugmentedRequest;
  }) {
    if (
      !req.__bless__.proxyPort ||
      !this.proxyUsername ||
      !this.proxyPassword
    ) {
      return;
    }

    let bytesUsed = 0;

    const proxyServer = new Server({
      port: req.__bless__.proxyPort,
      prepareRequestFunction: () => {
        const upstreamProxyUrl = this.proxyingEngine
          .chooseVendor(req.__bless__)
          .getProxyURL(req.__bless__);
        this.log(
          `Proxying requests to: "${new URL(upstreamProxyUrl).hostname}"`,
        );
        return {
          requestAuthentication: false,
          upstreamProxyUrl,
        };
      },
    }).on('connectionClosed', ({ stats }: { stats: ConnectionStats }) => {
      const sent = Math.max(stats.srcRxBytes ?? 0, stats.trgTxBytes ?? 0);
      const received = Math.max(stats.srcTxBytes ?? 0, stats.trgRxBytes ?? 0);
      bytesUsed += sent + received;
    });

    await proxyServer.listen();

    const waitForTunnelReady = new Promise((resolve) => {
      this.log(`WAITING FOR TUNNEL CONNECTION...`);
      const onTunnelConnectResponded = ({ socket }: { socket: Socket }) => {
        if (proxyServer.stats.connectRequestCount > 1 && bytesUsed > 0) {
          this.log(
            `TUNNEL CONNECTION ESTABLISHED - continuing with ${proxyServer.stats.connectRequestCount} connections`,
          );
          proxyServer.off('tunnelConnectResponded', onTunnelConnectResponded);
          resolve(true);
        } else {
          this.log(`TESTING TUNNEL CONNECTION - sending test request`);
          // Force a test request to ensure the tunnel is ready
          socket.write('HEAD / HTTP/1.1\r\nHost: google.com\r\n\r\n');
        }
      };

      proxyServer.on('tunnelConnectResponded', onTunnelConnectResponded);
    });

    browser.once('close', async () => {
      this.log(`Browser closed with proxying stats, recording`);
      // Allow events to propagate and hit our
      // before hook's events to tally bytes used.
      await sleep(1000);
      const token = getTokenFromRequest(req) as string;
      const proxy = getProxyUnitsUsed(bytesUsed);

      proxyServer.close(false);
      proxyServer.removeAllListeners();
      this.recordMetrics({
        token,
        proxy,
      });
    });

    await waitForTunnelReady;
  }

  public async page({ page }: PageHook) {
    if (page && !this.config.disableBlocklist()) {
      page.on('request', (request) => {
        if (request.isInterceptResolutionHandled()) return;
        const { pathname } = new URL(request.url());
        if (this.isURLAllowed(pathname)) {
          this.log(`Allowing request: ${request.url()} due to whitelist`);
          return;
        }

        if (this.urlDenyList.some((url) => request.url().startsWith(url))) {
          this.log(`Blocking request: ${request.url()} due to blacklist`);
          page.browser().close();
        }
      });

      page.on('response', (response) => {
        const responseUrl = response.url();
        const remoteAddressIP = response.remoteAddress().ip;
        const { pathname } = new URL(responseUrl);
        if (this.isURLAllowed(pathname)) {
          return;
        }

        if (
          responseUrl &&
          this.urlDenyList.some((url) => responseUrl.startsWith(url))
        ) {
          this.log(`Blocking response: ${responseUrl} due to denyList`);
          page.browser().close();
        }

        if (
          remoteAddressIP &&
          this.blockedIPPatterns.some((ip) => remoteAddressIP.startsWith(ip))
        ) {
          this.log(`Blocking response: ${remoteAddressIP} due to blacklist`);
          page.browser().close();
        }
      });
    }

    return true;
  }

  stop = async () => {
    this.log('Stop called. Shutting down.');
    clearInterval(this.intervalId);
    await this.persistMetrics();
  };
}

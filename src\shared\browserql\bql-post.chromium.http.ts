import { ServerResponse } from 'http';

import {
  contentTypes,
  Methods,
  APITags,
  jsonResponse,
  dedent,
  Logger,
  HTTPRoute,
  SystemQueryParameters,
  CDPLaunchOptions,
} from '@browserless.io/browserless';

import { AugmentedRequest, BrowserlessEnterpriseRoutes } from '../../types.js';
import { EnterpriseRoutes } from '../../paths.js';
import { bql, bqlIntrospection } from './index.js';
import { SharedConfig } from '../../config.js';
import { ChromiumStealthBrowser } from '../../browsers/chromium.stealth.js';
import {
  BYTES_IN_A_MB,
  CALLED_BASED_SECOND_LIMIT,
  getBooleanRequestParameter,
  isIntrospection,
} from '../../utils.js';

export interface QuerySchema extends SystemQueryParameters {
  launch?: CDPLaunchOptions | string;
  humanlike?: boolean;
  blockConsentModals?: boolean;
}

export interface BodySchema {
  query: string;
  operationName?: string;
  variables?: { [key: string]: any };
}

export type BQLRequest = AugmentedRequest & {
  body: BodySchema;
};

let introspectionCache: unknown;

export default class ChromiumBQLHTTPPostRoute extends HTTPRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromiumBQLPostRoute;
  auth = true;
  accepts = [contentTypes.json];
  browser = ChromiumStealthBrowser;
  contentTypes = [contentTypes.json];
  concurrency = true;
  description = dedent(`
    > This API is only available for Enterprise, hosted and self-hosted plans. [Contact us for more information here.](https://www.browserless.io/contact/)

    Parses and executes BrowserQL requests, powered by the BrowserQL Editor or by other API integrations. See the BrowserQL Editor for more documentation on this API.
  `);
  method = Methods.post;
  path = [EnterpriseRoutes.chromiumBQL];
  tags = [APITags.browserAPI];
  async handler(
    req: BQLRequest,
    res: ServerResponse,
    logger: Logger,
  ): Promise<void> {
    const payload = req.body;

    // Don't launch a browser when it's just a client doing introspection
    if (isIntrospection(req)) {
      introspectionCache =
        introspectionCache ?? (await bqlIntrospection(payload));
      return jsonResponse(res, 200, introspectionCache);
    }
    const browserManager = this.browserManager();
    const config = this.config() as SharedConfig;
    const browser = (await browserManager.getBrowserForRequest(
      req,
      this,
      logger,
    )) as unknown as ChromiumStealthBrowser;
    browser.setupCDPProxy(req);

    const humanLike = getBooleanRequestParameter(req, 'humanlike', false);
    const blockConsentModals = getBooleanRequestParameter(
      req,
      'blockConsentModals',
      false,
    );

    try {
      const start = Date.now();
      const onClose = () => browserManager.complete(browser);
      res.once('close', onClose);

      const response = await bql(
        browser,
        config,
        logger,
        payload,
        humanLike,
        blockConsentModals,
        req,
      );
      const end = Date.now();
      const seconds = (end - start) / 1000;
      const timeUnits = Math.ceil(seconds / CALLED_BASED_SECOND_LIMIT);
      const proxyUnits = Math.ceil(req.__bless__.bqlProxyBytes / BYTES_IN_A_MB);
      res.off('close', onClose);

      if (!res.headersSent) {
        res.setHeader('X-Browserless-Units', `${timeUnits + proxyUnits}`);
        return jsonResponse(res, 200, response);
      }
    } finally {
      browserManager.complete(browser);
    }
  }
}

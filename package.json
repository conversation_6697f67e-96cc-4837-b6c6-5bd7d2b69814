{"name": "@browserless.io/enterprise", "version": "2.53.1", "description": "Enterprise platform for browserless", "main": "index.js", "type": "module", "scripts": {"build:adblock": "node ./scripts/build-adblock.js", "build:client": "node ./scripts/build-client.js", "build:selectors": "node ./scripts/build-selectors.js", "build:minify": "node ./scripts/build-minify.js", "build": "cross-env DEBUG=browserless*,-**:verbose && npm run build:adblock && npm run build:client && browserless build && npm run build:selectors && npm run build:minify", "clean": "rimraf build extensions/ublock static/live/client.js", "dev": "echo '`npm run dev` has been replaced with one of: `npm run dev:cloud` or `npm run dev:enterprise`'", "dev:cloud": "npm run build && env-cmd -f .env node build/cloud/index.js", "dev:enterprise": "npm run build && env-cmd -f .env node build/enterprise/index.js", "prettier": "prettier '{src,scripts,.github}/**/*.{js,ts,json,yml,yaml,md}' --log-level error --write", "start:cloud": "node build/cloud/index.js", "start:enterprise": "node build/enterprise/index.js", "test": "env-cmd -f .env.test ./node_modules/.bin/mocha --exit", "install:dev": "cross-env DEBUG=browserless*,-**:verbose browserless install-browsers"}, "playwrightVersions": {"default": "playwright-core", "1.53": "playwright-1.53", "1.52": "playwright-1.52", "1.51": "playwright-1.51", "1.50": "playwright-1.50", "1.49": "playwright-1.49", "1.48": "playwright-1.48", "1.47": "playwright-1.47", "1.46": "playwright-1.46", "1.45": "playwright-1.45", "1.44": "playwright-1.44", "1.43": "playwright-1.43", "1.42": "playwright-1.42", "1.41": "playwright-1.41"}, "engines": {"node": ">= 24 < 25"}, "author": "browserless.io", "license": "UNLICENSED", "dependencies": {"@2captcha/captcha-solver": "^1.3.0", "@aws-sdk/client-sqs": "^3.856.0", "@browserless.io/browserless": "2.33.0", "@types/bezier-js": "^4.1.3", "bezier-js": "^6.1.4", "crypto-js": "^4.2.0", "css-unit-converter": "^1.1.2", "eventsource": "^2.0.2", "graphql": "^16.11.0", "http-proxy": "^1.18.1", "https-proxy-agent": "^7.0.6", "jszip": "^3.10.1", "micromatch": "^4.0.8", "node-fetch": "^3.3.2", "playwright-1.41": "npm:playwright-core@1.41.2", "playwright-1.42": "npm:playwright-core@1.42.1", "playwright-1.43": "npm:playwright-core@1.43.1", "playwright-1.44": "npm:playwright-core@1.44.1", "playwright-1.45": "npm:playwright-core@1.45.3", "playwright-1.46": "npm:playwright-core@1.46.1", "playwright-1.47": "npm:playwright-core@1.47.2", "playwright-1.48": "npm:playwright-core@1.48.2", "playwright-1.49": "npm:playwright-core@1.49.1", "playwright-1.50": "npm:playwright-core@1.50.1", "playwright-1.51": "npm:playwright-core@1.51.1", "playwright-1.52": "npm:playwright-core@1.52.0", "playwright-1.53": "npm:playwright-core@1.53.1", "playwright-core": "1.54.1", "proxy-chain": "^2.5.9", "puppeteer-core": "24.12.1", "rxjs": "^7.8.2", "sharp": "^0.34.3", "ws": "^8.18.3"}, "optionalDependencies": {"@types/ws": "^8.5.14", "chai": "^5.2.1", "env-cmd": "^10.1.0", "esbuild": "^0.25.8", "mocha": "^11.7.1", "prettier": "^3.6.2"}, "prettier": {"semi": true, "trailingComma": "all", "singleQuote": true, "printWidth": 80}, "mocha": {"extension": ["ts"], "loader": "ts-node/esm", "spec": "build/**/*.spec.js", "timeout": 30000, "slow": 5000}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/eventsource": "^1.1.15", "rimraf": "^6.0.1"}}
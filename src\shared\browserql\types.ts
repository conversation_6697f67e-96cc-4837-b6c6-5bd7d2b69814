import { Protocol, ScreenshotOptions } from 'puppeteer-core';

export type waitUntil =
  | 'commit'
  | 'domContentLoaded'
  | 'load'
  | 'networkIdle'
  | 'firstMeaningfulPaint'
  | 'firstContentfulPaint';

export type ScreenshotOpts = Omit<ScreenshotOptions, 'path' | 'encoding'> & {
  timeout: number;
  selector?: string;
};

export type PDFOptions = Omit<
  Protocol.Page.PrintToPDFRequest,
  'paperWidth' | 'paperHeight'
> & {
  timeout: number;
  format:
    | 'letter'
    | 'legal'
    | 'tabloid'
    | 'ledger'
    | 'a0'
    | 'a1'
    | 'a2'
    | 'a3'
    | 'a4'
    | 'a5'
    | 'a6';

  width: number | string;
  height: number | string;
  marginTop: number | string;
  marginBottom: number | string;
  marginLeft: number | string;
  marginRight: number | string;
};

export type solveTypes =
  | 'hcaptcha'
  | 'recaptcha'
  | 'recaptchaV3'
  | 'normal'
  | 'geetest'
  | 'friendlyCaptcha'
  | 'amazonWaf'
  | 'textCaptcha';

export type verifyTypes = 'cloudflare';

/**
 * Encapsulates a schema for how a intercepted request should be handled.
 * The "action" specifies if the request should be aborted, proxied or continued.
 * The "operator" specifies if the conditions should be "OR"'d or "AND"'d together.
 * Finally, the url[], method[] and type[] parameters are used as conditions themselves
 * to match a request against.
 */
export interface FetchInterception {
  /**
   * The action that BQL should take when these requests occur
   */
  action: 'continue' | 'abort' | 'proxy';

  /**
   * A list of HTTP Methods to match requests against and see if they match
   */
  method?: string[];

  /**
   * How to combine these conditions together: either through an "and" or "or"
   */
  operator: 'or' | 'and';

  /**
   * When action is set to "proxy", what server URL should browserless use to issue the request through
   */
  server?: string;

  /**
   * The list of request types (document, stylesheet, etc) to match a request against
   */
  type?: string[];

  /**
   * A list of URL regex patterns to check requests against and see if they match
   */
  url?: string[];

  /**
   * A country to use when action is proxy and no "server" is specified (browserless.io proxy)
   */
  country?: string;

  /**
   * A city to use when action is proxy and no "server" is specified (browserless.io proxy)
   */
  city?: string;

  /**
   * A state to use when action is proxy and no "server" is specified (browserless.io proxy)
   */
  state?: string;

  /**
   * A session ID to use for sticky sessions (browserless.io proxy)
   */
  session?: string;
}

export type operators = 'or' | 'and';

export interface ViewportOptions {
  width: number;
  height: number;
  deviceScaleFactor?: number;
  mobile?: boolean;
}

export interface ViewportResponse {
  width: number;
  height: number;
  deviceScaleFactor: number;
  mobile: boolean;
  time: number;
}

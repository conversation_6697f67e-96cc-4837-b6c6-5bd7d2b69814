import esbuild from 'esbuild';
import path from 'path';
import fs from 'fs/promises';

const buildDir = path.resolve(process.cwd(), 'build');

async function getAllJsFiles(dir) {
  return (await fs.readdir(dir, { recursive: true }))
    .map((file) => (file.endsWith('.js') ? path.join(buildDir, file) : null))
    .filter(Boolean);
}

(async function minifyFiles() {
  const jsFiles = await getAllJsFiles(buildDir);

  for (const file of jsFiles) {
    try {
      await esbuild.build({
        allowOverwrite: true,
        entryPoints: [file],
        outfile: file,
        minify: true,
        bundle: false,
        sourcemap: false,
      });
    } catch (error) {
      throw new Error(`Failed to minify ${file}:`, error);
    }
  }
})();

import { createLogger } from '@browserless.io/browserless';

import { AugmentedRequest } from '../../../types.js';

export abstract class ProxyProvider {
  protected log = createLogger('proxy-provider');
  public abstract name: string;
  public abstract isHealthy: boolean;
  public abstract lastHealthCheck: number;
  public abstract getProxyURL: (
    options: AugmentedRequest['__bless__'],
  ) => string;
  abstract healthCheck: () => Promise<void>;
}

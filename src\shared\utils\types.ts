export enum InteractiveCommands {
  'Input.dispatchKeyEvent' = 'Input.dispatchKeyEvent',
  'Input.dispatchMouseEvent' = 'Input.dispatchMouseEvent',
  'Input.emulateTouchFromMouseEvent' = 'Input.emulateTouchFromMouseEvent',
}

export enum HostCommands {
  'close' = 'close',
  'run' = 'run',
  'setViewport' = 'setViewport',
  'start' = 'start',
  'enableKeyboard' = 'enableKeyboard',
  'pasteClipboard' = 'pasteClipboard',
  'requestClipboard' = 'requestClipboard',
}

export enum ClientCommands {
  'browserClose' = 'browserClose',
  'error' = 'error',
  'runComplete' = 'runComplete',
  'screencastFrame' = 'screencastFrame',
  'startComplete' = 'startComplete',
  'iframeBoundsUpdate' = 'iframeBoundsUpdate',
  'clipboardContent' = 'clipboardContent',
}

export interface Message {
  command: string;
  data: unknown;
}

export interface StartMessage {
  height: number;
  id: string;
  width: number;
}

export interface ClipboardMessage {
  content: string;
  type: 'text' | 'html';
}

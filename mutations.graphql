"""
Response returned after a captcha has been solved
"""
type CaptchaResponse {
  """
  If a captcha was found or not
  """
  found: Boolean

  """
  If a captcha was found, whether or not it was solved
  """
  solved: Boolean

  """
  The total time it took to find, and solve, the captcha
  """
  time: Float

  """
  The solved token of the response, if any is provided
  """
  token: String
}

"""
Response returned after setting up the proxy patterns
"""
type ProxyResponse {
  time: Float
}

"""
A HTTP header input type
"""
input HeaderInput {
  """
  The name of the HTTP header
  """
  name: String!

  """
  The value of the HTTP header
  """
  value: String!
}

"""
The binary-type of the stream
"""
enum LiveURLStreamType {
  """
  JPEG formatted binary type
  """
  jpeg

  """
  PNG formatted binary type
  """
  png
}

"""
The two-letter ISO code for the specified country
"""
enum CountryType {
  """
  Andorra
  """
  AD

  """
  United Arab Emirates
  """
  AE

  """
  Afghanistan
  """
  AF

  """
  Antigua and Barbuda
  """
  AG

  """
  Albania
  """
  AL

  """
  Armenia
  """
  AM

  """
  Angola
  """
  AO

  """
  Argentina
  """
  AR

  """
  Austria
  """
  AT

  """
  Australia
  """
  AU

  """
  Aruba
  """
  AW

  """
  Åland Islands
  """
  AX

  """
  Azerbaijan
  """
  AZ

  """
  Bosnia and Herzegovina
  """
  BA

  """
  Barbados
  """
  BB

  """
  Bangladesh
  """
  BD

  """
  Belgium
  """
  BE

  """
  Burkina Faso
  """
  BF

  """
  Bulgaria
  """
  BG

  """
  Bahrain
  """
  BH

  """
  Burundi
  """
  BI

  """
  Benin
  """
  BJ

  """
  Bermuda
  """
  BM

  """
  Brunei Darussalam
  """
  BN

  """
  Plurinational State of Bolivia
  """
  BO

  """
  Sint Eustatius and Saba Bonaire
  """
  BQ

  """
  Brazil
  """
  BR

  """
  Bahamas
  """
  BS

  """
  Bhutan
  """
  BT

  """
  Botswana
  """
  BW

  """
  Belarus
  """
  BY

  """
  Belize
  """
  BZ

  """
  Canada
  """
  CA

  """
  Congo, The Democratic Republic of the
  """
  CD

  """
  Congo
  """
  CG

  """
  Switzerland
  """
  CH

  """
  Côte d'Ivoire
  """
  CI

  """
  Chile
  """
  CL

  """
  Cameroon
  """
  CM

  """
  China
  """
  CN

  """
  Colombia
  """
  CO

  """
  Costa Rica
  """
  CR

  """
  Cuba
  """
  CU

  """
  Cabo Verde
  """
  CV

  """
  Curaçao
  """
  CW

  """
  Cyprus
  """
  CY

  """
  Czechia
  """
  CZ

  """
  Germany
  """
  DE

  """
  Djibouti
  """
  DJ

  """
  Denmark
  """
  DK

  """
  Dominica
  """
  DM

  """
  Dominican Republic
  """
  DO

  """
  Algeria
  """
  DZ

  """
  Ecuador
  """
  EC

  """
  Estonia
  """
  EE

  """
  Egypt
  """
  EG

  """
  Spain
  """
  ES

  """
  Ethiopia
  """
  ET

  """
  Finland
  """
  FI

  """
  Fiji
  """
  FJ

  """
  Faroe Islands
  """
  FO

  """
  France
  """
  FR

  """
  Gabon
  """
  GA

  """
  United Kingdom
  """
  GB

  """
  Grenada
  """
  GD

  """
  Georgia
  """
  GE

  """
  French Guiana
  """
  GF

  """
  Guernsey
  """
  GG

  """
  Ghana
  """
  GH

  """
  Gibraltar
  """
  GI

  """
  Gambia
  """
  GM

  """
  Guinea
  """
  GN

  """
  Guadeloupe
  """
  GP

  """
  Greece
  """
  GR

  """
  Guatemala
  """
  GT

  """
  Guam
  """
  GU

  """
  Guyana
  """
  GY

  """
  Hong Kong
  """
  HK

  """
  Honduras
  """
  HN

  """
  Croatia
  """
  HR

  """
  Haiti
  """
  HT

  """
  Hungary
  """
  HU

  """
  Indonesia
  """
  ID

  """
  Ireland
  """
  IE

  """
  Israel
  """
  IL

  """
  Isle of Man
  """
  IM

  """
  India
  """
  IN

  """
  Iraq
  """
  IQ

  """
  Iran, Islamic Republic of
  """
  IR

  """
  Iceland
  """
  IS

  """
  Italy
  """
  IT

  """
  Jersey
  """
  JE

  """
  Jamaica
  """
  JM

  """
  Jordan
  """
  JO

  """
  Japan
  """
  JP

  """
  Kenya
  """
  KE

  """
  Kyrgyzstan
  """
  KG

  """
  Cambodia
  """
  KH

  """
  Saint Kitts and Nevis
  """
  KN

  """
  Korea, Republic of
  """
  KR

  """
  Kuwait
  """
  KW

  """
  Cayman Islands
  """
  KY

  """
  Kazakhstan
  """
  KZ

  """
  Lao People's Democratic Republic
  """
  LA

  """
  Lebanon
  """
  LB

  """
  Saint Lucia
  """
  LC

  """
  Liechtenstein
  """
  LI

  """
  Sri Lanka
  """
  LK

  """
  Liberia
  """
  LR

  """
  Lesotho
  """
  LS

  """
  Lithuania
  """
  LT

  """
  Luxembourg
  """
  LU

  """
  Latvia
  """
  LV

  """
  Libya
  """
  LY

  """
  Morocco
  """
  MA

  """
  Monaco
  """
  MC

  """
  Moldova, Republic of
  """
  MD

  """
  Montenegro
  """
  ME

  """
  Madagascar
  """
  MG

  """
  North Macedonia
  """
  MK

  """
  Mali
  """
  ML

  """
  Myanmar
  """
  MM

  """
  Mongolia
  """
  MN

  """
  Macao
  """
  MO

  """
  Martinique
  """
  MQ

  """
  Mauritania
  """
  MR

  """
  Montserrat
  """
  MS

  """
  Malta
  """
  MT

  """
  Mauritius
  """
  MU

  """
  Maldives
  """
  MV

  """
  Malawi
  """
  MW

  """
  Mexico
  """
  MX

  """
  Malaysia
  """
  MY

  """
  Mozambique
  """
  MZ

  """
  Namibia
  """
  NA

  """
  New Caledonia
  """
  NC

  """
  Nigeria
  """
  NG

  """
  Nicaragua
  """
  NI

  """
  Netherlands
  """
  NL

  """
  Norway
  """
  NO

  """
  Nepal
  """
  NP

  """
  New Zealand
  """
  NZ

  """
  Oman
  """
  OM

  """
  Panama
  """
  PA

  """
  Peru
  """
  PE

  """
  French Polynesia
  """
  PF

  """
  Papua New Guinea
  """
  PG

  """
  Philippines
  """
  PH

  """
  Pakistan
  """
  PK

  """
  Poland
  """
  PL

  """
  Puerto Rico
  """
  PR

  """
  Palestine, State of
  """
  PS

  """
  Portugal
  """
  PT

  """
  Paraguay
  """
  PY

  """
  Qatar
  """
  QA

  """
  Réunion
  """
  RE

  """
  Romania
  """
  RO

  """
  Serbia
  """
  RS

  """
  Russian Federation
  """
  RU

  """
  Rwanda
  """
  RW

  """
  Seychelles
  """
  SC

  """
  Sweden
  """
  SE

  """
  Singapore
  """
  SG

  """
  Slovenia
  """
  SI

  """
  Slovakia
  """
  SK

  """
  Sierra Leone
  """
  SL

  """
  San Marino
  """
  SM

  """
  Senegal
  """
  SN

  """
  Somalia
  """
  SO

  """
  Suriname
  """
  SR

  """
  South Sudan
  """
  SS

  """
  Sao Tome and Principe
  """
  ST

  """
  El Salvador
  """
  SV

  """
  Sint Maarten (Dutch part)
  """
  SX

  """
  Syrian Arab Republic
  """
  SY

  """
  Eswatini
  """
  SZ

  """
  Turks and Caicos Islands
  """
  TC

  """
  Togo
  """
  TG

  """
  Thailand
  """
  TH

  """
  Tajikistan
  """
  TJ

  """
  Tunisia
  """
  TN

  """
  Turkey
  """
  TR

  """
  Trinidad and Tobago
  """
  TT

  """
  Taiwan, Province of China
  """
  TW

  """
  United Republic of Tanzania
  """
  TZ

  """
  Ukraine
  """
  UA

  """
  Uganda
  """
  UG

  """
  United States of America
  """
  US

  """
  Uruguay
  """
  UY

  """
  Uzbekistan
  """
  UZ

  """
  Saint Vincent and the Grenadines
  """
  VC

  """
  Bolivarian Republic of Venezuela
  """
  VE

  """
  Virgin Islands, British
  """
  VG

  """
  Virgin Islands, U.S.
  """
  VI

  """
  Vietnam
  """
  VN

  """
  Samoa
  """
  WS

  """
  Mayotte
  """
  YT

  """
  South Africa
  """
  ZA

  """
  Zambia
  """
  ZM

  """
  Zimbabwe
  """
  ZW
}

"""
The different types of captchas that can be solved
"""
enum CaptchaTypes {
  """
  hCaptcha captcha type
  """
  hcaptcha

  """
  reCAPTCHA captcha type
  """
  recaptcha

  """
  reCAPTCHA v3 captcha type
  """
  recaptchaV3

  """
  GeeTest captcha type
  """
  geetest

  """
  Normal captcha type
  """
  normal

  """
  friendlyCaptcha captcha type
  """
  friendlyCaptcha

  """
  textCaptcha captcha type
  """
  textCaptcha

  """
  amazonWaf captcha type
  """
  amazonWaf
}

"""
Logical operators for APIs that have conditions
"""
enum OperatorTypes {
  """
  "and" logical operator
  """
  and

  """
  "or" logical operator
  """
  or
}

"""
Response returned after having clicked on an element
"""
type ClickResponse {
  """
  The selector text if specified
  """
  selector: String

  """
  The amount of time, in milliseconds, elapsed since the start of clicking to completion
  """
  time: Float

  """
  The X coordinate of the click, in pixels, on the page
  """
  x: Float

  """
  The Y coordinate of the click, in pixels, on the page
  """
  y: Float
}

"""
The cookie to be sent to the page
"""
input CookieInput {
  """
  Cookie domain
  """
  domain: String

  """
  Cookie expiration date, session cookie if not set
  """
  expires: Float

  """
  True if cookie is http-only
  """
  httpOnly: Boolean

  """
  Cookie name
  """
  name: String!

  """
  Cookie path
  """
  path: String

  """
  Cookie SameSite type
  """
  sameSite: CookieSameSite

  """
  True if cookie is secure
  """
  secure: Boolean

  """
  The request-URI to associate with the setting of the cookie. This value can affect the default domain, path, source port, and source scheme values of the created cookie
  """
  url: String

  """
  Cookie value
  """
  value: String!
}

"""
Options for cleaning up the DOM prior to exporting its content. Many options are available, and this query can destructively remove non-text DOM nodes, DOM attributes, and gratuitous whitespace characters. Since these operations are destructive in their nature it's recommended to run them at the very end of your query in order to preserve page functionality
"""
input CleanInput {
  """
  When true (default is true) this will remove non-textual nodes from the DOM like scripts, links, video, canvas, etc. You may override this by specifying a `selectors` argument for DOM selectors to remove.
  """
  removeNonTextNodes: Boolean = true

  """
  When true (default is false) this will remove all attributes on all DOM nodes. Useful for "cleaning" up all HTML markup but preserving the structure overall. You can specify specific attributes to remove with `attributes` argument
  """
  removeAttributes: Boolean = false

  """
  Removes any characters in the HTML by a regex pattern and arn in order. By default this is true and removes newlines, returns, tabs, multi-spaces and HTML comments in that order. You may supply your own regex by using the `regexes` argument
  """
  removeRegex: Boolean = true

  """
  A list of selectors to remove from the page when `removeNonTextNodes` is set to true (`true` by default).
  """
  selectors: [String!] = [
    "area"
    "audio"
    "base"
    "embed"
    "head"
    "iframe"
    "img"
    "link"
    "meta"
    "noscript"
    "object"
    "param"
    "path"
    "picture"
    "script"
    "source"
    "style"
    "svg"
    "track"
    "video"
  ]

  """
  A list of attributes to remove from all DOM nodes. When this isn't specified, and `removeAttributes` is true, all attributes on all DOM nodes are removed. `removeNonTextNodes` must be set to `true` for this to take effect
  """
  attributes: [String]

  """
  When `removeRegex` is set to "true" this list of regex items, without the beginning and ending `/`, are removed from the page. These are each run in order and replaced with a single space character to preserve some of their contents
  """
  regexes: [String!] = [
    # prettier-ignore
    " +"
    # prettier-ignore
    "\n"
    # prettier-ignore
    "\r"
    # prettier-ignore
    "\t"
    # prettier-ignore
    "<\\!--.*?-->"
  ]
}

"""
The response returned after setting or getting cookies
"""
type CookieResponse {
  """
  A standard cookie object with the values of the set cookies
  """
  cookies: [StandardCookie]

  """
  The time it took to set and return the cookies
  """
  time: Float
}

"""
The values a cookie's SameSite attribute can hold
"""
enum CookieSameSite {
  """
  Cookies are only sent in a first-party context and not sent along with requests initiated by third party websites
  """
  Strict

  """
  Cookies are withheld on cross-site sub-requests, such as calls to load images or frames, but are sent when a user navigates to the URL from an external site
  """
  Lax

  """
  Cookies are sent in all contexts, in responses to both first-party and cross-origin requests
  """
  None
}

"""
Default response for all methods
"""
type DefaultResponse {
  """
  The default timeout for all methods. Default timeout is 30 seconds, or 30000.
  """
  timeout: Float
}

"""
Response returned after evaluating a script
"""
type EvaluateResponse {
  """
  The time it took for the evaluate call to happen
  """
  time: Float

  """
  The returned value of the script, if any
  """
  value: String
}

"""
Holds the value of a either a float or a string
"""
scalar FloatOrString

"""
HTML content of a page
"""
type HTMLResponse {
  """
  The content of the page's HTML
  """
  html: String

  """
  The amount of time, in milliseconds, elapsed since the start of content retrieval to completion
  """
  time: Float
}

"""
An object representing the header's name and underlying value
"""
type HTTPHeaders {
  name: String
  value: String
}

"""
The response returned after setting HTTP headers
"""
type HTTPHeadersResponse {
  """
  The HTTP headers that were set
  """
  headers: [HTTPHeaders]

  """
  The time it took to set the HTTP headers
  """
  time: Float
}

"""
The response returned after setting the User-Agent
"""
type UserAgentResponse {
  """
  The User-Agent string that was set
  """
  userAgent: String

  """
  The time it took to set the User-Agent
  """
  time: Float
}

"""
Response returned after a navigation event
"""
type HTTPResponse {
  """
  The status code response of the initial page-load
  """
  status: Int

  """
  The amount of time, in milliseconds, elapsed since the start of navigation to completion
  """
  time: Float

  """
  The status text of the response from the initial page-load. Generally 'ok'
  """
  text: String

  """
  The final URL of the page after any potential redirects or URL rewrites
  """
  url: String
}

"""
An object representing the header's name and underlying value
"""
type Headers {
  name: String
  value: String
}

"""
Response returned from the request API
"""
type RequestResponse {
  """
  The final URL of the request
  """
  url: String

  """
  The HTTP method of the request
  """
  method: Method

  """
  The type of request that was made
  """
  type: ResourceType

  """
  The headers of the request
  """
  headers: [Headers]
}

"""
Response returned from the response API
"""
type ResponseResponse {
  """
  The HTTP status code of the response
  """
  status: Int

  """
  The final URL of the response
  """
  url: String

  """
  The HTTP method of the request that facilitated the response
  """
  method: Method

  """
  The type of response that was received
  """
  type: ResourceType

  """
  The response headers returned
  """
  headers: [Headers]

  """
  The body of the response, represented as a string when possible or base64 for binary-type requests like images
  """
  body: String
}

"""
Response returned after having hovered over an element
"""
type HoverResponse {
  """
  The selector text
  """
  selector: String

  """
  The amount of time, in milliseconds, elapsed since the start to completion
  """
  time: Float

  """
  The X coordinate in pixels, on the page
  """
  x: Float

  """
  The Y coordinate in pixels, on the page
  """
  y: Float
}

"""
The response returned after enabling or disabling JavaScript on the page
"""
type JavaScriptResponse {
  """
  Whether or not JavaScript is enabled on the page
  """
  enabled: Boolean

  """
  The time it took to perform this action
  """
  time: Float
}

type Attribute {
  name: String
  value: String
}

"""
Response returned from a Map Selector
"""
type MapSelectorResponse {
  """
  The innerHTML of the selected DOM Node
  """
  innerHTML: String

  """
  The innerText of the selected DOM Node, eg, the raw textual content
  """
  innerText: String

  """
  The ID attribute's value, if any, of the node
  """
  id: String

  """
  The class attribute's value, if any, of the node represented as an array of strings
  """
  class: [String]

  """
  Retrieve an attribute by the name of the attribute itself, eg, "data-test-id"
  """
  attribute(name: String): Attribute

  """
  You can further map nested DOM nodes as well. For instance, given a parent ".product" node, you can map further nodes like ".price" or ".shipping" as examples. This will give you items appropriately nested by their parent node for better hierarchical representation of data
  """
  mapSelector(
    """
    A query-selector-al compatible string, or JavaScript code that returns an DOM NodeList. Examples include:
    - All `<button />` Elements:
      `selector: "button"`

    - A JavaScript snippet that returns a button element
      `selector: "document.querySelectorAll('button')"`
    """
    selector: String!

    """
    How long to wait for the element to appear before timing out, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to wait for the selectors to present in the DOM
    """
    wait: Boolean = true
  ): [MapSelectorResponse]
}

"""
The various HTTP-based methods to wait for
"""
enum Method {
  CONNECT
  DELETE
  GET
  HEAD
  OPTIONS
  PATCH
  POST
  PUT
  TRACE
}

"""
The values a pdf's Size attribute can hold
"""
enum PDFPageFormat {
  """
  33.1102in x 46.811in
  """
  a0

  """
  23.3858in x 33.1102in
  """
  a1

  """
  16.5354in x 23.3858in
  """
  a2

  """
  11.6929in x 16.5354in
  """
  a3

  """
  8.2677in x 11.6929in
  """
  a4

  """
  5.8268in x 8.2677in
  """
  a5

  """
  4.1339in x 5.8268in
  """
  a6

  """
  8.5in x 14in
  """
  legal

  """
  8.5in x 11in
  """
  letter

  """
  17in x 11in
  """
  ledger

  """
  11in x 17in
  """
  tabloid
}

"""
The response returned after generating a PDF
"""
type PDFResponse {
  """
  Base64 encoded PDF content
  """
  base64: String

  """
  The size of the resulting PDF in bytes
  """
  size: Float

  """
  The time it took to generate the PDF
  """
  time: Float
}

"""
The response from the Live-URL query
"""
type LiveURLResponse {
  """
  A unique ID representing the session. Useful for storing and associations in other APIs
  """
  liveURLId: String

  """
  The fully-qualified URL to share with the end-user to allow them to live-stream the browser
  """
  liveURL: String
}

"""
The response returned after querying the page for a selector
"""
type QuerySelectorResponse {
  """
  The Element.childElementCount read-only property returns the number of child elements of this element
  """
  childElementCount: Float

  """
  The className property of the Element interface gets and sets the value of the class attribute of the specified element
  """
  className: String

  """
  The id property of the Element interface represents the element's identifier, reflecting the id global attribute
  """
  id: String

  """
  The Element property innerHTML gets the HTML or XML markup contained within the element
  """
  innerHTML: String

  """
  The Element property innerText gets the text contained within the element
  """
  innerText: String

  """
  The Element.localName read-only property returns the local part of the qualified name of an element
  """
  localName: String

  """
  The outerHTML attribute of the Element DOM interface gets the serialized HTML fragment describing the element including its descendants. It can also be set to replace the element with nodes parsed from the given string
  """
  outerHTML: String
}

"""
The response received after attempting to reconnect a BrowserQL session
"""
type ReconnectionResponse {
  """
  The fully-qualified URL to reconnect future BrowserQL sessions, eg: https://chrome.browserless.io/bql/$id. Please note that token information is not returned by this API and might be required
  """
  browserQLEndpoint: String

  """
  The fully-qualified URL of the browserWSEndpoint which can be used with other libraries like playwright or puppeteer. Please note that token information is not returned by this API and might be required
  """
  browserWSEndpoint: String

  """
  The fully-qualified URL of the devtools resources for loading Chrome's developer tools remotely
  """
  devtoolsFrontendUrl: String

  """
  The underlying page's webSocketDebuggerUrl, useful for hooking libraries that operate on a page and not a browser object
  """
  webSocketDebuggerUrl: String
}

"""
The response parameters for the reject mutation
"""
type RejectResponse {
  time: Float
  enabled: Boolean
}

"""
The specific request to perform a conditional action on
"""
input RequestInput {
  """
  The HTTP Method of the request
  """
  method: Method

  """
  The pattern of the request URL to wait for, using glob-style pattern-matching
  """
  url: String
}

"""
The type of resource for a given network or HTTP request
"""
enum ResourceType {
  cspviolationreport
  document
  eventsource
  fetch
  font
  image
  manifest
  media
  other
  ping
  prefetch
  preflight
  script
  signedexchange
  stylesheet
  texttrack
  websocket
  xhr
}

"""
The specific response to perform a conditional action on
"""
input ResponseInput {
  """
  The HTTP Response code(s) of the URL to wait for. Can be a single HTTP code or a list of desired codes
  """
  statuses: [Int]

  """
  The HTTP Response code(s) of the URL to wait for. Can be a single HTTP code or a list of desired codes
  """
  codes: [Int]
    @deprecated(
      reason: "Use `statuses` field instead as it is more consistent in BrowserQL."
    )

  """
  The pattern of the response URL to wait for, using glob-style pattern-matching
  """
  url: String
}

"""
Response returned after having scrolling inside the page
"""
type ScrollResponse {
  """
  The CSS selector of the element on the page you want to scroll to
  """
  selector: String

  """
  The amount of time, in milliseconds, elapsed since the start of scrolling to completion
  """
  time: Float

  """
  The X coordinate, in pixels, to scroll to
  """
  x: Float

  """
  The Y coordinate, in pixels, to scroll to
  """
  y: Float
}

"""
The clipping to be applied to the screenshot
"""
input ScreenshotClip {
  """
  The height of the clip, in pixels
  """
  height: Float

  """
  The scale factor of the clip
  """
  scale: Float

  """
  The width of the clip, in pixels
  """
  width: Float

  """
  The x coordinate to start clipping, in pixels
  """
  x: Float

  """
  The y coordinate to start clipping, in pixels
  """
  y: Float
}

"""
The response returned after generating a Screenshot
"""
type ScreenshotResponse {
  """
  The base64 encoded image of the screenshot
  """
  base64: String

  """
  The format of the screenshot
  """
  format: String

  """
  The time it took to take the screenshot
  """
  time: Float
}

"""
The different types of screenshot formats
"""
enum ScreenshotType {
  """
  JPEG image format
  """
  jpeg

  """
  PNG image format
  """
  png

  """
  WebP image format
  """
  webp
}

"""
The response returned after selecting a value from a dropdown or multiple select element
"""
type SelectResponse {
  """
  The selector of the element you selected from
  """
  selector: String

  """
  The amount of time, in milliseconds, elapsed since the start of selecting to completion
  """
  time: Float

  """
  The value or values you selected from the select element
  """
  value: StringOrArray
}

"""
A standard cookie
"""
type StandardCookie {
  """
  Cookie domain
  """
  domain: String

  """
  Cookie expiration date, session cookie if not set
  """
  expires: Float

  """
  True if cookie is http-only
  """
  httpOnly: Boolean

  """
  Cookie name
  """
  name: String!

  """
  Cookie path
  """
  path: String

  """
  Cookie SameSite type
  """
  sameSite: CookieSameSite

  """
  True if cookie is secure
  """
  secure: Boolean

  """
  Cookie value
  """
  value: String!

  """
  The request-URI to associate with the setting of the cookie. This value can affect the default domain, path, source port, and source scheme values of the created cookie
  """
  url: String
}

"""
Holds the value of a either a string or an array of strings
"""
scalar StringOrArray

"""
Text content of a page
"""
type TextResponse {
  """
  The textual content of the page
  """
  text: String

  """
  The amount of time, in milliseconds, elapsed since the start of text retrieval to completion
  """
  time: Float
}

"""
Response returned after the page's title has been set or get
"""
type TitleResponse {
  """
  The title of the current page
  """
  title: String
}

"""
Response returned after having typed into an element
"""
type TypeResponse {
  """
  The selector of the element you typed into
  """
  selector: String

  """
  The textual content that was typed
  """
  text: String

  """
  The amount of time, in milliseconds, elapsed since the start of typing to completion
  """
  time: Float

  """
  The X coordinate of the element, in pixels, on the page
  """
  x: Float

  """
  The Y coordinate of the element, in pixels, on the page
  """
  y: Float
}

"""
Response returned after the URL of the page has been set or get
"""
type URLResponse {
  """
  The URL of the current page
  """
  url: String
}

"""
Response returned after a verification method has been bypassed
"""
type VerifyResponse {
  """
  If a verification was found or not
  """
  found: Boolean

  """
  If a verification was found, whether or not it was clicked
  """
  solved: Boolean

  """
  The total time it took to find, and click, the verification
  """
  time: Float
}

"""
The different types of verification methods that can be bypassed
"""
enum VerifyTypes {
  """
  Cloudflare protection
  """
  cloudflare
}

"""
Response returned after a particular network request has been sent
"""
type WaitForRequest {
  """
  The period of time elapsed, in milliseconds, waited for
  """
  time: Float

  """
  The URL parameter used to match the response with
  """
  url: String
}

"""
Response returned after a particular network response has been received
"""
type WaitForResponse {
  """
  The period of time elapsed, in milliseconds, waited for
  """
  time: Float

  """
  The status code response of the response
  """
  status: Int

  """
  The URL parameter used to match the response with
  """
  url: String
}

"""
Response returned after a particular selector has been found in the DOM
"""
type WaitForSelector {
  """
  The height, in pixels, of the element
  """
  height: Float

  """
  The selector waited for
  """
  selector: String

  """
  The period of time elapsed, in milliseconds, waited for
  """
  time: Float

  """
  The position, in pixels, top of the viewport
  """
  y: Float

  """
  The position, in pixels, left of the viewport
  """
  x: Float

  """
  The width, in pixels, of the element
  """
  width: Float
}

"""
Response returned after having waited for a selector to appear in the DOM
"""
type WaitForTimeout {
  """
  The period of time elapsed, in milliseconds, waited for
  """
  time: Float
}

"""
Response returned after setting the viewport
"""
type ViewportResponse {
  """
  The width of the viewport in pixels
  """
  width: Float

  """
  The height of the viewport in pixels
  """
  height: Float

  """
  The device scale factor
  """
  deviceScaleFactor: Float

  """
  Whether the device is in mobile mode
  """
  mobile: Boolean

  """
  The time it took to set the viewport
  """
  time: Float
}

"""
Options for when to consider the page has loaded and proceed with further execution
"""
enum WaitUntilGoto {
  """
  Fired when network response is received and the document has started loading
  """
  commit

  """
  Fired when the JavaScript 'load' event occurs
  """
  load

  """
  Fired when the DOMContentLoaded event is fired
  """
  domContentLoaded

  """
  Indicates when the primary content of a page is visible to the user
  """
  firstMeaningfulPaint

  """
  The render time of the largest image or text block visible in the viewport, relative to when the user first navigated to the page
  """
  firstContentfulPaint

  """
  Use with caution: Chrome's best guess as to when the page becomes interactable
  """
  interactiveTime

  """
  Use with caution: Fired when there are no more than 2 network connections for at least 500 ms
  """
  networkIdle
}

"""
The different stages of the browser's loading process
"""
enum WaitUntilHistory {
  """
  Fired when network response is received and the document started loading
  """
  commit

  """
  Fired when the DOMContentLoaded event is fired
  """
  domContentLoaded

  """
  Fired when the 'load' event occurs
  """
  load

  """
  Use with caution: Fired when there are no network connections for at least 500 ms
  """
  networkIdle
}

"""
Below is a list of all available API options for the service. Click on an API for options, examples, and even argument details.

For more comprehensive examples and recipes, [see our documentation site here](https://docs.browserless.io/).
"""
type Mutation {
  """
  Goes back in browser history, optionally accepting waitUntil and timeout arguments. Returns null if no back is possible

  Example:
  ```graphql
  mutation GoBack {
    firstNav: goto(url: "https://example.com") {
      time
    }

    secondNav: goto(url: "https://browserless.com") {
      time
    }

    back(waitUntil: domContentLoaded) {
      status
    }
  }
  ```
  """
  back(
    """
    The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    When to consider the page fully-loaded and proceed with further execution
    """
    waitUntil: WaitUntilHistory = load
  ): HTTPResponse

  """
  Sets or un-sets the value of a checkbox on the page

  Example:
  ```graphql
  mutation ClickCheckbox {
    goto(url: "https://example.com") {
      status
    }

    checkbox(
      checked: true
      selector: "input[type='checkbox']"
    ) {
      time
    }
  }
  ```
  """
  checkbox(
    """
    Whether or not the input should be checked
    """
    checked: Boolean!

    """
    The CSS selector of the element on the page you want to check/uncheck
    """
    selector: String!

    """
    Whether or not to scroll to the element prior to clicking, defaults to true
    """
    scroll: Boolean = true

    """
    How long to wait for the element to appear before timing out on the handler, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to check/uncheck the element only if it's visible
    """
    visible: Boolean = false

    """
    Whether or not to wait for the element to present in the DOM
    """
    wait: Boolean = true
  ): ClickResponse

  """
  Waits for the element to be visible, scrolls to it, then clicks on it with native events

  Example:
  ```grapqhql
  mutation ClickButton {
    goto(url: "https://example.com") {
      status
    }

    click(selector: "a") {
      time
    }
  }
  ```
  """
  click(
    """
    A query-selector compatible string, JavaScript that returns an HTML Node, OR a Browserless-deep query. Examples include:
    - A simple `<button />` Element:
      `selector: "button"`

    - A JavaScript snippet that returns a button element
      `selector: "document.querySelector('button')"`

    - A Browserless Deep query. These queries must start with a "<" character.
      Deep queries will traverse all iframes, shadow-doms (open or closed), and more.
      Basic deep query:
      `selector: "< button"`

    Advanced deep query features:
    - URL Pattern Matching: Target elements within specific iframes using glob patterns
      `selector: "< https://example.com/* button.active"`
      `selector: "< *google.com/recaptcha* #recaptcha-anchor"`

    - Complex Selectors: Combine multiple attributes, classes, and IDs
      `selector: "< input#username.login-form[type='text'][data-test-id*='user']"`

    - Attribute Matching: Use various attribute matching operators
      - Contains (*): `[data-test*="partial"]`
      - Starts with (^): `[class^="prefix-"]`
      - Ends with ($): `[id$="-suffix"]`
      - Exact match: `[type="submit"]`
      - Presence only: `[required]`

    - Class and ID Combinations:
      `selector: "< button#submit.primary.large[data-action='save']"`

    For reCAPTCHA interactions, you can use deep selectors to target elements within the reCAPTCHA iframe:
    `selector: "< *google.com/recaptcha* #recaptcha-anchor"`

    Note: Deep selectors support standard CSS selector syntax while maintaining security by disallowing potentially dangerous characters and patterns.
    """
    selector: String!

    """
    Whether or not to scroll to the element prior to clicking, defaults to true
    """
    scroll: Boolean = true

    """
    How long to wait for the element to appear before timing out on the click handler, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to click the element only if it's visible
    """
    visible: Boolean = false

    """
    Whether or not to wait for the element to present in the DOM
    """
    wait: Boolean = true
  ): ClickResponse

  """
  Sets the given HTML content on the page with an optional waitUntil parameter

  Example:
  ```graphql
  mutation SetContent {
    content(html: "<h1>Hello, World!</h1>") {
      status
    }
  }
  ```
  """
  content(
    """
    When present, sets the content of page to the value passed, then returns the pages content
    """
    html: String!

    """
    When to consider the page fully-loaded and proceed with further execution, used in conjunction with the value parameter
    """
    waitUntil: WaitUntilHistory
  ): HTTPResponse

  """
  Sets and gets cookies on the page

  Example:
  ```graphql
  mutation SetCookies {
    # Get the cookies on the page
    getCookies: cookies {
      cookies {
        name
        value
      }
    }

    # Set a cookie on the page
    setCookies: cookies(cookies: [
        {
          name: "my-cookie"
          value: "my-value"
          url: "https://example.com"
        }
    ]) {
      cookies {
        name
        value
      }
    }
  }
  ```
  """
  cookies(
    """
    The cookies to set on the page
    """
    cookies: [CookieInput]
  ): CookieResponse

  """
  Evaluates JavaScript client-side, via raw content or a URL to some JavaScript code, in the browser's page environment

  Example:
  ```graphql
  mutation EvaluateScript {
    byContent: evaluate(content: "2 + 2") {
      value
    }

    byUrl: evaluate(url: "https://example.com/script.js") {
      value
    }
  }
  ```
  """
  evaluate(
    """
    The raw script you'd like to evaluate. This code gets wrapped in an async function so you can use `return` at the end as well as `await` and other async concepts. You can return any stringified value from this function
    """
    content: String

    """
    A timeout to wait for the script to finish evaluating, overriding any defaults. Useful for async scripts that may be longer running. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    The URL of the script you'd like to evaluate. This code gets wrapped in an async function so you can use `return` at the end as well as `await` and other async
    concepts. You can return any stringified value from this function
    """
    url: String
  ): EvaluateResponse

  """
  Goes forward in browser history, optionally accepting waitUntil and timeout arguments. Returns null if no forward is possible

  Example:
  ```graphql
  mutation GoForward {
    firstNav: goto(url: "https://example.com", waitUntil: load) {
      time
    }

    secondNav: goto(url: "https://browserless.io", waitUntil: load) {
      time
    }

    back(waitUntil: domContentLoaded) {
      status
    }

    forward(waitUntil: domContentLoaded) {
      status
    }
  }
  ```
  """
  forward(
    """
    The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    When to consider the page fully-loaded and proceed with further execution
    """
    waitUntil: WaitUntilHistory = load
  ): HTTPResponse

  """
  Navigates to a URL with an optional waitUntil parameter and timeout parameter

  Example:
  ```graphql
  mutation Goto {
    goto(url: "https://example.com") {
      status
    }
  }
  ```
  """
  goto(
    """
    The fully-qualified URL of the page you'd like to navigate to
    """
    url: String!

    """
    The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    When to consider the page fully-loaded and proceed with further execution
    """
    waitUntil: WaitUntilGoto = load
  ): HTTPResponse

  """
  Returns the HTML content of the page or selector when specified. This API can also "clean" HTML markup returned by specifying a "clean" argument with numerous options. Features of the "clean" argument include removal of non-text nodes, removal of DOM attributes, as well as removal of excessive whitespace and newlines. Using "clean" can save nearly 1,000 times the payload size. Useful for LLM's and other scenarios

  Example:
  ```graphql
  mutation GetHTML {
    goto(url: "https://example.com") {
      status
    }
    html(selector: "h1") {
      html
    }
  }
  ```

  Remove non-text DOM nodes and all Node attributes, but preserve the DOM tree
  ```graphql
  mutation GetHTML {
    goto(url: "https://example.com") {
      status
    }

    html(clean: {
      removeAttributes: true
      removeNonTextNodes: true
    }) {
      html
    }
  }
  ```
  """
  html(
    """
    The DOM selector of the given element you want to return the HTML of
    """
    selector: String

    """
    The maximum amount of time, in milliseconds, to wait for the selector to appear, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to return the HTMLπ content of the element only if it's visible
    """
    visible: Boolean = false

    """
    Specifies conditions for "cleaning" HTML, useful for minimizing the amount of markup returned for cases like LLMs and more. See nested options for parameters.
    """
    clean: CleanInput
  ): HTMLResponse

  """
  Sets the HTTP headers for the page

  Example:
  ```graphql
  mutation SetExtraHTTPHeaders {
    setExtraHTTPHeaders(headers: [
      {name: "x-browserless-secret-header", value: "55"},
      {name: "x-browserless-ping", value: "pong"}
    ]) {
      time
    }
    goto(url: "https://example.com") {
      status
    }
  }
  ```
  """
  setExtraHTTPHeaders(
    """
    The HTTP headers to set on the page
    """
    headers: [HeaderInput]
  ): HTTPHeadersResponse

  """
  Sets the User-Agent string for the browser session

  Example:
  ```graphql
  mutation UserAgent {
    userAgent(userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15") {
      userAgent
      time
    }
    goto(url: "https://example.com") {
      status
    }
  }
  ```
  """
  userAgent(
    """
    The User-Agent string to set for the browser session
    """
    userAgent: String!
  ): UserAgentResponse

  """
  Waits for the element to be visible, scrolls to it, then hover on it with native events

  Example:
  ```graphql
  mutation HoverElement {
    goto(url: "https://example.com") {
      status
    }

    hover(selector: "a") {
      time
    }
  }
  ```
  """
  hover(
    """
    Whether or not to scroll to the element, defaults to true
    """
    scroll: Boolean = true

    """
    The CSS selector of the element on the page you want to hover on
    """
    selector: String

    """
    How long to wait for the element to appear before timing out, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to hover on the element only if it's visible
    """
    visible: Boolean = false

    """
    Whether or not to wait for the element to present in the DOM
    """
    wait: Boolean = true

    """
    The X coordinate, in pixels, to hover on the page
    """
    x: Float

    """
    The Y coordinate, in pixels, to hover on the page
    """
    y: Float
  ): HoverResponse

  """
  Triggers a nested branch of work when a given condition is `true`. Does not wait for these items and is a point-in-time check. Use the wait method if you're wanting to await certain behaviors to be present

  Example:
  ```graphql
  mutation If {
    goto(url: "https://example.com") {
      status
    }

    # Will only trigger the screenshot if the h1 is present
    if(selector: "h1") {
      screenshot {
        base64
      }
    }
  }
  ```
  """
  if(
    """
    Triggers the nested conditions if a request has been made with the following conditions
    """
    request: RequestInput

    """
    Triggers the nested conditions if a response has been received with the following conditions
    """
    response: ResponseInput

    """
    Triggers the subsequent conditions if the selector is immediately present
    """
    selector: String

    """
    When using selectors in conditionals this options sets whether their or not to consider if they're visible to the viewport
    """
    visible: Boolean = false
  ): Mutation

  """
  Triggers a nested branch of work when a given condition is `false`. This method does not wait for these items and is a point-in-time check. Use the wait method if you're wanting to await certain behaviors to be present

  Example:
  ```graphql
  mutation IfNot {
    goto(url: "https://example.com") {
      status
    }

    # Will only trigger the screenshot if the h2 is not present
    ifnot(selector: "h2") {
      screenshot {
        base64
      }
    }
  }
  ```
  """
  ifnot(
    """
    Triggers the nested conditions if a request has been made with the following conditions
    """
    request: RequestInput

    """
    Triggers the nested conditions if a response has been received with the following conditions
    """
    response: ResponseInput

    """
    Triggers the subsequent conditions if the selector is immediately present
    """
    selector: String
  ): Mutation

  """
  Sets and gets JavaScript execution on the page

  **Note: changing this value won't affect scripts that have already been run. It will take full effect on the next navigation.**

  Example:
  ```graphql
  mutation JavaScript {
    status: javaScriptEnabled {
      enabled
    }

    disable: javaScriptEnabled(enabled: false) {
      enabled
    }

    enable: javaScriptEnabled(enabled: true) {
      enabled
    }
  }
  ```
  """
  javaScriptEnabled(
    """
    Whether or not to enable JavaScript on the page
    """
    enabled: Boolean
  ): JavaScriptResponse

  """
  Returns a fully-qualified, user-shareable live-URL for streaming the web-browser to an end-user, optionally interactive.

  **Set 30 second timer for interaction**
  ```graphql
  mutation LiveURL {
    goto(url: "https://example.com") {
      status
    }
    liveURL {
      liveURL
    }
  }
  ```

  **Maintain the current browsers viewport and don't resize it**
  ```graphql
  mutation LiveURL {
    goto(url: "https://example.com") {
      status
    }
    liveURL(resizable: false) {
      liveURL
    }
  }
  ```

  **Don't allow interaction at all**
  ```graphql
  mutation LiveURL {
    goto(url: "https://example.com") {
      status
    }
    liveURL(interactable: false) {
      liveURL
    }
  }
  ```

  **Low quality for better bandwidth**
  ```graphql
  mutation LiveURL {
    goto(url: "https://example.com") {
      status
    }
    liveURL(quality: 20 type: jpeg) {
      liveURL
    }
  }
  ```
  """
  liveURL(
    """
    The maximum time allowed for the browser to remain alive. Once the time is reached, the end-user will receive a prompt that the session has closed
    """
    timeout: Float

    """
    Whether the session is interactable or not. Set to "false" to not allow click and mouse events to be forwarded through to the end-user
    """
    interactable: Boolean = true

    """
    The binary-type of the streamed imaged. "jpeg" will consumer lower bandwidth and useful low bandwidth networks and devices. "png" is a much higher quality but will consume considerably more bandwidth.

    Use "jpeg" when setting a custom quality
    """
    type: LiveURLStreamType = jpeg

    """
    The quality of the stream, represented as number from 1 - 100. Only used when "type" is "jpeg"
    """
    quality: Int = 100

    """
    Whether or not to resize the underlying browser to match the end-user's screen size. When `false` the underlying browser will retain it's current viewport, and the end users's screen wil maintain the appropriate aspect ratio.
    """
    resizable: Boolean = true
  ): LiveURLResponse

  """
  Specify a selector that returns multiple nodes in a document (similar to `document.querySelectorAll`), or JavaScript that returns a NodeList, and this API will respond with details about those DOM nodes. Similar to how "map" works in most functional programming languages and libraries. Useful for mapping over repetitive data in sites and pages like product listings or search results. This will automatically wait for the selector to be present on the page, and is configurable with the "wait" and "timeout" options.

  For getting arbitrary DOM attributes back you can specify them via the `attribute(name: "data-custom-attribute")` property. This will return an object with `name` and `value` properties.

  You may also continuously map further nested items as well, for instance this query might get all books on a page, and then a nested `mapSelector` call might list all sellers of that book, or shipping speeds. Hierarchy of data is preserved to pass through the hierarchical data modeled inside the DOM.

  This API will always return a list of results back regardless if one or more items are found, or `null` if none are found.

  Using aliases can also give the returned JSON more meaning and better model the data returned by this powerful API.

  **Simple Example**
  ```graphql
  mutation HNLinks {
    goto(url: "https://news.ycombinator.com") {
      status
    }

    # Get all the top links HTML
    mapSelector(selector: ".athing") {
      innerHTML
    }
  }
  ```

  **Nested Example With Aliases**
  ```graphql
  mutation HNLinksWithMetaData {
    goto(url: "https://news.ycombinator.com") {
      status
    }

    # Get all textual content
    posts: mapSelector(selector: ".athing") {
      postName: innerText

      # Get the author(s)
      authors: mapSelector(selector: ".author") {
        authorName: innerText
      }

      # Get the post score
      score: mapSelector(selector: ".score") {
        score: innerText
      }
    }
  }
  ```
  """
  mapSelector(
    """
    A `document.querySelectorAll` compatible string, or JavaScript that returns a DOM NodeList. Examples include:
    - A list of `<button />` Elements:
      `selector: "button"`

    - A JavaScript snippet that returns a button element
      `selector: "document.querySelectorAll('button')"`
    """
    selector: String!

    """
    How long to wait for the element to appear before timing out, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to wait for the selectors to present in the DOM
    """
    wait: Boolean = true
  ): [MapSelectorResponse]

  """
  Generates a PDF of the page with the print CSS media type

  Example:
  ```graphql
  mutation PDF {
    goto(url: "https://example.com", waitUntil: firstMeaningfulPaint) {
      status
    }

    simple: pdf {
      base64
    }

    customArgs: pdf(
      format: a5
      displayHeaderFooter: true
      headerTemplate: "<span style=\"font-size: 16pt;\">Hello World</span>"
    ) {
      base64
    }
  }
  ```
  """
  pdf(
    """
    Display header and footer. Defaults to false
    """
    displayHeaderFooter: Boolean

    """
    The page format to use for the PDF
    """
    format: PDFPageFormat

    """
    HTML template for the print footer. Should use the same format as the `headerTemplate`.
    """
    footerTemplate: String

    """
    Whether or not to embed the document outline into the PDF
    """
    generateDocumentOutline: Boolean

    """
    Whether or not to generate tagged (accessible) PDF. Defaults to embedded choice
    """
    generateTaggedPDF: Boolean

    """
    Paper orientation. Defaults to false
    """
    landscape: Boolean

    """
    Print background graphics. Defaults to false
    """
    printBackground: Boolean

    """
    Scale of the webpage rendering. Defaults to 1
    """
    scale: Float

    """
    The maximum amount of time, in milliseconds, to wait for the PDF to be generated. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Width in inches or CSS unit. Defaults to 8.5 inches
    """
    width: FloatOrString

    """
    HTML template for the print header. Should be valid HTML markup with following
    classes used to inject printing values into them:
    - `date`: formatted print date
    - `title`: document title
    - `url`: document location
    - `pageNumber`: current page number
    - `totalPages`: total pages in the document

    For example, `<span class=title></span>` would generate span containing the title
    """
    headerTemplate: String

    """
    Height in inches or CSS unit. Defaults to 11 inches
    """
    height: FloatOrString

    """
    Bottom margin in inches or CSS unit. Defaults to 1cm (~0.4 inches).
    """
    marginBottom: FloatOrString

    """
    Left margin in inches or CSS unit. Defaults to 1cm (~0.4 inches).
    """
    marginLeft: FloatOrString

    """
    Right margin in inches or CSS unit. Defaults to 1cm (~0.4 inches).
    """
    marginRight: FloatOrString

    """
    Top margin in inches or CSS unit. Defaults to 1cm (~0.4 inches).
    """
    marginTop: FloatOrString

    """
    Paper ranges to print, one based, e.g., '1-5, 8, 11-13'. Pages are
    printed in the document order, not in the order specified, and no
    more than once. Defaults to empty string, which implies the entire document is printed.
    The page numbers are quietly capped to actual page count of the
    document, and ranges beyond the end of the document are ignored.
    If this results in no pages to print, an error is reported.
    It is an error to specify a range with start greater than end
    """
    pageRanges: String

    """
    Whether or not to prefer page size as defined by css. Defaults to false,
    in which case the content will be scaled to fit the paper size
    """
    preferCSSPageSize: Boolean

    """
    Return as stream (PrintToPDFRequestTransferMode enum)
    """
    transferMode: String
  ): PDFResponse

  """
  Sets configuration for the entirety of the session, replacing defaults like the 30 second timeout default

  Example:
  ```graphql
  mutation Preferences {
    preferences(timeout: 10000) {
      timeout
    }
  }
  ```
  """
  preferences(
    """
    Sets a default timeout for all methods, including 'goto', 'type', 'wait', etc. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float = 30000
  ): DefaultResponse

  """
  Proxies requests, by a specified set of conditions, through either the Browserless residential proxy or through an external proxy. Only requests that match these conditions are proxied and the rest are sent from the instance's own IP address.

  Use the "server" argument to specify an external proxy for Browserless to use for requests. For requests with authentication, the username and password should be included in the URL using Basic Authentication. See the examples below for more information on how to format those URLs.

  **Using the browserless proxy for all requests and proxy through Brazil**
  ```graphql
  proxy(
    url: "*"
    country: BR
  ) {
    time
  }
  ```
  **Using an external proxy for all requests**
  ```graphql
  proxy(
    url: "*"
    server: "http://username:<EMAIL>:12321"
  ) {
    time
  }
  ```
  **Using the Browserless proxy for only document requests and proxying through France**
  ```graphql
  proxy(
    url: "*"
    type: document
    country: FR
  ) {
    time
  }
  ```
  """
  proxy(
    """
    The country you wish to proxy through. Only allowed when using the browserless.io proxy and no `server` argument.
    """
    country: CountryType

    """
    The city you wish to proxy through. Any spaces should be removed and all casing lowercase. For instance, "New York City" should be "newyorkcity"
    """
    city: String

    """
    The state or provence you wish to proxy through. Any spaces should be removed and all casing lowercase. For instance, "Rhode Island" would be "rhodeisland" and "New Brunswick" would be "newbrunswick"
    """
    state: String

    """
    Whether or not you want the same IP to be used for subsequent requests matching the pattern
    """
    sticky: Boolean

    """
    An external proxy to use for these requests matching the specified patterns set in the other arguments. When this is set then `country`, `city`, `state` and `sticky` options will throw errors as these are only valid for the browserless.io proxy network
    """
    server: String

    """
    The Method(s) of the request you'd like to proxy
    """
    method: [Method]

    """
    Whether to "or" conditions together, meaning any condition that matches will be proxied, or "and" them together meaning every condition must match to proxy the request.
    """
    operator: OperatorTypes = or

    """
    The content-type of the request you'd like to proxy requests to
    """
    type: [ResourceType]

    """
    A glob-style URL pattern to match requests, and if matched, are proxied through
    """
    url: [String]
  ): ProxyResponse

  """
  Passes through certain properties of the browsers' own `document.querySelector` API

  Example:
  ```graphql
  mutation QuerySelector {
    goto(url: "https://example.com") {
      status
    }

    query: querySelector(selector: "h1") {
      innerHTML
    }
  }
  ```
  """
  querySelector(
    selector: String!
    timeout: Float
    visible: Boolean = false
  ): QuerySelectorResponse

  """
  Passes through certain properties of the browsers' own `document.querySelectorAll` API

  Example:
  ```graphql
  mutation QuerySelectorAll {
    goto(url: "https://example.com") {
      status
    }

    query: querySelectorAll(selector: "h1") {
      innerHTML
    }
  }
  """
  querySelectorAll(
    selector: String!
    timeout: Float
    visible: Boolean = false
  ): [QuerySelectorResponse]

  """
  Returns a payload with reconnection information in order to reconnect back to the same browser session

  Example:
  ```graphql
  mutation Reconnect {
    goto(url: "https://example.com") {
      status
    }

    reconnect(timeout: 30000) {
      browserQLEndpoint
      browserWSEndpoint
      devtoolsFrontendUrl
      webSocketDebuggerUrl
    }
  }
  ```
  """
  reconnect(
    """
    The amount of time, in milliseconds, to leave the browser open without a connection before it is manually terminated. Defaults to 30 seconds
    """
    timeout: Float = 30000
  ): ReconnectionResponse

  """
  Rejects requests by a specified URL pattern, method, or type and operator. You may supply a single pattern, or a list (array) of them. This mutation, by default, will reject any requests that match *any* pattern, which we call an "or" operator. To reject requests where conditions must all match, specify an "and" operator in the mutation. Note that this only has an effect when the query is executing, so scripts that return quickly will likely see assets loading in the editor as these rejections only happen when mutations are executing.
  ___
  **Rejecting images or media**
  ```graphql
  mutation RejectImages {
    reject(type: [image, media]) {
      enabled
      time
    }
    goto(url: "https://cnn.com"
    waitUntil: firstContentfulPaint) {
      status
      time
    }
  }
  ```
  ___
  **Rejecting media when coming from google.com domain**
  ```graphql
  mutation Reject {
    reject(
      operator: and
      type: image
      url: "*google.com*"
    ) {
      enabled
      time
    }
    goto(url: "https://cnn.com"
    waitUntil: firstContentfulPaint) {
      status
      time
    }
  }
  ```
  """
  reject(
    """
    Whether or not to enable request rejections
    """
    enabled: Boolean = true

    """
    The Method of the request you'd like to reject
    """
    method: [Method]

    """
    Whether to "or" conditions together, meaning any condition that matches will be rejected, or "and" them together meaning every condition must match to reject the request.
    """
    operator: OperatorTypes = or

    """
    The type of resource you'd like to reject request to
    """
    type: [ResourceType]

    """
    The glob-style URL pattern you'd like to reject requests to
    """
    url: [String]
  ): RejectResponse

  """
  Reloads the given page with an optional waitUntil parameter and timeout parameter

  Example:
  ```graphql
  mutation Reload {
    goto(url: "https://example.com") {
      status
    }
    reload(timeout: 10000) {
      status
    }
  }
  ```
  """
  reload(
    """
    The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    When to consider the page fully-loaded and proceed with further execution
    """
    waitUntil: WaitUntilHistory = load
  ): HTTPResponse

  """
  Returns request information made by the Browser with optional filters via arguments. You may filter the returned results by a lob-like URL-pattern, the method of the request or the type of request. Applying an operator to this will then change the behavior by either "and"ing the filters together or "or"ing them. This API will automatically wait for the request to be made if none is immediately found which you can turn off by disabling the "wait" option.

  **Getting all "Document" requests**
  ```graphql
    mutation DocumentRequests {
      goto(url: "https://example.com/", waitUntil: load) {
        status
      }
      request(type:document) {
        url
        type
        method
        headers {
          name
          value
        }
      }
    }
  ```

  **Load all "GET" AJAX Requests**
  ```graphql
    mutation AJAXGetCalls {
      goto(url: "https://msn.com/", waitUntil: load) {
        status
      }
      request(type: xhr, method: GET, operator: and) {
        url
        type
        method
        headers {
          name
          value
        }
      }
    }
  ```
  """
  request(
    """
    The type content-type of the request to match against
    """
    type: [ResourceType]

    """
    The method of the request to return results for
    """
    method: [Method]

    """
    How long to wait for the request(s) to be made before timing out, overriding any defaults. Default timeout is 30 seconds, or 30000. "wait" parameter must also be "true".
    """
    timeout: Float

    """
    The pattern of the request URL to wait for, using glob-style pattern-matching
    """
    url: [String]

    """
    Whether or not to wait for the request to be made. When set to `true`, generally only one request is returned since this API will wait for the first request that matches any patterns to be returned
    """
    wait: Boolean = true

    """
    When applying arguments like URL or method, this operator will either "and" them together, or "or" them together. Default is "or"
    """
    operator: OperatorTypes = or
  ): [RequestResponse]

  """
  Returns response information, filtered by the provided arguments, made by the browser. You may optionally filter the returned results by a glob-like URL-pattern, the Method of the response or the Type of response. Applying an operator to this will then change the behavior by either "and"ing the filters together, or "or"ing them. This API will automatically wait for the response to be made if none is immediately found which you can turn off by disabling the "wait" option.

  **Getting all "Document" responses**
  ```graphql
    mutation DocumentResponses{
      goto(url: "https://example.com/", waitUntil: load) {
        status
      }
      response(type:document) {
        url
        body
        headers {
          name
          value
        }
      }
    }
  ```

  **Load all "GET" AJAX Responses**
  ```graphql
    mutation AJAXGetCalls {
      goto(url: "https://msn.com/", waitUntil: load) {
        status
      }
      response(type: xhr, method: GET, operator: and) {
        url
        type
        method
        body
        headers {
          name
          value
        }
      }
    }
  ```
  """
  response(
    """
    The status codes response to return results for
    """
    status: [Int]

    """
    The method of the response to return results for
    """
    method: [Method]

    """
    When applying arguments like URL or method, this operator will either "and" them together, or "or" them together. Default is "or"
    """
    operator: OperatorTypes = or

    """
    How long to wait for the response(s) to be made before timing out, overriding any defaults. Default timeout is 30 seconds, or 30000. "wait" parameter must also be "true".
    """
    timeout: Float

    """
    The type content-type of the response to match against
    """
    type: [ResourceType]

    """
    The pattern of the response URL to wait for, using glob-style pattern-matching
    """
    url: [String]

    """
    Whether or not to wait for the response to be received. When set to `true`, generally only one response is returned since this API will wait for the first response that matches any patterns to be returned
    """
    wait: Boolean = true
  ): [ResponseResponse]

  """
  Screenshots the page or a specific selector

  Example:
  ```graphql
  mutation Screenshot {
    goto(url: "https://example.com") {
      status
    }

    screenshot {
      base64
    }
  }
  ```
  """
  screenshot(
    """
    Capture the screenshot beyond the viewport.
    Default: False if there is no clip. True otherwise
    """
    captureBeyondViewport: Boolean

    """
    Specifies the region of the page/element to clip
    """
    clip: ScreenshotClip
    """
    Capture the screenshot from the surface, rather than the view.
    Default: True
    """
    fromSurface: Boolean

    """
    When True, takes a screenshot of the full page.
    Default: False
    """
    fullPage: Boolean

    """
    Hides default white background and allows capturing screenshots with transparency.
    Default: False
    """
    omitBackground: Boolean

    """
    Optimize image encoding for speed, not for resulting size.
    Default: False
    """
    optimizeForSpeed: Boolean

    """
    Quality of the image, between 0-100. Not applicable to png images.
    """
    quality: Float

    """
    The CSS selector of the element on the page you want to screenshot
    """
    selector: String

    """
    The final format of the screenshot
    """
    type: ScreenshotType

    """
    The maximum amount of time, in milliseconds, to wait for the screenshot to be taken. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float
  ): ScreenshotResponse

  """
  Waits for a selector, then scrolls to it on the page or an x,y coordinate in pixels

  Example:
  ```graphql
  mutation Scroll {
    goto(url: "https://example.com") {
      status
    }

    scroll(selector: "h1") {
      time
    }
  }
  ```
  """
  scroll(
    """
    The DOM selector of the element on the page you want to scroll to
    """
    selector: String

    """
    How long to wait for the element to appear before timing out, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to scroll to the element only if it's visible
    """
    visible: Boolean = false

    """
    Whether or not to wait for the element, then scroll to it
    """
    wait: Boolean = true

    """
    The X coordinate, in pixels, to scroll to
    """
    x: Float

    """
    The Y coordinate, in pixels, to scroll to
    """
    y: Float
  ): ScrollResponse

  """
  Selects a value from a dropdown or multiple select element

  Example:
  ```graphql
  mutation Select {
    goto(url: "https://example.com") {
      status
    }

    single: select(
      selector: "select"
      value: "option1"
    ) {
      time
    }

    multi: select(
      selector: "select"
      value: ["option1", "option2"]
    ) {
      time
    }
  }
  ```
  """
  select(
    """
    How long to wait for the element to appear before timing out on the handler, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to scroll to the select element prior to selecting, defaults to true
    """
    scroll: Boolean = true

    """
    The CSS selector of the element on the page you want to select a value from
    """
    selector: String!

    """
    The value or values to select from the dropdown or multiple select element
    """
    value: StringOrArray!

    """
    Whether or not to select the element only if it's visible
    """
    visible: Boolean = false

    """
    Whether or not to wait for the select to present in the DOM
    """
    wait: Boolean = true
  ): SelectResponse

  """
  🚨 **EXPERIMENTAL** 🚨
  Solves a captcha or other challenge. Can auto-detect the CAPTCHA type or solve a specific type.
  Uses the same detection logic as BaaS to automatically identify the CAPTCHA type
  and routes to the appropriate solver. Optionally accepts a specific type to solve.

  Example (Auto-detection):
  ```graphql
  mutation SolveAuto {
    goto(url: "https://protected.domain") {
      status
    }

    solve {
      found
      solved
      time
      token
    }
  }
  ```

  Example (Specific type):
  ```graphql
  mutation SolveSpecific {
    goto(url: "https://protected.domain") {
      status
    }

    solve(type: hcaptcha) {
      found
      solved
      time
      token
    }
  }
  ```
  """
  solve(
    """
    An enum of the type of captcha to look for and solve. If not provided, auto-detection will be used.
    """
    type: CaptchaTypes

    """
    A time, in milliseconds, to wait for a captcha to appear. Only valid when wait = true.
    If a captcha is found then this timer doesn't have an effect on timing-out the solve
    Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to wait for the Captcha to be present on the page
    """
    wait: Boolean = true
  ): CaptchaResponse

  """
  Returns the text content on the given page or by selector when specified

  Example:
  ```graphql
  mutation GetText {
    goto(url: "https://example.com") {
      status
    }

    selector: text(selector: "h1") {
        text
    }

    fullPage: text {
      text
    }
  }
  ```
  """
  text(
    """
    The maximum amount of time, in milliseconds, to wait for the selector to appear, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    The DOM selector of the given element you want to return the text of
    """
    selector: String = html

    """
    Whether or not to return the text content of the element only if it's visible
    """
    visible: Boolean = false

    """
    Specifies conditions for "cleaning" the returned text, useful for minimizing the amount of markup returned for cases like LLMs and more. See nested options for parameters.
    """
    clean: CleanInput
  ): TextResponse

  """
  Returns the title of the page that the browser is currently at

  Example:
  ```graphql
  mutation GetTitle {
    goto(url: "https://example.com") {
      status
    }

    title {
      title
    }
  }
  ```
  """
  title: TitleResponse

  """
  Types text into an element by scrolling to it, clicking it, then emitting key events for every character

  Example:
  ```graphql
  mutation Type {
    goto(url: "https://example.com") {
      status
    }

    type(
      text: "Hello, World!"
      selector: "input[type='text']"
    ) {
      time
    }
  }
  ```
  """
  type(
    """
    The text content you want to type into the element
    """
    text: String!

    """
    The CSS selector of the element on the page you want to type text into
    """
    selector: String!

    """
    The amount of delay between keystrokes in milliseconds. Values are used as a range and chosen at random
    """
    delay: [Int] = [50, 200]

    """
    Whether or not to check if element can be interacted with by hovering over it and seeing if the element
    is available at that x and y position
    """
    interactable: Boolean = true

    """
    Whether or not to scroll to the element prior to typing, defaults to true
    """
    scroll: Boolean = true

    """
    How long to wait for the element to appear before timing out, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to type into the element only if it's visible
    """
    visible: Boolean = false

    """
    Whether or not to wait for the element to present in the DOM
    """
    wait: Boolean = true
  ): TypeResponse

  """
  Returns the URL of the page that the browser is currently at

  Example:
  ```graphql
  mutation GetURL {
    goto(url: "https://example.com") {
      status
    }

    url {
      url
    }
  }
  ```
  """
  url: URLResponse

  """
  🚨 **EXPERIMENTAL** 🚨
  Clicks a verification button to assert human-like

  Example:
  ```graphql
  mutation Verify {
    goto(url: "https://protected.domain") {
      status
    }

    verify(type: cloudflare) {
      found
      solved
      time
    }
  }
  ```
  """
  verify(
    """
    An enum of the type of captcha to look for and solve
    """
    type: VerifyTypes!

    """
    A time, in milliseconds, to wait for a verification to appear. Only valid when wait = true. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to wait for the verification to be present on the page
    """
    wait: Boolean = true
  ): CaptchaResponse

  """
  Waits for a navigation even to fire, useful for clicking an element and waiting for a page load of some

  Example:
  ```graphql
  mutation WaitForNavigation {
    goto(url: "https://example.com") {
      status
    }

    waitForNavigation(waitUntil: load) {
      status
    }
  }
  ```
  """
  waitForNavigation(
    """
    The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    When to consider the page fully-loaded and proceed with further execution
    """
    waitUntil: WaitUntilGoto = load
  ): HTTPResponse

  """
  Waits for the browser to make a particular request

  Example:
  ```graphql
  mutation WaitForRequest {
    goto(url: "https://browserless.io") {
      status
    }

    waitForRequest(method: GET) {
      time
    }
  }
  ```
  """
  waitForRequest(
    """
    The method of the request to wait for
    """
    method: Method

    """
    How long to wait for the request to be made before timing out, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    The pattern of the request URL to wait for, using glob-style pattern-matching
    """
    url: String
  ): WaitForRequest

  """
  Waits for a particular network response to be made back to the browser

  Example:
  ```graphql
  mutation WaitForResponse {
    goto(url: "https://browserless.io") {
      status
    }

    waitForResponse(codes: [200]) {
      time
    }
  }
  ```
  """
  waitForResponse(
    """
    The HTTP Response code(s) of the URL to wait for. Can be a single HTTP code or a list of desired codes
    """
    codes: [Int]
      @deprecated(
        reason: "Use `statuses` field instead as it is more consistent in BrowserQL."
      )

    """
    The HTTP Response code(s) of the URL to wait for. Can be a single HTTP code or a list of desired codes
    """
    statuses: [Int]

    """
    The pattern of the response URL to wait for, using glob-style pattern-matching
    """
    url: String
  ): WaitForResponse

  """
  Wait for a period of time, defined in milliseconds

  Example:
  ```graphql
  mutation WaitForTimeout {
    waitForTimeout(time: 1000) {
      time
    }
  }
  ```
  """
  waitForTimeout(
    """
    The amount of time to wait for, in milliseconds
    """
    time: Float!
  ): WaitForTimeout

  """
  Waits for a given selector to be present in the DOM, with optional visibility

  Example:
  ```graphql
  mutation WaitForSelector {
    goto(url: "https://example.com") {
      status
    }

    waitForSelector(selector: "h1") {
      time
    }
  }
  ```
  """
  waitForSelector(
    """
    The selector to wait for until present in the DOM
    """
    selector: String!

    """
    When waiting for a selector applies a timeout to wait for in milliseconds, overriding any defaults. Default timeout is 30 seconds, or 30000.
    """
    timeout: Float

    """
    Whether or not to consider the element as present only if it's visible
    """
    visible: Boolean = false
  ): WaitForSelector

  """
  Sets the viewport dimensions for the browser session

  Example:
  ```graphql
  mutation SetViewport {
    viewport(width: 1920, height: 1080) {
      width
      height
      time
    }
  }
  ```

  **Mobile device simulation:**
  ```graphql
  mutation MobileViewport {
    viewport(width: 375, height: 667, deviceScaleFactor: 1, mobile: true) {
      width
      height
      deviceScaleFactor
      mobile
      time
    }
  }
  ```

  **Desktop simulation:**
  ```graphql
  mutation DesktopViewport {
    viewport(width: 1920, height: 1080, mobile: false) {
      width
      height
      mobile
      time
    }
  }
  ```
  """
  viewport(
    """
    The width of the viewport in pixels
    """
    width: Float!

    """
    The height of the viewport in pixels
    """
    height: Float!

    """
    The device scale factor, defaults to 1
    """
    deviceScaleFactor: Float = 1

    """
    Whether to simulate a mobile device, defaults to false
    """
    mobile: Boolean = false
  ): ViewportResponse
}

type Query {
  """
  The Version of this server
  """
  version: String

  """
  The Version of the browser
  """
  browser: String
}

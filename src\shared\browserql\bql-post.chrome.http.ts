import { ChromeStealthBrowser } from '../../browsers/chrome.stealth.js';
import { EnterpriseRoutes } from '../../paths.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';
import {
  BodySchema,
  QuerySchema,
  default as ChromiumBQLHTTPPostRoute,
} from './bql-post.chromium.http.js';

export default class ChromeBQLPostRoute extends ChromiumBQLHTTPPostRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromeBQLPostRoute;
  browser = ChromeStealthBrowser;
  path = [EnterpriseRoutes.chromeBQL];
}

export { BodySchema, QuerySchema };

import { noop } from '@browserless.io/browserless';
import { expect } from 'chai';
import Sinon from 'sinon';
import fs from 'fs/promises';
import path from 'path';
import { findBreakingChanges, buildClientSchema, buildSchema } from 'graphql';

import BrowserlessCloud from '../browserless.js';
import { CloudAPI } from '../api.js';
import { CloudUnitConfig } from '../config.js';
import { CloudCredits } from '../credit.js';
import { CloudHooks } from '../hooks.js';
import { randomID } from '../../utils.js';
import { ServerEvents } from '../server-events.js';
import { SessionEndedEventsPublisher } from '../../shared/utils/session-ended-publisher.js';
import { cloudUnitPlanNumbers } from '../../types.js';
import { loadSchema } from '../../shared/browserql/schema/index.js';

const token = randomID(16);

const apiMock = Sinon.createStubInstance(CloudAPI, {
  getCloudCredits: Sinon.stub().resolves({
    [token]: ['Infinity', 1],
  }) as any,
  saveCloudMetrics: Sinon.stub().resolves({}) as any,
});

const serverEventsMock = Sinon.createStubInstance(ServerEvents, {
  addEventListener: Sinon.stub().resolves({}) as any,
});

const sessionEventsPublisherMock = Sinon.createStubInstance(
  SessionEndedEventsPublisher,
  {
    publishEvents: Sinon.stub().resolves({}) as any,
    publishFailedEntries: Sinon.stub().resolves({}) as any,
  },
);

describe('Cloud-Unit BQL APIs', function () {
  let browserless: BrowserlessCloud;

  const start = async ({
    config: configOverride,
    credits: creditsOverride,
    api: apiOverride,
    sessionEndedPublisher: sessionEndedPublisherOverride,
    hooks: hooksOverride,
  }: {
    config?: CloudUnitConfig;
    credits?: CloudCredits;
    api?: CloudAPI;
    sessionEndedPublisher?: SessionEndedEventsPublisher;
    hooks?: CloudHooks;
  } = {}) => {
    const api = apiOverride ?? apiMock;
    const credits = creditsOverride ?? new CloudCredits(api, serverEventsMock);
    const config = configOverride ?? new CloudUnitConfig(credits);
    const sessionEndedPublisher =
      sessionEndedPublisherOverride ?? sessionEventsPublisherMock;
    const hooks =
      hooksOverride ??
      new CloudHooks(config, credits, api, '', sessionEndedPublisher);

    browserless = new BrowserlessCloud({
      api,
      credits,
      hooks,
      config,
      sessionEndedPublisher,
    });

    await browserless.start();
    return browserless;
  };

  afterEach(async () => {
    browserless && (await browserless.stop().catch(noop));
  });

  it('should have no breaking changes', async () => {
    const oldIntrospection = await fs.readFile(
      path.join(process.cwd(), '__schema_snapshot__.json'),
      'utf-8',
    );
    const introJSON = JSON.parse(oldIntrospection);
    const oldSchema = buildClientSchema(introJSON);
    const schemaString = await loadSchema();
    const newSchema = buildSchema(schemaString);
    const breaking = findBreakingChanges(oldSchema, newSchema);

    // `breaking` here will be an array of breakages if they exist, if not it's empty
    expect(breaking).to.eql([]);
  });

  it('should allow BQL calls', async () => {
    await start();
    const res = await fetch(
      `http://localhost:3000/chromium/bql?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          query: 'mutation a { goto(url: "https://example.com") { status } }',
        }),
      },
    );

    expect(res.status).to.equal(200);
    const body = await res.json();
    expect(body).to.eql({ data: { goto: { status: 200 } } });
  });

  it('should allow BQL calls to Chrome', async () => {
    await start();
    const res = await fetch(`http://localhost:3000/chrome/bql?token=${token}`, {
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      body: JSON.stringify({
        query: `mutation Example { goto(url: "https://one.one.one.one", waitUntil: networkIdle) { status } text { text } }`,
      }),
    });

    expect(res.status).to.equal(200);
    const body = await res.json();
    expect(body.data.goto.status).to.equal(200);
    expect(body.data.text.text).to.include('The free app that makes');
  });

  it('should limit BQL reconnections', async () => {
    await start();
    const res = await fetch(
      `http://localhost:3000/chromium/bql?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          query:
            'mutation a { goto(url: "https://example.com", waitUntil: networkIdle) { status } reconnect(timeout: 31000) { browserQLEndpoint } }',
        }),
      },
    );

    expect(res.status).to.equal(200);
    const body = await res.json();
    expect(body.errors[0].message).to.eql(
      'Reconnect time exceeds your current plans limits.',
    );
  });

  it('should allow BQL re-connections when within limits', async () => {
    const token = randomID(16);
    const apiMock = Sinon.createStubInstance(CloudAPI, {
      getCloudCredits: Sinon.stub().resolves({
        [token]: ['Infinity', cloudUnitPlanNumbers.oneHundredEightyThousand],
      }) as any,
      saveCloudMetrics: Sinon.stub().resolves({}) as any,
    });
    const credits = new CloudCredits(apiMock, serverEventsMock);
    const config = new CloudUnitConfig(credits);
    const hooks = new CloudHooks(
      config,
      credits,
      apiMock,
      '',
      sessionEventsPublisherMock,
    );
    await start({ credits, hooks });
    const res = await fetch(
      `http://localhost:3000/chromium/bql?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          query:
            'mutation a { goto(url: "https://example.com", waitUntil: networkIdle) { status } reconnect(timeout: 30000) { browserQLEndpoint } }',
        }),
      },
    );

    expect(res.status).to.equal(200);
    const body = await res.json();
    expect(body.data.reconnect.browserQLEndpoint);
  });

  describe('#text', () => {
    it('should allow getting text by selector', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              text(selector:"a") { text }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.text.text).to.equal('Check');
    });

    it('should allow getting html by eval', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              text(selector:"document.querySelector('a')") { text }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.text.text).to.equal('Check');
    });
  });

  describe('#html', () => {
    it('should allow getting html by selector', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              html(selector:"a") { html }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.html.html).to.equal('Check');
    });

    it('should allow getting html by eval', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              html(selector:"document.querySelector('a')") { html }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.html.html).to.equal('Check');
    });
  });

  describe('#waitForSelector', () => {
    it('should allow waiting for a selector', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              waitForSelector(selector: "a") { time }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.waitForSelector.time).to.be.a('number');
    });

    it('should allow waiting for selector evals', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              waitForSelector(selector: "document.querySelector('a')") { time }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.waitForSelector.time).to.be.a('number');
    });
  });

  describe('#waitForTimeout', () => {
    it('should allow waiting for a timeout', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              waitForTimeout(time: 10) { time }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.waitForTimeout.time).to.be.a('number');
    });
  });

  describe('#waitForRequest', () => {
    it('should allow waiting for a request to have been made by pattern', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              goto(url: "https://example.com/") { status }
              waitForRequest(url: "**example.com/") { time }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.waitForRequest.time).to.be.a('number');
    });
  });

  describe('#waitForResponse', () => {
    it('should allow waiting for a request to have been made by pattern', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              goto(url: "https://example.com/") { status }
              waitForResponse(url: "**example.com/") { time }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.waitForResponse.time).to.be.a('number');
    });
  });

  describe('#hover', () => {
    it('should allow hovering on an element', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              hover(selector: "a") { x, y }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.hover.x).to.be.a('number');
      expect(body.data.hover.y).to.be.a('number');
    });

    it('should allow hovering on an element by eval', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              hover(selector: "document.querySelector('a')") { x, y }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.hover.x).to.be.a('number');
      expect(body.data.hover.y).to.be.a('number');
    });
  });

  describe('#click', () => {
    it('should allow clicking on an element', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              click(selector: "a") { x, y }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.click.x).to.be.a('number');
      expect(body.data.click.y).to.be.a('number');
    });

    it('should allow clicking on an element by eval', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>Check</a>") { time }
              click(selector: "document.querySelector('a')") { x, y }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.click.x).to.be.a('number');
      expect(body.data.click.y).to.be.a('number');
    });

    it('should allow clicking on an element in the shadow-DOM', async () => {
      await start();
      const html = `<body></body><script>const shadowHost=document.createElement('div');document.body.appendChild(shadowHost);const shadowRoot=shadowHost.attachShadow({mode:'closed'}),button=document.createElement('button');button.textContent='Cant click this!',shadowRoot.appendChild(button);</script>`;
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a($html: String!) {
              content(html: $html) {
                time
              }
              click(selector: "< button") {
                x
                y
              }
            }`,
            variables: {
              html,
            },
          }),
        },
      );

      const body = await res.json();
      expect(body.data.click.x).to.be.a('number');
      expect(body.data.click.y).to.be.a('number');
    });
  });

  describe('#reject', () => {
    it('should allow rejecting by type', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation Reject {
              reject(type: document) {
                time
                enabled
              }
              goto(url: "https://example.com" timeout: 1000) {
                status
                time
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.reject.enabled).to.eql(true);
      expect(body.data.goto.status).to.eql(null);
    });

    it('should allow rejecting by URL', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation Reject {
              reject(url: "*example.com*") {
                time
                enabled
              }
              goto(url: "https://example.com" timeout: 1000) {
                status
                time
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.reject.enabled).to.eql(true);
      expect(body.data.goto.status).to.eql(null);
    });

    it('should allow rejecting by method', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation Reject {
              reject(method: GET) {
                time
                enabled
              }
              goto(url: "https://example.com" timeout: 1000) {
                status
                time
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.reject.enabled).to.eql(true);
      expect(body.data.goto.status).to.eql(null);
    });
  });

  describe('#type', () => {
    it('should allow typing on an element', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation Specs {
              content(html: "<input id='selector' type='text' />") {
                time
              }
              type(selector: "input", text: "bar") {
                time
              }
              evaluate(content: "document.querySelector('input').value") {
                value
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.evaluate.value).to.eql('bar');
    });

    it('should allow typing on an element by eval', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation Specs {
              content(html: "<input id='selector' type='text' />") {
                time
              }
              type(selector: "document.querySelector('input')", text: "bar") {
                time
              }
              evaluate(content: "document.querySelector('input').value") {
                value
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.evaluate.value).to.eql('bar');
    });

    it('should allow typing in an element in the shadow-DOM', async () => {
      await start();
      const html = `<body></body><script>const shadowHost=document.createElement('div');document.body.appendChild(shadowHost);const shadowRoot=shadowHost.attachShadow({mode:'closed'}),input=document.createElement('input');input.value='',shadowRoot.appendChild(input);</script>`;
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a($html: String!) {
              content(html: $html) {
                time
              }
              type(selector: "< input", text: "foo") {
                x
                y
              }
            }`,
            variables: {
              html,
            },
          }),
        },
      );

      const body = await res.json();
      expect(body.data.type.x).to.be.a('number');
      expect(body.data.type.y).to.be.a('number');
    });
  });

  describe('#evaluate', () => {
    it('should allow running custom JS', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation Specs {
              evaluate(content: "'hello world'") {
                value
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.evaluate.value).to.eql('hello world');
    });

    it('should allow running async functions', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation Specs {
              evaluate(content: "new Promise(r => setTimeout(r('hello world')))") {
                value
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.evaluate.value).to.eql('hello world');
    });
  });

  describe('#querySelector', () => {
    it('should return attributes for the query', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>hello world</a>") { time }
              querySelector(selector: "a") { innerText }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.querySelector.innerText).to.eql('hello world');
    });

    it('should allow running async functions', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              content(html: "<a>hello world</a>") { time }
              querySelector(selector: "document.querySelector('a')") { innerText }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.querySelector.innerText).to.eql('hello world');
    });
  });

  describe('#goto', () => {
    it('should return data when navigating with waitUntil options', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              goto(url:"https://example.com", waitUntil: networkIdle) {
                status
                time
                url
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.goto.status).to.eql(200);
      expect(body.data.goto.time).to.be.a('number');
      expect(body.data.goto.url).to.include('example.com');
    });

    it('should timeout goto calls', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              goto(url:"https://example.com", waitUntil: networkIdle, timeout: 100) {
                status
                time
                url
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.errors[0].message).to.include('Timeout of 100ms reached');
    });
  });

  describe('#forward #backward', () => {
    it('should return data when navigating with backward', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              goto(url:"https://example.com/some/path", waitUntil: networkIdle) {
                status
                time
                url
              }
              gotoNext: goto(url:"https://example.com", waitUntil: networkIdle) {
                status
                time
                url
              }
              back(waitUntil: load) {
                status
                time
                url
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      console.log(body);
      expect(body.data.goto.status).to.eql(404);
      expect(body.data.gotoNext.status).to.eql(200);
      // https://example.com/some/path is a non-200 site
      expect(body.data.back.status).to.eql(404);
      expect(body.data.back.time).to.be.a('number');
      expect(body.data.back.url).to.include('example.com');
    });

    it('should return data when navigating with forward', async () => {
      await start();
      const res = await fetch(
        `http://localhost:3000/chromium/bql?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            query: `mutation a {
              goto(url:"https://example.com/some/path", waitUntil: networkIdle) {
                status
                time
                url
              }
              gotoNext: goto(url:"https://example.com", waitUntil: networkIdle) {
                status
                time
                url
              }
              back(waitUntil: load) {
                status
                time
                url
              }
              forward(waitUntil: domContentLoaded) {
                status
                time
                url
              }
            }`,
          }),
        },
      );

      const body = await res.json();
      expect(body.data.forward.status).to.eql(200);
      expect(body.data.forward.time).to.be.a('number');
      expect(body.data.forward.url).to.include('example.com');
    });
  });
});

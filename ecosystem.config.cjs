module.exports = {
  apps: [
    {
      name: "browserlesss",
      script: "./build/enterprise/index.js",
      env: {
        CORS: true,
        ENTERPRISE_API_TOKEN: "4749E1ACDB26B2D1C80EFA8836E82262",
        PROXY_PASSWORD: "PKzbu2qzd5unxqgy",
        PROXY_USERNAME: "browserlessio",
        CONCURRENT: "100",
        QUEUED: "100",
        EXTERNAL: "https://chrome.browserless.io/e/53616c7465645f5f00fc27dd1fdf1212fdfaeeb282c38e3182a4a8a57a3bb52a51759d7bc2a70166093c3f6f3f23b55d",
        DEBUG: "browserless*,bql*,-**:verbose,-**:trace,-**:debug",
        API_URL: "https://example.com/",
        TOKEN: "4749E1ACDB26B2D1C80EFA8836E82262",
        PORT: "3000",
        DEBUG_COLORS: "true",
        HOST: "0.0.0.0",
        TIMEOUT: "600000"
      }
    }
  ]
}

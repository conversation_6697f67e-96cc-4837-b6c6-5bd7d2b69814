import { ChromeStealthBrowser } from '../../browsers/chrome.stealth.js';
import { EnterpriseRoutes } from '../../paths.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';
import {
  QuerySchema,
  default as ChromiumStealthWebsocketRoute,
} from './chromium.stealth.ws.js';

export { QuerySchema };

export default class ChromeStealthWebsocketRoute extends ChromiumStealthWebsocketRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromeStealthWebsocketRoute;
  browser = ChromeStealthBrowser;
  path = [EnterpriseRoutes.chromeStealth];
}

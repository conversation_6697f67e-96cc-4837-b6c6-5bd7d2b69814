import { seedManager } from '../utils/seed-manager.js';

export const createDomProtectionScript = () => {
  // Enhanced DOMRect protection with variable noise
  const createDOMRectNoise = seedManager.createDOMRectNoise();

  // Override all timing functions for consistent behavior
  const timingProxy = (original: Performance['now']) => {
    return function (this: typeof original, ...args: any) {
      const result = original.apply(this, args);
      const noise = createDOMRectNoise(result);
      return result + noise;
    };
  };

  Performance.prototype.now = timingProxy(Performance.prototype.now);
  Date.now = timingProxy(Date.now);

  // Protect all measurement APIs
  const protectMeasurements = (obj: unknown, props: string[]) => {
    const noise = createDOMRectNoise;
    props.forEach((prop) => {
      const descriptor = Object.getOwnPropertyDescriptor(obj, prop);
      if (descriptor && descriptor.get) {
        Object.defineProperty(obj, prop, {
          get: function () {
            const value = descriptor.get?.call(this);
            return value + noise(value);
          },
        });
      }
    });
  };

  // Apply protection to all measurement APIs
  protectMeasurements(window, [
    'innerWidth',
    'innerHeight',
    'outerWidth',
    'outerHeight',
  ]);
  protectMeasurements(screen, ['width', 'height', 'availWidth', 'availHeight']);
  protectMeasurements(Element.prototype, [
    'clientWidth',
    'clientHeight',
    'offsetWidth',
    'offsetHeight',
  ]);
};

export const createMathProtectionScript = () => {
  // Enhanced Math protection with realistic precision errors
  const createMathNoise = seedManager.createMathNoise();
  const originalMath = {
    acos: Math.acos,
    acosh: Math.acosh,
    asin: Math.asin,
    asinh: Math.asinh,
    atan: Math.atan,
    atan2: Math.atan2,
    atanh: Math.atanh,
    cbrt: Math.cbrt,
    cos: Math.cos,
    cosh: Math.cosh,
    exp: Math.exp,
    expm1: Math.expm1,
    log: Math.log,
    log10: Math.log10,
    log1p: Math.log1p,
    log2: Math.log2,
    sin: Math.sin,
    sinh: Math.sinh,
    sqrt: Math.sqrt,
    tan: Math.tan,
    tanh: Math.tanh,
  };

  // Apply realistic precision errors to Math functions
  Object.keys(originalMath).forEach((key) => {
    (Math as any)[key] = function (...args: any[]) {
      const result = (originalMath as any)[key].apply(this, args);
      return result + createMathNoise(result);
    };
  });

  // Protect Error stack traces
  const originalError = Error;
  // @ts-ignore
  Error = function (...args) {
    const error = new originalError(...(args as any));
    Object.defineProperty(error, 'stack', {
      get: function () {
        return error.stack?.replace(/\\(.+\\)@|@.+:/g, '@');
      },
    });
    return error;
  };
  // @ts-ignore
  Error.prototype = originalError.prototype;
};

export const createWorkerProtectionScript = () => {
  // Consistent worker scope protection
  const originalWorker = window.Worker;
  // @ts-ignore
  window.Worker = function (scriptURL, options) {
    const worker = new originalWorker(scriptURL, options);

    // Protect worker scope from detection
    const originalPostMessage = worker.postMessage;
    worker.postMessage = function (message, transfer) {
      setTimeout(() => {
        // @ts-ignore
        originalPostMessage.call(this, message, transfer);
      }, Math.random() * 2);
    };

    return worker;
  };

  // Protect SharedWorker if available
  if (window.SharedWorker) {
    const originalSharedWorker = window.SharedWorker;
    // @ts-ignore
    window.SharedWorker = function (scriptURL, name) {
      const worker = new originalSharedWorker(scriptURL, name);
      return worker;
    };
  }
};

export const createProfileProtectionScript = () => {
  // Enhanced browser profile protection with realistic values
  const createProfileNoise = seedManager.createProfileNoise();

  // Common desktop/laptop configurations
  const hardwareProfiles = [
    { cores: 4, memory: 8, touch: 0 },
    { cores: 6, memory: 16, touch: 0 },
    { cores: 8, memory: 16, touch: 0 },
    { cores: 8, memory: 32, touch: 0 },
    { cores: 12, memory: 32, touch: 0 },
  ];

  // Select a consistent profile for this session
  const profileIndex = Math.floor(Date.now() % hardwareProfiles.length);
  const profile = hardwareProfiles[profileIndex];

  // Protect navigator properties
  const originalNavigator = navigator;
  Object.defineProperty(window, 'navigator', {
    get: () => {
      return new Proxy(originalNavigator, {
        get: (target, prop) => {
          switch (prop) {
            case 'hardwareConcurrency':
              return Math.floor(createProfileNoise(profile.cores, 0.5));
            case 'deviceMemory':
              return Math.floor(createProfileNoise(profile.memory, 1));
            case 'maxTouchPoints':
              return Math.floor(createProfileNoise(profile.touch, 0.1));
            case 'platform':
              return target.platform.replace(/\\s+/g, '');
            case 'userAgent':
              return target.userAgent.replace(
                /\\(.*?\\)/,
                '(Windows NT 10.0; Win64; x64)',
              );
            case 'languages':
              return ['en-US', 'en'];
            default:
              return Reflect.get(target, prop);
          }
        },
      });
    },
  });

  // Enhanced storage protection
  const createStorageProxy = (storage: Storage) => {
    const storageSize = Math.floor(createProfileNoise(5, 2));
    return new Proxy(storage, {
      get: (target, prop) => {
        if (prop === 'length') {
          return storageSize;
        }
        return Reflect.get(target, prop);
      },
    });
  };

  Object.defineProperty(window, 'localStorage', {
    get: () => createStorageProxy(localStorage),
  });

  Object.defineProperty(window, 'sessionStorage', {
    get: () => createStorageProxy(sessionStorage),
  });

  // Protect screen properties with consistent values
  Object.defineProperty(window, 'screen', {
    get: () => {
      const originalScreen = window.screen;
      return new Proxy(originalScreen, {
        get: (target, prop) => {
          switch (prop) {
            case 'width':
            case 'availWidth':
              return Math.floor(createProfileNoise(1920, 10));
            case 'height':
            case 'availHeight':
              return Math.floor(createProfileNoise(1080, 10));
            case 'colorDepth':
            case 'pixelDepth':
              return 24;
            default:
              return Reflect.get(target, prop);
          }
        },
      });
    },
  });
};

export const createNoiseScript = () => {
  // Enhanced hardware profile simulation
  const createHardwareProfile = seedManager.createHardwareNoise();

  // @ts-ignore
  window.createNoise = createHardwareProfile;
};

export const createCanvasProtectionScript = () => {
  // Enhanced canvas protection with improved WebGL support
  const createContextProxy = (
    canvas: HTMLCanvasElement,
    contextType: string,
  ) => {
    // Handle all WebGL context variations
    const isWebGL =
      /^(webgl|webgl2|experimental-webgl|experimental-webgl2)$/.test(
        contextType,
      );
    const ctx = canvas.getContext(contextType);

    if (!ctx) {
      throw new Error(`Failed to get ${contextType} context`);
    }

    return new Proxy(ctx, {
      get: (target, prop) => {
        // Handle WebGL-specific methods
        if (isWebGL && prop in target) {
          return function (...args: any) {
            const fn = Reflect.get(target, prop);
            if (typeof fn === 'function') {
              const result = fn.apply(target, args);
              if (
                result instanceof Uint8Array ||
                result instanceof Uint8ClampedArray
              ) {
                const noiseFn = seedManager.createCanvasNoise();
                for (let i = 0; i < result.length; i += 4) {
                  result[i] = Math.max(
                    0,
                    Math.min(255, result[i] + noiseFn(i)),
                  );
                  result[i + 1] = Math.max(
                    0,
                    Math.min(255, result[i + 1] + noiseFn(i + 1)),
                  );
                  result[i + 2] = Math.max(
                    0,
                    Math.min(255, result[i + 2] + noiseFn(i + 2)),
                  );
                }
              }
              return result;
            }
            return fn;
          };
        }

        // Handle 2D context methods
        if (!isWebGL && 'getImageData' in target) {
          return function (...args: any) {
            const imageData = target.getImageData.apply(target, args);
            const noiseFn = seedManager.createCanvasNoise();
            for (let i = 0; i < imageData.data.length; i += 4) {
              imageData.data[i] = Math.max(
                0,
                Math.min(255, imageData.data[i] + noiseFn(i)),
              );
              imageData.data[i + 1] = Math.max(
                0,
                Math.min(255, imageData.data[i + 1] + noiseFn(i + 1)),
              );
              imageData.data[i + 2] = Math.max(
                0,
                Math.min(255, imageData.data[i + 2] + noiseFn(i + 2)),
              );
            }
            return imageData;
          };
        }

        return Reflect.get(target, prop);
      },
    });
  };

  // Override toDataURL with enhanced protection
  const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
  HTMLCanvasElement.prototype.toDataURL = function (type, quality) {
    const ctx = createContextProxy(
      this,
      this.getAttribute('data-context') || '2d',
    );
    if (ctx && 'getImageData' in ctx && 'putImageData' in ctx) {
      const imageData = ctx.getImageData(0, 0, this.width, this.height);
      ctx.putImageData(imageData, 0, 0);
    }
    return originalToDataURL.call(this, type, quality);
  };

  // Override toBlob with enhanced protection
  const originalToBlob = HTMLCanvasElement.prototype.toBlob;
  HTMLCanvasElement.prototype.toBlob = function (callback, type, quality) {
    const ctx = createContextProxy(
      this,
      this.getAttribute('data-context') || '2d',
    );
    if (ctx && 'getImageData' in ctx && 'putImageData' in ctx) {
      const imageData = ctx.getImageData(0, 0, this.width, this.height);
      ctx.putImageData(imageData, 0, 0);
    }
    return originalToBlob.call(this, callback, type, quality);
  };

  // @ts-ignore
  const originalGetContext = HTMLCanvasElement.prototype.getContext;
  // @ts-ignore
  HTMLCanvasElement.prototype.getContext = function (
    contextType,
    _contextAttributes,
  ) {
    // Store the context type for later use
    this.setAttribute('data-context', contextType);

    if (
      /^(webgl|webgl2|experimental-webgl|experimental-webgl2)$/.test(
        contextType,
      )
    ) {
      return createContextProxy(this, contextType);
    }
    return createContextProxy(this, contextType);
  };
};

export const createInlineProfileProtectionScript = () => {
  // Enhanced browser profile protection with realistic values
  const createProfileNoise = seedManager.createProfileNoise();

  // Common desktop/laptop configurations
  const hardwareProfiles = [
    { cores: 4, memory: 8, touch: 0 },
    { cores: 6, memory: 16, touch: 0 },
    { cores: 8, memory: 16, touch: 0 },
    { cores: 8, memory: 32, touch: 0 },
    { cores: 12, memory: 32, touch: 0 },
  ];

  // Select a consistent profile for this session
  const profileIndex = Math.floor(Date.now() % hardwareProfiles.length);
  const profile = hardwareProfiles[profileIndex];

  // Protect navigator properties
  const originalNavigator = navigator;
  Object.defineProperty(window, 'navigator', {
    get: () => {
      return new Proxy(originalNavigator, {
        get: (target, prop) => {
          switch (prop) {
            case 'hardwareConcurrency':
              return Math.floor(createProfileNoise(profile.cores, 0.5));
            case 'deviceMemory':
              return Math.floor(createProfileNoise(profile.memory, 1));
            case 'maxTouchPoints':
              return Math.floor(createProfileNoise(profile.touch, 0.1));
            case 'platform':
              return target.platform.replace(/\\s+/g, '');
            case 'userAgent':
              return target.userAgent.replace(
                /\\(.*?\\)/,
                '(Windows NT 10.0; Win64; x64)',
              );
            case 'languages':
              return ['en-US', 'en'];
            default:
              return Reflect.get(target, prop);
          }
        },
      });
    },
  });

  // Enhanced storage protection
  const createStorageProxy = (storage: Storage) => {
    const storageSize = Math.floor(createProfileNoise(5, 2));
    return new Proxy(storage, {
      get: (target, prop) => {
        if (prop === 'length') {
          return storageSize;
        }
        return Reflect.get(target, prop);
      },
    });
  };

  Object.defineProperty(window, 'localStorage', {
    get: () => createStorageProxy(localStorage),
  });

  Object.defineProperty(window, 'sessionStorage', {
    get: () => createStorageProxy(sessionStorage),
  });

  // Protect screen properties with consistent values
  Object.defineProperty(window, 'screen', {
    get: () => {
      const originalScreen = window.screen;
      return new Proxy(originalScreen, {
        get: (target, prop) => {
          switch (prop) {
            case 'width':
            case 'availWidth':
              return Math.floor(createProfileNoise(1920, 10));
            case 'height':
            case 'availHeight':
              return Math.floor(createProfileNoise(1080, 10));
            case 'colorDepth':
            case 'pixelDepth':
              return 24;
            default:
              return Reflect.get(target, prop);
          }
        },
      });
    },
  });
};

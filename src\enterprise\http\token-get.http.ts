import { ServerResponse } from 'http';

import {
  contentTypes,
  Request,
  Methods,
  APITags,
  HTTPRoute,
  jsonResponse,
  dedent,
  Logger,
} from '@browserless.io/browserless';

import { BrowserlessEnterpriseRoutes, BrowserlessToken } from '../../types.js';

import token from '../token.js';
import { EnterpriseRoutes } from '../../paths.js';

export type ResponseSchema = BrowserlessToken[];

export default class TokenGetRoute extends HTTPRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseTokenGetRoute;
  auth = true;
  accepts = [contentTypes.any];
  browser = null;
  contentTypes = [contentTypes.json];
  concurrency = false;
  description = dedent(`
    > This API is only available for Enterprise hosted and self-hosted plans. [Contact us for more information here.](https://www.browserless.io/contact/)

    Returns a JSON payload of the current tokens available and their relevant access.
  `);
  method = Methods.get;
  path = EnterpriseRoutes.tokens;
  tags = [APITags.management];
  async handler(
    _req: Request,
    res: ServerResponse,
    _logger: Logger,
  ): Promise<void> {
    const response: ResponseSchema = await token.getTokens();

    return jsonResponse(res, 200, response);
  }
}

import { ChromeCDP } from '@browserless.io/browserless';
import { EnterpriseRoutes } from '../../paths.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';
import {
  BodySchema,
  QuerySchema,
  ResponseSchema,
  default as ChromiumExportPostRoute,
} from './chromium.export.http.js';

export default class ChromeExportPostRoute extends ChromiumExportPostRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromeExportPostRoute;
  browser = ChromeCDP;
  path = [EnterpriseRoutes.chromeExport];
}

export { BodySchema, QuerySchema, ResponseSchema };

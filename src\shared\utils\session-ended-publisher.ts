import { createLogger } from '@browserless.io/browserless';
import { SessionEndedEvent } from '../../types.js';
import {
  SQSClient,
  SendMessageBatchCommand,
  SendMessageBatchRequestEntry,
} from '@aws-sdk/client-sqs';

export class SessionEndedEventsPublisher {
  protected log = createLogger('events-publisher');
  protected batchSize: number = 10;
  protected queueUrl: string;
  protected client: SQSClient;
  protected failedEntries: SendMessageBatchRequestEntry[] = [];

  constructor(queueUrl: string, region: string) {
    this.queueUrl = queueUrl;
    this.client = new SQSClient({ region });
  }

  protected async publishEntries(
    entries: SendMessageBatchRequestEntry[],
    retries: number = 3,
  ) {
    let pendingEntries: SendMessageBatchRequestEntry[] = [...entries];
    let retry = 0;
    while (retry++ < retries) {
      try {
        const command = new SendMessageBatchCommand({
          QueueUrl: this.queueUrl,
          Entries: pendingEntries,
        });
        const data = await this.client.send(command);
        if (data.Failed?.length) {
          const failedIds = data.Failed.map((failed) => failed.Id);
          this.log('Error pushing session events', data.Failed);
          const failedEntries: SendMessageBatchRequestEntry[] =
            pendingEntries.filter((entry) => failedIds.includes(entry.Id));
          pendingEntries = failedEntries;
        } else {
          pendingEntries = [];
          break;
        }
      } catch (error) {
        this.log('Error pushing session events', error);
      }
    }
    this.failedEntries.push(...pendingEntries);
  }

  public async publishEvents(events: SessionEndedEvent[]) {
    const entries: SendMessageBatchRequestEntry[] = events.map((event) => ({
      Id: `${event.event_properties.request_id}-${event.time}`,
      MessageBody: JSON.stringify(event),
    }));
    return this.publishEntries(entries);
  }

  public async publishFailedEntries() {
    this.log(`Trying publish '${this.failedEntries.length}' failed events`);
    const batches: SendMessageBatchRequestEntry[][] = [];
    while (this.failedEntries.length > 0) {
      const entries = this.failedEntries.splice(0, this.batchSize);
      batches.push(entries);
    }
    const promises: Promise<void>[] = batches.map((entries) =>
      this.publishEntries(entries),
    );
    await Promise.allSettled(promises);
  }
}

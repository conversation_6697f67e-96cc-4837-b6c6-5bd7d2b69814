import { ServerResponse } from 'http';
import {
  contentTypes,
  Methods,
  APITags,
  HTTPRoute,
  NotFound,
  jsonResponse,
  dedent,
  getTokenFromRequest,
  BadRequest,
  Logger,
  SystemQueryParameters,
} from '@browserless.io/browserless';

import { EnterpriseRoutes } from '../../paths.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';
import { AugmentedRequest } from '../../types.js';
import { FileDatabase } from '../../file-system.js';
import { getURLLastSegment } from '../../utils.js';

export interface QuerySchema extends SystemQueryParameters {
  /**
   * Whether to force the deletion of the session even if it has active connections.
   * Defaults to false.
   */
  force?: boolean;
}

export default class SessionDeleteHTTPRoute extends HTTPRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseSessionDeleteRoute;
  auth = true;
  accepts = [contentTypes.any];
  contentTypes = [contentTypes.any];
  concurrency = false;
  description = dedent(`
    > This API is only available for Enterprise and cloud plans. [Contact us for more information here.](https://www.browserless.io/contact/), or [sign-up here](https://www.browserless.io/pricing/).

    Deletes a session and its associated data. This will immediately terminate any active connections to the session if "force" is applied.

    Query Parameters:
    - force (optional): If true, forces deletion even if the session has active connections
  `);
  method = Methods.delete;
  path = [EnterpriseRoutes.sessionById];
  tags = [APITags.management];

  async handler(
    req: AugmentedRequest,
    res: ServerResponse,
    logger: Logger,
  ): Promise<void> {
    const fileSystem = this.fileSystem() as FileDatabase;
    const sessionId = getURLLastSegment(req.parsed)!;
    const force = req.parsed.searchParams.has('force');

    if (!sessionId) {
      throw new NotFound('Session ID is required');
    }

    const apiKey = getTokenFromRequest(req);

    if (!apiKey) {
      throw new BadRequest(
        'A valid API token is required. Please provide a valid token in the query parameters.',
      );
    }

    try {
      const session = await fileSystem.getSession(sessionId);

      if (!session) {
        throw new NotFound(`Session ID "${sessionId}" wasn't found.`);
      }

      if (session.createdBy !== apiKey) {
        throw new NotFound(`Session ID "${sessionId}" wasn't found.`);
      }

      if (session.running && !force) {
        throw new BadRequest(
          `Session ID "${sessionId}" is executing. Please use "force" query parameter to forcefully stop it and remove it.`,
        );
      }

      // Even if session is not found, we still want to try to delete it
      // as it might be in a partially deleted state
      logger.debug('Attempting to delete session:', sessionId);
      await fileSystem.deleteSession(sessionId);

      const response = {
        success: true,
        message: `Session ${sessionId} was successfully removed`,
        sessionId,
        timestamp: new Date().toISOString(),
      };

      return jsonResponse(res, 200, response);
    } catch (error: any) {
      logger.error('Error processing session deletion:', {
        error: error.message,
        stack: error.stack,
        type: error.constructor.name,
      });
      if (error instanceof BadRequest) {
        throw error;
      }
      throw new BadRequest(`Failed to delete session: "${error.message}"`);
    }
  }
}

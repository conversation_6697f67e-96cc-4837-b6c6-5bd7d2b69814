// Basic file-storage that saves to a flat JSON file
import fs from 'fs/promises';
import {
  FileSystem,
  safeParse,
  exists,
  ServerError,
  Logger,
} from '@browserless.io/browserless';
import { BrowserlessToken, Database, SessionMetadata } from './types.js';
import config, { SharedConfig } from './config.js';
import { randomID } from './utils.js';
import path from 'path';
import { SessionEncryption } from './shared/utils/encryption.js';

export interface StateFile {
  sessions: string;
  tokens: string;
}

export type MetaField = Record<string, any>;

/**
 * FileDatabase
 * @TODO Add file encryption at rest with token
 * A default file-based storage implementation to persist
 * important information to disk.
 */
export class FileDatabase extends FileSystem implements Database {
  public config: SharedConfig;
  protected encryption: SessionEncryption;
  protected state: StateFile = { sessions: '', tokens: '' };
  protected stateLoaded: Promise<boolean>;
  protected stateFile?: string;
  protected logger = new Logger(`file-database`);
  protected sessionCheckInterval?: NodeJS.Timeout;

  /**
   * Used for container-specific or short-lived things like ephemeral IDs
   * or browser ID's that are alive for a specific TTL. If it doesn't
   * need to survive a restart, use this inMemoryDB.
   */
  protected inMemoryDB: Map<
    string,
    {
      id: string;
      value: string;
      timerId: NodeJS.Timeout;
      meta?: MetaField;
    }
  > = new Map();

  constructor(config: SharedConfig) {
    super(config);
    this.config = config;
    this.encryption = new SessionEncryption(
      this.config.getSessionEncryptionKey(),
    );

    this.checkExpiredSessions();
    this.stateLoaded = new Promise(async (resolve) => {
      const stateFile = await this.config.getStateFile();
      this.logger.info(`Using file at "${stateFile}" to persist data`);
      this.stateFile = stateFile;

      if (!(await exists(stateFile))) {
        this.logger.info(
          `State file does not yet exist at "${stateFile}", creating one`,
        );
        await fs.writeFile(stateFile, JSON.stringify(this.state));
        return resolve(true);
      }

      const file = await fs.readFile(stateFile, { encoding: 'utf8' });

      if (!file) {
        throw new Error(
          `Couldn't load state from file-path "${stateFile}". Did you forget to mount it?`,
        );
      }

      const state = safeParse(file) as StateFile | null;

      if (!state) {
        throw new Error(
          `Couldn't load state contents from file "${stateFile}". Is this the right file?`,
        );
      }

      this.state = state;

      return resolve(true);
    });
  }

  private async createSessionsDir(): Promise<string> {
    const sessionsDir = this.config.getSessionsDir();
    if (!(await exists(sessionsDir))) {
      this.logger.info(
        `Sessions directory does not exist, creating "${sessionsDir}".`,
      );
      await fs.mkdir(sessionsDir, { recursive: true });
    }
    return sessionsDir;
  }

  private async loaded(): Promise<boolean> {
    return this.stateLoaded;
  }

  private async persist(): Promise<void> {
    if (!this.stateFile) {
      throw new Error(
        `Can't persist changes to ${this.stateFile} until it is loaded`,
      );
    }
    return fs.writeFile(this.stateFile, JSON.stringify(this.state));
  }

  public setupExpiredSessionsPoll() {
    this.logger.info(`Setting up session expiration poller`);
    this.sessionCheckInterval = setInterval(
      () => {
        this.logger.info(`Checking for expired sessions...`);
        this.checkExpiredSessions();
      },
      15 * 60 * 1000,
    );
  }

  public async set(key: keyof StateFile, value: string): Promise<void> {
    await this.loaded();

    this.state[key] = value;

    return this.persist();
  }

  public async get(key: keyof StateFile): Promise<string> {
    await this.loaded();
    return this.state[key];
  }

  public async saveTokens(tokens: BrowserlessToken[]): Promise<void> {
    this.logger.info(`Saving browserless tokens into state`);
    await this.set('tokens', JSON.stringify(tokens));
    this.logger.info(`Tokens saved`);
  }

  public async loadTokens(): Promise<BrowserlessToken[]> {
    this.logger.info(`Attempting to load tokens from state`);
    const tokensString = await this.get('tokens');

    if (tokensString) {
      const tokens = safeParse(tokensString) as BrowserlessToken[] | null;

      if (!tokens) {
        throw new ServerError(
          `Error parsing string as JSON for loading tokens "${tokensString}"`,
        );
      }

      if (tokens.length) {
        this.logger.info(
          `Found prior tokens, returning ${tokens.length} tokens`,
        );
        return tokens;
      }
    }

    this.logger.info(`No prior saved tokens found`);
    return [];
  }

  public saveId(location: string, ttl: number, meta?: MetaField): string {
    this.logger.info(`Saving "${location}" into state`);
    const prior = this.getByValue(location);

    if (prior) {
      this.logger.trace(`Replacing prior location "${location}"`);
      this.deleteId(prior[0]);
    }

    const id = prior ? prior[0] : randomID(16);

    this.inMemoryDB.set(id, {
      id,
      value: location,
      meta,
      timerId: global.setTimeout(() => {
        this.logger.info(`Deleting ID "${id} after ${ttl}ms`);
        this.deleteId(id);
      }, ttl),
    });

    return id;
  }

  public async createSession(
    id: string,
    metadata: Omit<
      SessionMetadata,
      'id' | 'createdAt' | 'userDataDir' | 'expiresAt'
    > & {
      createdBy: string;
    },
  ): Promise<string> {
    this.logger.info(`Saving session "${id}" into durable storage`);
    const createdAt = Date.now();
    const expiresAt = metadata.ttl === 0 ? 0 : metadata.ttl + createdAt;
    const sessionDir = path.join(this.config.getSessionsDir(), id);
    const userDataDir = path.join(sessionDir, 'user-data');

    await fs.mkdir(sessionDir, { recursive: true });
    await fs.mkdir(userDataDir, { recursive: true });

    const sessionMetadata: SessionMetadata = {
      ...metadata,
      id,
      createdAt,
      expiresAt,
      userDataDir,
    };

    await this.updateSession(id, sessionMetadata);

    return id;
  }

  public async updateSession(
    id: string,
    metadata: SessionMetadata,
  ): Promise<string> {
    this.logger.info(`Updating session "${id}" into durable storage`);
    const sessionDir = path.join(this.config.getSessionsDir(), id);
    const metadataPath = path.join(sessionDir, 'metadata.enc');
    const encryptedData = this.encryption.encrypt(id, metadata);
    await fs.writeFile(metadataPath, encryptedData, 'utf-8');

    return id;
  }

  public async setSessionExecuting(
    id: string,
    running: boolean,
  ): Promise<SessionMetadata> {
    this.logger.info(`Setting session "${id}" running state to ${running}`);
    const metadata = await this.getSession(id);
    if (!metadata) {
      throw new Error(`Session with ID "${id}" does not exist`);
    }

    metadata.running = running;

    await this.updateSession(id, metadata);

    return metadata;
  }

  /**
   * Get session metadata by ID
   */
  public async getSession(id: string): Promise<SessionMetadata | null> {
    try {
      const metadataPath = path.join(
        this.config.getSessionsDir(),
        id,
        'metadata.enc',
      );
      const encryptedData = await fs.readFile(metadataPath, 'utf-8');
      const metadata = this.encryption.decrypt(
        id,
        encryptedData,
      ) as SessionMetadata;

      if (metadata.expiresAt !== 0 && metadata.expiresAt < Date.now()) {
        await this.deleteSession(id);
        return null;
      }

      return metadata;
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        return null;
      }
      this.logger.error(`Failed to get session ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete a session and its directory
   */
  public async deleteSession(id: string): Promise<void> {
    try {
      const sessionDir = path.join(this.config.getSessionsDir(), id);

      // Delete the session directory
      await fs.rm(sessionDir, { recursive: true, force: true });
      this.logger.info(`Session "${id}" deleted successfully`);
    } catch (error: any) {
      this.logger.error(`Failed to delete session "${id}": ${error.message}`);
      throw error;
    }
  }

  /**
   * Clean up expired sessions
   */
  protected async checkExpiredSessions(): Promise<void> {
    try {
      const entries = await fs
        .readdir(this.config.getSessionsDir(), {
          withFileTypes: true,
        })
        .catch((err) => {
          if (err.code === 'ENOENT') {
            this.createSessionsDir();
            return [];
          }
          throw err;
        });
      const now = Date.now();

      for (const entry of entries) {
        if (!entry.isDirectory()) continue;

        try {
          const metadata = await this.getSession(entry.name);

          if (metadata && metadata.expiresAt < now) {
            await this.deleteSession(entry.name);
          }
        } catch (error: any) {
          this.logger.error(
            `Failed to process session ${entry.name}: ${error.message}`,
          );
        }
      }
    } catch (error: any) {
      this.logger.error(`Failed to cleanup expired sessions: ${error.message}`);
      throw error;
    }
  }

  public getByValue(val: string) {
    return [...this.inMemoryDB].find(([, { value }]) => value === val);
  }

  public getId(id: string) {
    return this.inMemoryDB.get(id);
  }

  public deleteId(id: string) {
    if (this.inMemoryDB.has(id)) {
      global.clearInterval(this.inMemoryDB.get(id)!.timerId);
    }
    this.emit('deleteId', id);
    return this.inMemoryDB.delete(id);
  }

  public dangerouslyDeleteAllSessions() {
    return fs.rm(this.config.getSessionsDir(), {
      force: true,
      recursive: true,
    });
  }

  public async stop() {
    this.logger.info(`Clearing saved TTLs for exiting.`);
    global.clearInterval(this.sessionCheckInterval);
    [...this.inMemoryDB].forEach(([, { timerId }]) =>
      global.clearTimeout(timerId),
    );
  }
}

export default new FileDatabase(config);

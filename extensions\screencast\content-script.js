/* eslint-disable no-undef */
/**
 * Using short-lived messaging between page and service worker
 * to avoid disconnected port errors in Manifest V3
 */
window.onload = () => {
  // Listen for messages from the page and forward to the service worker
  window.addEventListener('message', (event) => {
    if (event.source === window && event.data.type) {
      chrome.runtime.sendMessage(event.data, (response) => {
        if (response) {
          window.postMessage(response, '*');
        }
      });
    }
  });

  const downloadBlob = async (blobUrl, filename) => {
    try {
      const response = await fetch(blobUrl);
      const blob = await response.blob();
      const downloadLink = document.createElement('a');
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = filename;
      downloadLink.click();
      URL.revokeObjectURL(downloadLink.href);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    if (message.type === 'RECORDING_COMPLETE') {
      const filename = `${message.recordingId}.webm`;

      downloadBlob(message.blobUrl, filename)
        .then(() => {
          sendResponse({ success: true });
        })
        .catch((error) => {
          console.error('Error downloading blob:', error);
          sendResponse({ success: false });
        });
    }

    return true;
  });

  // Set a consistent title for the recording tab
  document.title = 'browserless-screencast';
};

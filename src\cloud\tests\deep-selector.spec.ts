import { expect } from 'chai';
import { Protocol } from 'puppeteer-core';
import { parse, match } from '../../shared/browserql/deep-selector.js';

describe('Deep-selectors', () => {
  describe('parser', () => {
    it('parses out classes', () => {
      expect(parse('< button.active')).to.eql({
        urlPattern: null,
        selectors: [
          {
            tag: 'button',
            id: null,
            classes: ['active'],
            attributes: {},
          },
        ],
      });
    });

    it('parses out multiple classes', () => {
      expect(parse('< button.active.buy')).to.eql({
        urlPattern: null,
        selectors: [
          {
            tag: 'button',
            id: null,
            classes: ['active', 'buy'],
            attributes: {},
          },
        ],
      });
    });

    it('parses out IDs', () => {
      expect(parse('< input#password')).to.eql({
        urlPattern: null,
        selectors: [
          {
            tag: 'input',
            id: 'password',
            classes: [],
            attributes: {},
          },
        ],
      });
    });

    it('parses out data attributes', () => {
      expect(parse('< input[type="text"]')).to.eql({
        urlPattern: null,
        selectors: [
          {
            tag: 'input',
            id: null,
            classes: [],
            attributes: {
              type: 'text',
            },
          },
        ],
      });
    });

    it('parses out data attributes that starts with text', () => {
      expect(parse('< input[class^="cool"]')).to.eql({
        urlPattern: null,
        selectors: [
          {
            tag: 'input',
            id: null,
            classes: [],
            attributes: {
              class: 'cool*',
            },
          },
        ],
      });
    });

    it('parses out data attributes that ends with text', () => {
      expect(parse('< input[class^="cool"]')).to.eql({
        urlPattern: null,
        selectors: [
          {
            tag: 'input',
            id: null,
            classes: [],
            attributes: {
              class: 'cool*',
            },
          },
        ],
      });
    });

    it('parses multiple selection criteria', () => {
      expect(parse('< input#foo.bar[data-test-id*="cool"]')).to.eql({
        urlPattern: null,
        selectors: [
          {
            tag: 'input',
            id: 'foo',
            classes: ['bar'],
            attributes: {
              'data-test-id': '*cool*',
            },
          },
        ],
      });
    });

    it('parses out data attributes with different types of quotes', () => {
      expect(parse('< input[type=`text`]')).to.eql({
        urlPattern: null,
        selectors: [
          {
            tag: 'input',
            id: null,
            classes: [],
            attributes: {
              type: 'text',
            },
          },
        ],
      });
    });

    it('parses very complex queries', () => {
      expect(
        parse(
          '< *example.com/* input#active.new.type[data-testid*=`price`][class="cool"]',
        ),
      ).to.eql({
        urlPattern: '*example.com/*',
        selectors: [
          {
            tag: 'input',
            id: 'active',
            classes: ['new', 'type'],
            attributes: {
              'data-testid': '*price*',
              class: 'cool',
            },
          },
        ],
      });
    });

    describe('Invalid selectors', () => {
      it('throws when selectors do not have the right starting character', () => {
        expect(() => parse('button')).to.throw;
      });

      it('throws when selectors have too many descendants', () => {
        expect(() => parse('< https://www.example.com button button')).to.throw;
      });

      it('throws when * characters are used', () => {
        expect(() => parse('< https://www.example.com *')).to.throw;
      });

      it('throws when : characters are used', () => {
        expect(() => parse('< https://www.example.com :p')).to.throw;
      });

      it('throws when | characters are used', () => {
        expect(() => parse('< https://www.example.com |a')).to.throw;
      });

      it('throws when ~ characters are used', () => {
        expect(() => parse('< https://www.example.com ~')).to.throw;
      });
    });
  });

  describe('match', () => {
    it('matches simple nodes in a list', () => {
      const node = {
        nodeName: 'a',
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< a');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by ID', () => {
      const node = {
        nodeName: 'a',
        attributes: ['id', 'foo'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< a#foo');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by class', () => {
      const node = {
        nodeName: 'a',
        attributes: ['class', 'foo bar'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< a.foo');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by multiple classes', () => {
      const node = {
        nodeName: 'a',
        attributes: ['class', 'foo bar'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< a.foo.bar');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by multiple classes and ID', () => {
      const node = {
        nodeName: 'a',
        attributes: ['class', 'foo bar', 'id', 'baz'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< a#baz.foo.bar');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by attributes', () => {
      const node = {
        nodeName: 'input',
        attributes: ['type', 'text'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< input[type="text"]');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by data attributes that use ^', () => {
      const node = {
        nodeName: 'input',
        attributes: ['data-test', 'text-cool-bro'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< input[data-test^="text"]');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by data attributes that use *', () => {
      const node = {
        nodeName: 'input',
        attributes: ['data-test', 'text-cool-bro'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< input[data-test*="cool"]');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by data attributes that use $', () => {
      const node = {
        nodeName: 'input',
        attributes: ['data-test', 'text-cool-bro'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< input[data-test$="bro"]');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes by data attributes that are simply present', () => {
      const node = {
        nodeName: 'input',
        attributes: ['data-test', ''],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< input[data-test]');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches nodes when no tag is present', () => {
      const node = {
        nodeName: 'input',
        attributes: ['data-test', '', 'class', 'foo bar baz'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< .foo[data-test]');
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches very complex queries', () => {
      const node = {
        nodeName: 'button',
        attributes: [
          'data-test',
          '',
          'class',
          'foo bar baz',
          'id',
          'submit',
          'data-click-action',
          'submit',
        ],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse(
        '< button#submit.foo.bar[data-test][data-click-action*="ubmi"]',
      );
      expect(match(query, nodes)).to.eql(node);
    });

    it('matches the most simple query', () => {
      const node = {
        nodeName: 'button',
        attributes: [
          'data-test',
          '',
          'class',
          'foo bar baz',
          'id',
          'submit',
          'data-click-action',
          'submit',
        ],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< ');
      expect(match(query, nodes)).to.eql(node);
    });

    it('does not match when the HTML element is different', () => {
      const node = {
        nodeName: 'a',
        attributes: ['class', 'foo'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< button.foo');
      expect(match(query, nodes)).to.eql(null);
    });

    it('does not match when a class is missed', () => {
      const node = {
        nodeName: 'a',
        attributes: ['class', 'foo'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< a.foo.bar');
      expect(match(query, nodes)).to.eql(null);
    });

    it('does not match when an ID is missed', () => {
      const node = {
        nodeName: 'a',
        attributes: ['class', 'foo', 'id', 'baz'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< a.foo#boo');
      expect(match(query, nodes)).to.eql(null);
    });

    it('does not match when data-attribute is missed', () => {
      const node = {
        nodeName: 'a',
        attributes: ['class', 'foo', 'id', 'baz', 'data-test-id', ''],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< a[data-not-here]');
      expect(match(query, nodes)).to.eql(null);
    });

    it('does not match when data-attribute has the wrong value', () => {
      const node = {
        nodeName: 'a',
        attributes: ['data-test-id', 'here'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< [data-test-id="not-here"]');
      expect(match(query, nodes)).to.eql(null);
    });

    it('does not match when data-attribute has the wrong start value', () => {
      const node = {
        nodeName: 'a',
        attributes: ['data-test-id', 'not-here'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< [data-test-id^="here"]');
      expect(match(query, nodes)).to.eql(null);
    });

    it('does not match when data-attribute has the wrong end value', () => {
      const node = {
        nodeName: 'a',
        attributes: ['data-test-id', 'here-not'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< [data-test-id$="here"]');
      expect(match(query, nodes)).to.eql(null);
    });

    it('does not match when data-attribute has the wrong * value', () => {
      const node = {
        nodeName: 'a',
        attributes: ['data-test-id', 'foo'],
      } as Protocol.DOM.Node;
      const nodes = [node];
      const query = parse('< [data-test-id*="bar"]');
      expect(match(query, nodes)).to.eql(null);
    });
  });
});

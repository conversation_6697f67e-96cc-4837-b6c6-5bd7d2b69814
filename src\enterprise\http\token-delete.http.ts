import { ServerResponse } from 'http';

import {
  contentTypes,
  Request,
  Methods,
  APITags,
  BadRequest,
  HTTPRoute,
  Unauthorized,
  getTokenFromRequest,
  writeResponse,
  dedent,
  Logger,
} from '@browserless.io/browserless';

import token from '../token.js';
import { BrowserlessEnterpriseRoutes, BrowserlessToken } from '../../types.js';
import { EnterpriseRoutes } from '../../paths.js';
import { getURLLastSegment } from '../../utils.js';

export type ResponseSchema = BrowserlessToken[];

export default class TokenDeleteRoute extends HTTPRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseTokenDeleteRoute;
  accepts = [contentTypes.any];
  auth = false;
  browser = null;
  concurrency = false;
  contentTypes = [contentTypes.text];
  description = dedent(`
    > This API is only available for Enterprise hosted and self-hosted plans. [Contact us for more information here.](https://www.browserless.io/contact/)

    Delete's a token by ID in the path parameter. Requires that browserless be started with a root token, or a TOKEN parameter.`);
  method = Methods.delete;
  path = EnterpriseRoutes.tokenById;
  tags = [APITags.management];
  async handler(
    req: Request,
    res: ServerResponse,
    _logger: Logger,
  ): Promise<void> {
    const tokenId = getURLLastSegment(req.parsed);
    const authorizedBy = getTokenFromRequest(req);

    if (!tokenId) {
      throw new BadRequest(
        `Couldn't find the token to remove: did pass in the ID?`,
      );
    }

    if (!authorizedBy) {
      throw new Unauthorized(`Bad or invalid token`);
    }

    await token.removeTokenById(authorizedBy, tokenId);

    return writeResponse(res, 204, '');
  }
}

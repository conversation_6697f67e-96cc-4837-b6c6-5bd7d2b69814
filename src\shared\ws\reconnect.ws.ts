import {
  APITags,
  Browser<PERSON>eb<PERSON>cket<PERSON>oute,
  <PERSON><PERSON>,
  SystemQueryParameters,
  dedent,
} from '@browserless.io/browserless';
import { Duplex } from 'stream';
import { AugmentedRequest, BrowserlessEnterpriseRoutes } from '../../types.js';
import { EnterpriseChromiumCDP } from '../../browsers/core.js';
import { EnterpriseRoutes } from '../../paths.js';

export interface QuerySchema extends SystemQueryParameters {
  token?: string;
}

export default class ReconnectCDPWebSocketRoute extends BrowserWebsocketRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseReconnectRoute;
  auth = true;

  // @ts-ignore @todo Request extensions
  browser = EnterpriseChromiumCDP;
  concurrency = true;
  description = dedent(
    `Reconnect to an existing Chromium or Chrome session with a library like puppeteer or others that work over chrome-devtools-protocol.`,
  );
  path = [EnterpriseRoutes.reconnect];
  tags = [APITags.browserWS];

  // @ts-ignore @todo Request extensions
  handler = async (
    req: AugmentedRequest,
    socket: Duplex,
    head: Buffer,
    _logger: Logger,
    browser: EnterpriseChromiumCDP,
  ): Promise<void> => browser.proxyWebSocket(req, socket, head);
}

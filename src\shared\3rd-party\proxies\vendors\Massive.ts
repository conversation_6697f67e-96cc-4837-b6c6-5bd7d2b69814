import fetch from 'node-fetch';

import { AugmentedRequest } from '../../../../types.js';
import { SharedConfig } from '../../../../config.js';
import { ProxyProvider } from '../ProxyVendor.js';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { FIVE_MINUTES_IN_MS } from '../../../../utils.js';

export class Massive extends ProxyProvider {
  private credentials: {
    username: string;
    password: string;
  };

  constructor(private config: SharedConfig) {
    super();

    this.credentials = {
      username: this.config.getMassiveUsername()!,
      password: this.config.getMassivePassword()!,
    };

    // If no credentials, is never healthy, so it will never be used
    if (!this.credentials.username || !this.credentials.password) {
      this.log('Massive credentials are not set, this vendor will not be used');
      this.isHealthy = false;
    } else {
      this.healthCheck();
      setInterval(() => this.healthCheck(), FIVE_MINUTES_IN_MS);
    }
  }

  public name = 'Massive';
  public isHealthy = true;
  public lastHealthCheck = Date.now();

  public getProxyURL = (options: AugmentedRequest['__bless__']) => {
    const parts = [this.credentials.username];

    if (options.proxyCountry) {
      parts.push(`country-${options.proxyCountry.toUpperCase()}`);
    }

    if (options.proxyCity) {
      parts.push(`city-${options.proxyCity.toUpperCase()}`);
    }

    return `http://${parts.join('-')}:${this.credentials.password}@network.joinmassive.com:65534`;
  };

  healthCheck = async () => {
    this.log('Checking health of Massive');

    try {
      const proxyURL = `http://${this.credentials.username}:${this.credentials.password}@network.joinmassive.com:65534`;
      const agent = new HttpsProxyAgent(proxyURL);
      const response = await fetch('https://api.ipify.org/?format=json', {
        agent,
      });
      const data = await response.json();

      if (!(data as any).ip) {
        this.log('Massive is not healthy');
        this.isHealthy = false;
        return;
      }

      this.log('Massive is healthy');
      this.isHealthy = true;
      this.lastHealthCheck = Date.now();
    } catch (error) {
      this.log('Massive is not healthy');
      this.isHealthy = false;
    }
  };
}

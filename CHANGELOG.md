> Note that these changes are only for Browserless' cloud, Enterprise and self-hosted Enterprise deployments. For information on the open-source container [please refer to this link](https://github.com/browserless/browserless/blob/main/CHANGELOG.md).

# Latest

- Dependency updates.

# v2.53.1

- Revert live-URL updates until they're backwards compatible.

# v2.53.0

- Dependency updates.g
- New `viewport` API for BQL.
- Consolidation of some internal utilities for consistency sake.
- Big improvements to the `/stealth` routes for CDP libraries.
- You can `solveCaptcha` without specifying the vendor in BQL.
- Support for proxying through Massive with the `MASSIVE_USERNAME` and `MASSIVE_PASSWORD`.
- Fixes an issue in BQL that can cause a browser to stay open when navigating.
- `LiveURL` can now support multi-tabs workflows.
- Testing fixes.

# v2.52.4

- Fixes an issue when deleting sessions

# v2.52.3

- Updates to proxying for future compatibility

# v2.52.2

- Fixes an issue where proxying can cause un-necessary latency

# v2.52.1

- Fix BQL navigation not filtering out iframe page events.

# v2.52.0

- Dependency updates.
- Fixes navigation (`goto`, `back`, `forward`) issues in BQL for goto, forward and backward actions.
- `blockConsentModal` is now default to `false` in BQL as it sometimes causes sites to hang indefinitely when loading.
- New `amazonWaf` for BQL captcha solving.
- New downloading handling forcing all downloads to be in a configurable path by setting a `DOWNLOAD_DIR`.
- Fix `page`'s created by playwright's `context` object hanging.
- Fix issues with CDP-based screen-recording.
- Other fixes, improvements, and reliability enhancements.
- Bumps `puppeteer-core` to `24.12.1`.
- Bumps `playwright-core` to `1.54.1`.
- Drops support for `playwright-core` at `1.49`.
- Supports:
  - puppeteer-core: `24.12.1`
  - playwright-core: `1.41.2`, `1.42.1`, `1.43.1`, `1.44.1`, `1.45.3`, `1.46.1`, `1.47.2`, `1.48.2`, `1.49.1`, `1.50.1`, `1.51.1`, `1.52.0`,
  - Chromium: `139.0.7258.5`
  - Firefox: `140.0.2`
  - Webkit: `26.0`
  - Chrome: `138.0.7204.101` (amd64 only)
  - Edge: `138.0.3351.83` (amd64 only)

import { HttpsProxyAgent } from 'https-proxy-agent';
import fetch from 'node-fetch';

import { AugmentedRequest } from '../../../../types.js';
import { SharedConfig } from '../../../../config.js';
import { ProxyProvider } from '../ProxyVendor.js';
import { FIVE_MINUTES_IN_MS } from '../../../../utils.js';
export class IPRoyal extends ProxyProvider {
  private credentials: {
    username: string;
    password: string;
  };

  constructor(private config: SharedConfig) {
    super();

    this.credentials = {
      username: this.config.getProxyUsername()!,
      password: this.config.getProxyPassword()!,
    };

    // If no credentials, is never healthy, so it will never be used
    if (!this.credentials.username || !this.credentials.password) {
      this.log('IPRoyal credentials are not set, this vendor will not be used');
      this.isHealthy = false;
    } else {
      this.healthCheck();
      setInterval(() => this.healthCheck(), FIVE_MINUTES_IN_MS);
    }
  }

  public name = 'IPRoyal';
  public isHealthy = true;
  public lastHealthCheck = Date.now();

  public getProxyURL = (options: AugmentedRequest['__bless__']) => {
    const parts = [this.credentials.password, 'streaming-1', 'skipispstatic-1'];

    if (options.proxyCountry) {
      parts.push(`country-${options.proxyCountry}`);
    }

    if (options.proxyCity) {
      parts.push(`city-${options.proxyCity}`);
    }

    if (options.proxyState) {
      parts.push(`state-${options.proxyState}`);
    }

    if (options.proxySticky) {
      parts.push(`session-${options.proxySticky}_lifetime-30m`);
    }

    return `http://${this.credentials.username}:${parts.join('_')}@geo.iproyal.com:12321`;
  };

  healthCheck = async () => {
    this.log('Checking health of IPRoyal');

    try {
      const proxyURL = `http://${this.credentials.username}:${this.credentials.password}@geo.iproyal.com:12321`;
      const agent = new HttpsProxyAgent(proxyURL);
      const response = await fetch('https://api.ipify.org/?format=json', {
        agent,
      });
      const data = await response.json();

      if (!(data as any).ip) {
        this.log('IPRoyal is not healthy');
        this.isHealthy = false;
        return;
      }

      this.log('IPRoyal is healthy');
      this.isHealthy = true;
      this.lastHealthCheck = Date.now();
    } catch (error) {
      this.log('IPRoyal is not healthy');
      this.isHealthy = false;
    }
  };
}

import { ServerResponse } from 'http';
import {
  contentTypes,
  Methods,
  APITags,
  dedent,
  SystemQueryParameters,
  BrowserHTTPRoute,
  CDPLaunchOptions,
  Logger,
  WaitForEventOptions,
  WaitForFunctionOptions,
  WaitForSelectorOptions,
  waitForEvent as waitForEvt,
  waitForFunction as waitForFn,
  sleep,
  bestAttemptCatch,
  bestAttempt,
  BadRequest,
  noop,
  ChromiumCDP,
} from '@browserless.io/browserless';
import { PassThrough } from 'stream';
import { Page } from 'puppeteer-core';
import JSZip from 'jszip';

import { EnterpriseRoutes } from '../../paths.js';
import { AugmentedRequest, BrowserlessEnterpriseRoutes } from '../../types.js';
import { getBooleanRequestParameter } from '../../utils.js';

export interface QuerySchema extends SystemQueryParameters {
  launch?: CDPLaunchOptions | string;
}

export interface BodySchema {
  bestAttempt?: bestAttempt;

  /**
   * The URL of the site you want to archive.
   */
  url: string;

  /**
   * An optional goto parameter object for considering when the page is done loading.
   */
  gotoOptions?: Parameters<Page['goto']>[1];
  waitForEvent?: WaitForEventOptions;
  waitForFunction?: WaitForFunctionOptions;
  waitForSelector?: WaitForSelectorOptions;
  waitForTimeout?: number;
  headers?: Record<string, string>;

  /**
   * Whether to include all linked resources (images, CSS, JS) in a zip file.
   * When true, the response will be a zip file containing the HTML and all resources.
   * When false or not provided, the response will be the raw content (default behavior).
   */
  includeResources?: boolean;
}

export interface ResponseSchema {
  /**
   * The HTML content of the page.
   */
  html: string;
}

export default class ChromiumExportPostRoute extends BrowserHTTPRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromiumExportPostRoute;
  auth = true;
  accepts = [contentTypes.json];
  browser = ChromiumCDP;
  contentTypes = [contentTypes.json];
  concurrency = true;
  description = dedent(`
    > This API is only available for Enterprise, hosted and self-hosted plans. [Contact us for more information here.](https://www.browserless.io/contact/)

    Exports a webpage to a PDF or image format. This API is useful for generating reports, screenshots, or PDFs of web pages.
  `);
  method = Methods.post;
  path = [EnterpriseRoutes.chromiumExport, EnterpriseRoutes.export];
  tags = [APITags.browserAPI];

  async handler(
    req: AugmentedRequest,
    res: ServerResponse,
    logger: Logger,
    browser: ChromiumCDP,
  ): Promise<void> {
    const body = req.body as BodySchema;
    logger.info('Export API invoked with body:', body);

    if (!body) {
      throw new BadRequest(`Couldn't parse JSON body`);
    }

    const {
      url,
      headers = {},
      waitForEvent,
      waitForFunction,
      waitForSelector,
      waitForTimeout,
      gotoOptions,
    } = body;

    if (!url) {
      throw new BadRequest(`"url" property is required.`);
    }

    const page = await browser.newPage();

    const resources = new Map<
      string,
      { buffer: Buffer; contentType: string }
    >();
    const baseUrl = new URL(url);

    // Set up response listener if we need to include resources
    let responseListener: ((response: any) => void) | null = null;

    const includeResources = getBooleanRequestParameter(body.includeResources);
    const bestAttempt = getBooleanRequestParameter(body.bestAttempt);

    if (includeResources) {
      responseListener = async (response) => {
        try {
          if (response.status() !== 200) {
            return;
          }

          const responseUrl = response.url();
          const resourceType = response.request().resourceType();

          if (
            !['document', 'stylesheet', 'image', 'font', 'script'].includes(
              resourceType,
            )
          ) {
            return;
          }

          const responseUrlObj = new URL(responseUrl);
          if (
            resourceType !== 'document' &&
            responseUrlObj.origin !== baseUrl.origin
          ) {
            return;
          }

          if (resourceType === 'document' && responseUrl !== url) {
            return;
          }

          // Get the response body as buffer
          const buffer = await response.buffer().catch(() => null);
          if (buffer) {
            const contentType =
              response.headers()['content-type'] || 'application/octet-stream';
            resources.set(responseUrl, { buffer, contentType });
            logger.debug(`Captured resource: ${responseUrl} (${resourceType})`);
          }
        } catch (error: any) {
          logger.error(`Failed to process resource: ${error.message}`);
        }
      };

      page.on('response', responseListener);
    }

    if (Object.keys(headers).length) {
      await page.setExtraHTTPHeaders(headers);
    }

    // Navigate to the URL and get the response
    const gotoResponse = await page
      .goto(url, gotoOptions)
      .catch(bestAttemptCatch(bestAttempt));

    if (!gotoResponse) {
      throw new BadRequest(`Failed to get response from ${url}`);
    }

    if (waitForTimeout) {
      await sleep(waitForTimeout).catch(bestAttemptCatch(bestAttempt));
    }

    if (waitForFunction) {
      await waitForFn(page as any, waitForFunction).catch(
        bestAttemptCatch(bestAttempt),
      );
    }

    if (waitForEvent) {
      await waitForEvt(page as any, waitForEvent).catch(
        bestAttemptCatch(bestAttempt),
      );
    }

    if (waitForSelector) {
      const { selector, ...opts } = waitForSelector;
      await (page as any)
        .waitForSelector(selector, opts)
        .catch(bestAttemptCatch(bestAttempt));
    }

    if (includeResources) {
      await page.waitForNetworkIdle({ idleTime: 1000 }).catch((err) => {
        logger.warn(`Network idle timeout: ${err.message}`);
      });
    }

    // Get the content type from the response
    const contentType =
      gotoResponse.headers()['content-type'] || 'application/octet-stream';
    const filename = url.split('/').pop() || 'file';

    if (includeResources && contentType.includes('text/html')) {
      try {
        // Create a zip file with all resources
        const zip = new JSZip();

        // Get the HTML content and process it to update resource URLs
        const processedHtml = await page.evaluate((baseUrlString) => {
          const baseUrl = new URL(baseUrlString);
          const getRelativePath = (absoluteUrl: string) => {
            try {
              const url = new URL(absoluteUrl);
              if (url.origin !== baseUrl.origin) {
                return absoluteUrl;
              }

              let path = url.pathname;
              if (path.startsWith('/')) {
                path = path.substring(1);
              }

              return path || 'index.html';
            } catch (error: any) {
              return absoluteUrl;
            }
          };

          document.querySelectorAll('img').forEach((img) => {
            const src = img.getAttribute('src');
            if (src) {
              try {
                img.setAttribute(
                  'src',
                  getRelativePath(new URL(src, baseUrl).href),
                );
              } catch (error) {}
            }
          });

          document
            .querySelectorAll('link[rel="stylesheet"]')
            .forEach((link) => {
              const href = link.getAttribute('href');
              if (href) {
                try {
                  link.setAttribute(
                    'href',
                    getRelativePath(new URL(href, baseUrl).href),
                  );
                } catch (error) {}
              }
            });

          document.querySelectorAll('script').forEach((script) => {
            const src = script.getAttribute('src');
            if (src) {
              try {
                script.setAttribute(
                  'src',
                  getRelativePath(new URL(src, baseUrl).href),
                );
              } catch (error) {}
            }
          });

          document
            .querySelectorAll('link[rel="icon"], link[rel="shortcut icon"]')
            .forEach((link) => {
              const href = link.getAttribute('href');
              if (href) {
                try {
                  link.setAttribute(
                    'href',
                    getRelativePath(new URL(href, baseUrl).href),
                  );
                } catch (error) {}
              }
            });

          return document.documentElement.outerHTML;
        }, url);

        zip.file('index.html', processedHtml);

        for (const [resourceUrl, resource] of resources.entries()) {
          try {
            if (resourceUrl === url) {
              continue; // Skip the main document as we've already added it
            }

            const resourceUrlObj = new URL(resourceUrl);
            let relativePath = resourceUrlObj.pathname;
            if (relativePath.startsWith('/')) {
              relativePath = relativePath.substring(1);
            }

            if (!relativePath) {
              continue;
            }

            // Create directories if needed
            zip.file(relativePath, resource.buffer);
            logger.debug(`Added to zip: ${relativePath}`);
          } catch (error: any) {
            logger.error(
              `Failed to add ${resourceUrl} to zip: ${error.message}`,
            );
          }
        }

        const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

        // Set appropriate headers for zip download
        res.setHeader('Content-Type', 'application/zip');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="${new URL(url).hostname}.zip"`,
        );

        // Create a PassThrough stream and pipe the buffer to the response
        const readStream = new PassThrough();
        readStream.end(zipBuffer);
        await new Promise((r) => readStream.pipe(res).once('close', r));
      } catch (error: any) {
        logger.error(`Failed to create zip file: ${error.message}`);
        throw new BadRequest(`Failed to create zip file: ${error.message}`);
      }
    } else {
      let buffer: Buffer;
      if (contentType.includes('pdf')) {
        // For PDFs, generate a proper PDF buffer
        buffer = (await page.pdf()) as Buffer;
      } else {
        // For other content types, get the response buffer
        const rawBuffer = await (gotoResponse as any).buffer();
        if (!rawBuffer || rawBuffer.length === 0) {
          throw new BadRequest('No content received from the page');
        }
        buffer = Buffer.from(rawBuffer) as Buffer;
      }

      // Set appropriate headers based on content type
      res.setHeader('Content-Type', contentType);

      // If the content type is not text/html, set as attachment
      if (!contentType.includes('text/html')) {
        const extension = contentType.includes('pdf')
          ? 'pdf'
          : contentType.split('/')[1] || 'bin';
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="${filename}.${extension}"`,
        );
      }

      // Create a PassThrough stream and pipe the buffer to the response
      const readStream = new PassThrough();
      readStream.end(buffer);
      await new Promise((r) => readStream.pipe(res).once('close', r));
    }

    if (includeResources && responseListener) {
      page.off('response', responseListener);
    }

    page.close().catch(noop);
  }
}

import EventSource from 'eventsource';

import { createLogger } from '@browserless.io/browserless';
import {
  cloudUnitPlanNumbers,
  ISharedFleetGetPayload,
  ISharedFleetMemoized,
} from '../types.js';
import { CloudAPI } from './api.js';

export class CloudCredits {
  private credits: ISharedFleetMemoized = {};
  private hasHydrated = false;
  private retryInterval?: NodeJS.Timeout;
  private log = createLogger('credits');

  constructor(
    private api: CloudAPI,
    private serverEvents: EventSource,
  ) {
    this.hydrate();

    this.serverEvents.addEventListener('accountAdded', ({ data }) => {
      const credit: ISharedFleetGetPayload = JSON.parse(data);
      this.addCredits(credit);
    });
    this.serverEvents.addEventListener('accountRemoved', ({ data }) => {
      this.removeCredit(data);
    });
  }

  private hydrate() {
    const start = Date.now();
    this.api
      .getCloudCredits()
      .then((credits) => {
        this.hasHydrated = true;
        this.log(`Credits cache hydrated in ${Date.now() - start}`);
        this.set(credits);
      })
      .catch((error) => {
        this.log('Credits failed to fetch', error);
        this.log('Retrying in one minute');
        this.retryInterval = setTimeout(this.hydrate, 60 * 1000);
      });
  }

  private addCredits(newCredits: ISharedFleetGetPayload) {
    for (const apiKey of Object.keys(newCredits)) {
      if (!this.credits[apiKey]) {
        const newCredit = newCredits[apiKey];
        this.credits[apiKey] = [
          newCredit[0] === 'Infinity' ? Infinity : newCredit[0],
          newCredit[1],
        ];
      }
    }
  }

  private removeCredit(apiKey: string) {
    delete this.credits[apiKey];
  }

  public decrby = (apiKey: string, unitsOrSeconds: number) => {
    this.credits[apiKey] = this.credits[apiKey] ?? [0, false];
    this.credits[apiKey][0] -= unitsOrSeconds;

    return this.credits[apiKey][0];
  };

  public set(newCredits: ISharedFleetGetPayload) {
    this.hasHydrated = true;
    this.credits = Object.entries(newCredits).reduce(
      (accum, [apiKey, sharedFleetBitMap]) => {
        accum[apiKey] = [
          sharedFleetBitMap[0] === 'Infinity' ? Infinity : sharedFleetBitMap[0],
          sharedFleetBitMap[1],
        ];
        return accum;
      },
      {} as ISharedFleetMemoized,
    );
  }

  public hasCredits(apiKey: string): boolean {
    const credit = this.credits[apiKey];

    if (!this.hasHydrated) {
      return true;
    }

    if (credit) {
      return credit[0] > 0;
    }

    return false;
  }

  public isPaidAccount(apiKey: string) {
    if (!this.hasHydrated) {
      return true;
    }
    const planNumber = this.getPlanNumber(apiKey);
    return planNumber > cloudUnitPlanNumbers.free;
  }

  public isCloudUnitAccount(apiKey: string) {
    if (!this.hasHydrated) {
      return true;
    }
    const planNumber = this.getPlanNumber(apiKey);
    return planNumber >= cloudUnitPlanNumbers.free;
  }

  public getPlanNumber(apiKey: string): number {
    const credit = this.credits[apiKey];

    // Return largest number since most checks are
    // based upon gte, lte checks.
    if (!this.hasHydrated) {
      return Infinity;
    }

    if (credit) {
      return credit[1];
    }

    return -1;
  }

  public stop() {
    if (this.retryInterval) {
      this.log(`Stopping retry interval`);
      clearInterval(this.retryInterval);
    }
  }
}

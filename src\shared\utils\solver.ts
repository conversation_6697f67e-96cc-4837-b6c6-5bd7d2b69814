import { Connection, Protocol } from 'puppeteer-core';
import { Logger, sleep } from '@browserless.io/browserless';
import { NodeWebSocketTransport } from 'puppeteer-core/lib/esm/puppeteer/node/NodeWebSocketTransport.js';
import { getRandomArbitrary } from '../../utils.js';

const cloudflareCheckBoxInterval = 2000;

const requestIsNavigation = ({
  requestId,
  loaderId,
  type,
}: {
  requestId: string;
  loaderId: string;
  type: string;
}) => requestId === loaderId && type === 'Document';

export const solveCloudFlare = async (
  cdp: Connection,
  { hostname }: URL,
  logger: Logger,
): Promise<number> =>
  new Promise(async (resolve, reject) => {
    const removeListener = () => {
      clearInterval(veryHumanInterval);
      cdp.off('Network.responseReceived', responseReceived);
    };

    const veryHumanInterval = setInterval(async () => {
      let response: Protocol.Target.GetTargetsResponse | Error;
      try {
        response = await cdp.send('Target.getTargets');
      } catch (err) {
        logger.error(`Error communicating with CDP, rejecting`, err);
        response = new Error(`Failed to send Target.getTargets to CDP`);
      }

      if (response instanceof Error) {
        removeListener();
        return reject(response);
      }
      const iframe = response.targetInfos.find((t) => t.type === 'iframe');
      if (iframe) {
        logger.debug(`Found challenge iframe, attempting to click`);
        const { port } = new URL(cdp.url());
        const webSocketDebuggerUrl = `ws://0.0.0.0:${port}/devtools/page/${iframe.targetId}`;
        const iframeCDP = new Connection(
          webSocketDebuggerUrl,
          await NodeWebSocketTransport.create(webSocketDebuggerUrl),
        );
        await Promise.all([
          iframeCDP.send('Page.enable'),
          iframeCDP.send('Network.enable'),
          iframeCDP.send('DOM.enable'),
        ]);
        const x = 30 + getRandomArbitrary(1, 10);
        const y = 30 + getRandomArbitrary(1, 10);
        const clickCount = 1;
        logger.trace(`Input position is ${x} x ${y}`);
        await iframeCDP.send('Input.dispatchMouseEvent', {
          type: 'mouseMoved',
          x,
          y,
        });
        await iframeCDP.send('Input.dispatchMouseEvent', {
          type: 'mousePressed',
          button: 'left',
          clickCount,
          force: 0.125,
          x,
          y,
        });
        await iframeCDP.send('Input.dispatchMouseEvent', {
          type: 'mouseReleased',
          button: 'left',
          clickCount,
          force: 0.125,
          x,
          y,
        });
        iframeCDP.dispose();
      }
    }, cloudflareCheckBoxInterval);

    const responseReceived = async (
      params: Protocol.Network.ResponseReceivedEvent,
    ) => {
      const { url: responseUrl, status: responseStatus } = params.response;
      logger.trace('Network Response', responseStatus, responseUrl);

      if (requestIsNavigation(params)) {
        const { hostname: navigatingHost } = new URL(responseUrl);
        if (hostname === navigatingHost) {
          logger.debug('Browser Navigation', params.response.status);
          if (params.response.status < 300) {
            removeListener();
            return resolve(params.response.status);
          }
        }
        return;
      }
    };

    cdp.on('Network.responseReceived', responseReceived);
  });

export const solveDataDome = async (
  cdp: Connection,
  url: URL,
  logger: Logger,
): Promise<void> =>
  new Promise(async (resolve, _reject) => {
    const responseReceived = async (
      params: Protocol.Network.ResponseReceivedEvent,
    ) => {
      const { url: responseUrl, status: responseStatus } = params.response;
      const removeListener = () =>
        cdp.off('Network.responseReceived', responseReceived);
      logger.trace('Network Response', responseStatus, responseUrl);

      if (requestIsNavigation(params)) {
        const { hostname: navigatingHost } = new URL(responseUrl);
        if (url.hostname === navigatingHost) {
          logger.debug('Browser Navigation', params.response.status);
          if (params.response.status < 300) {
            removeListener();
            return resolve();
          } else {
            logger.debug(`Sleeping and retrying in 5 seconds...`);
            removeListener();
            setTimeout(() => resolve(solveDataDome(cdp, url, logger)));
          }
        }
        return;
      }
    };

    cdp.on('Network.responseReceived', responseReceived);
  });

export const solveWalMart = async (
  cdp: Connection,
  url: URL,
  logger: Logger,
): Promise<void> =>
  new Promise(async (resolve, _reject) => {
    const removeListener = () => {
      cdp.off('Network.responseReceived', responseReceived);
    };
    const responseReceived = async (
      params: Protocol.Network.ResponseReceivedEvent,
    ) => {
      const { url: responseUrl, status: responseStatus } = params.response;
      logger.trace('Network Response', responseStatus, responseUrl);
      if (requestIsNavigation(params)) {
        const { hostname: navigatingHost } = new URL(responseUrl);
        if (url.hostname === navigatingHost) {
          logger.debug('Browser Navigation', params.response.status);

          if (params.response.status < 300) {
            if (params.response.url.includes('walmart.com/blocked')) {
              // Still blocked...
              return;
            }
            removeListener();
            return resolve();
          }
        }
        return;
      }
    };
    cdp.on('Network.responseReceived', responseReceived);
    const { result } = await cdp.send('Runtime.evaluate', {
      expression: `JSON.stringify(document.querySelector('#px-captcha').getBoundingClientRect())`,
      returnByValue: true,
    });
    if (result.type === 'string') {
      logger.debug('Found captcha, pressing and holding');
      await sleep(5000); // wait for frame to load
      const { top, left } = JSON.parse(result.value);
      const x = left + getRandomArbitrary(20, 150);
      const y = top + getRandomArbitrary(20, 50);
      logger.debug(`Input position is ${x} x ${y}`);
      await cdp.send('Input.dispatchMouseEvent', {
        type: 'mouseMoved',
        x,
        y,
      });
      await cdp.send('Input.dispatchMouseEvent', {
        type: 'mousePressed',
        button: 'left',
        clickCount: 1,
        force: 0.125,
        x,
        y,
      });
      await sleep(8500);
      await cdp.send('Input.dispatchMouseEvent', {
        type: 'mouseReleased',
        button: 'left',
        clickCount: 1,
        force: 0.125,
        x,
        y,
      });
    }
  });

export const unblock = async (
  cdp: Connection,
  url: string,
  logger: Logger,
): Promise<void> =>
  new Promise(async (resolve) => {
    let requestedURL = new URL(url);

    await Promise.all([
      cdp.send('Page.enable'),
      cdp.send('Network.enable'),
      cdp.send('DOM.enable'),
    ]);

    const removeListener = () => {
      cdp.off('Network.responseReceived', responseReceived);
      cdp.off('Network.requestWillBeSent', requestWillBeSent);
    };

    const requestWillBeSent = async (
      params: Protocol.Network.RequestWillBeSentEvent,
    ) => {
      if (
        params.redirectHasExtraInfo &&
        params.redirectResponse?.headers.location &&
        requestIsNavigation(
          params as { requestId: string; loaderId: string; type: string },
        )
      ) {
        logger.trace(
          'Network Redirect',
          params.redirectResponse.url,
          params.redirectResponse.headers.location,
        );

        requestedURL = new URL(params.redirectResponse?.headers.location);
      }
    };

    const responseReceived = async (
      params: Protocol.Network.ResponseReceivedEvent,
    ) => {
      const { url: responseUrl, status: responseStatus } = params.response;
      logger.trace('Network Response', responseStatus, responseUrl);

      if (requestIsNavigation(params)) {
        const { hostname: navigatingHost } = new URL(responseUrl);
        if (requestedURL.hostname === navigatingHost) {
          logger.debug('Browser Navigation', params.response.status);

          if (params.response.status < 300) {
            if (params.response.url.includes('walmart.com/blocked')) {
              logger.debug(`Site is walmart protected`);
              removeListener();
              return resolve(solveWalMart(cdp, requestedURL, logger));
            }
            removeListener();
            return resolve();
          }
        }
        return;
      }

      if (responseUrl.includes('challenges.cloudflare.com')) {
        logger.debug(`Site is cloudflare protected`);
        removeListener();
        await solveCloudFlare(cdp, requestedURL, logger);
        return resolve();
      }

      if (responseUrl.includes('ips.js')) {
        logger.debug(`Site is data-dome protected, solving.`);
        removeListener();
        return resolve(solveDataDome(cdp, requestedURL, logger));
      }
    };

    cdp.on('Network.responseReceived', responseReceived);
    cdp.on('Network.requestWillBeSent', requestWillBeSent);
    cdp.send('Page.navigate', { url });

    return;
  });

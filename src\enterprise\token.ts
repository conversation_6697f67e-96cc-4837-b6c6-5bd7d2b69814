import { Config, id, Token, Unauthorized } from '@browserless.io/browserless';
import config from '../config.js';
import { BrowserlessToken, Permissions } from '../types.js';
import fileSystem, { FileDatabase } from '../file-system.js';

class EnterpriseToken extends Token {
  config: Config;
  fileSystem: FileDatabase;
  tokens: Array<BrowserlessToken> = [];

  constructor(config: Token['config'], state: EnterpriseToken['fileSystem']) {
    super(config);
    this.config = config;
    this.fileSystem = state;

    this.start();
  }

  private async start() {
    const tokens = await this.getTokens();
    const rootToken = this.config.getToken();

    // On a clean install populate the "root" TOKEN
    // into state so we can use it again later
    if (!tokens.length && rootToken) {
      tokens.push({
        createdAt: Date.now(),
        createdBy: rootToken,
        permissions: Permissions.admin,
        token: rootToken,
      });
      await this.fileSystem.saveTokens(tokens);
    }
  }

  public getTokens = async () => this.fileSystem.loadTokens();

  public removeTokenById = async (authorizedBy: string, tokenId: string) => {
    const tokens = await this.getTokens();
    const authorizedToken = tokens.find(({ token }) => token === authorizedBy);

    if (!authorizedToken) {
      throw new Unauthorized(`Token ${authorizedBy} is not valid`);
    }

    if (authorizedToken.permissions !== Permissions.admin) {
      throw new Unauthorized(`Token ${authorizedBy} is not valid`);
    }

    const removed = tokens.filter(({ token }) => token !== tokenId);

    await this.fileSystem.saveTokens(removed);
  };

  public createNewToken = async ({
    authorizedBy,
    permission,
    token = id(),
    label,
  }: {
    authorizedBy: string;
    label?: string;
    permission: Permissions;
    token?: string;
  }) => {
    const tokens = await this.getTokens();
    const authorizedToken = tokens.find(({ token }) => token === authorizedBy);

    if (!authorizedToken) {
      throw new Unauthorized(`Token ${authorizedBy} is not valid`);
    }

    if (authorizedToken.permissions !== Permissions.admin) {
      throw new Unauthorized(`Token ${authorizedBy} is not valid`);
    }

    const newToken = {
      createdAt: Date.now(),
      createdBy: authorizedBy,
      label,
      permissions: permission,
      token,
    };

    tokens.push(newToken);
    await this.fileSystem.saveTokens(tokens);

    return newToken;
  };

  public isAdminToken = async (tokenId: string) => {
    const tokens = await this.getTokens();
    const found = tokens.find((t) => t.token === tokenId) ?? {
      permissions: Permissions.public,
    };

    return found.permissions === Permissions.admin;
  };
}

export default new EnterpriseToken(config, fileSystem);

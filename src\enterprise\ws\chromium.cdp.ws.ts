import {
  APITags,
  BrowserWebsocketRoute,
  CDPLaunchOptions,
  Logger,
  SystemQueryParameters,
  WebsocketRoutes,
} from '@browserless.io/browserless';
import { Duplex } from 'stream';
import { AugmentedRequest, BrowserlessEnterpriseRoutes } from '../../types.js';
import { EnterpriseChromiumCDP } from '../../browsers/core.js';

export interface QuerySchema extends SystemQueryParameters {
  launch?: CDPLaunchOptions | string;
  record?: boolean;
}

export default class EnterpriseChromiumCDPWebSocketRoute extends BrowserWebsocketRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromiumCDPWebSocketRoute;
  auth = true;

  // @ts-ignore @todo Request extensions
  browser = EnterpriseChromiumCDP;
  concurrency = true;
  description = `Launch and connect to Chromium with a library like puppeteer or others that work over chrome-devtools-protocol.`;
  path = [WebsocketRoutes['/'], WebsocketRoutes.chromium];
  tags = [APITags.browserWS];

  // @ts-ignore @todo Request extensions
  async handler(
    req: AugmentedRequest,
    socket: Duplex,
    head: Buffer,
    _logger: Logger,
    browser: EnterpriseChromiumCDP,
  ): Promise<void> {
    return browser.proxyWebSocket(req, socket, head);
  }
}

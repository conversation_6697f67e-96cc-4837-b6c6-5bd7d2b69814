import { noop, sleep } from '@browserless.io/browserless';
import { expect } from 'chai';
import Sinon from 'sinon';
import puppeteer from 'puppeteer-core';

import BrowserlessCloud from '../browserless.js';
import { CloudAPI } from '../api.js';
import { CloudUnitConfig } from '../config.js';
import { CloudCredits } from '../credit.js';
import { CloudHooks } from '../hooks.js';
import { randomID } from '../../utils.js';
import { ServerEvents } from '../server-events.js';
import { SessionEndedEventsPublisher } from '../../shared/utils/session-ended-publisher.js';
import { cloudUnitPlanNumbers } from '../../types.js';
import fileSystem from '../../file-system.js';

const token = randomID(16);

const apiMock = Sinon.createStubInstance(CloudAPI, {
  getCloudCredits: Sinon.stub().resolves({
    [token]: ['Infinity', 1],
  }) as any,
  saveCloudMetrics: Sinon.stub().resolves({}) as any,
});

const serverEventsMock = Sinon.createStubInstance(ServerEvents, {
  addEventListener: Sinon.stub().resolves({}) as any,
});

const sessionEventsPublisherMock = Sinon.createStubInstance(
  SessionEndedEventsPublisher,
  {
    publishEvents: Sinon.stub().resolves({}) as any,
    publishFailedEntries: Sinon.stub().resolves({}) as any,
  },
);

describe('Cloud-Unit HTTP APIs', function () {
  let browserless: BrowserlessCloud;

  const start = async ({
    config: configOverride,
    credits: creditsOverride,
    api: apiOverride,
    sessionEndedPublisher: sessionEndedPublisherOverride,
    hooks: hooksOverride,
  }: {
    config?: CloudUnitConfig;
    credits?: CloudCredits;
    api?: CloudAPI;
    sessionEndedPublisher?: SessionEndedEventsPublisher;
    hooks?: CloudHooks;
  } = {}) => {
    const api = apiOverride ?? apiMock;
    const credits = creditsOverride ?? new CloudCredits(api, serverEventsMock);
    const config = configOverride ?? new CloudUnitConfig(credits);
    const sessionEndedPublisher =
      sessionEndedPublisherOverride ?? sessionEventsPublisherMock;
    const hooks =
      hooksOverride ??
      new CloudHooks(config, credits, api, '', sessionEndedPublisher);

    browserless = new BrowserlessCloud({
      api,
      credits,
      hooks,
      config,
      sessionEndedPublisher,
    });

    await browserless.start();
    return browserless;
  };

  afterEach(async () => {
    await browserless.stop().catch(noop);
  });

  ['/metrics', '/metrics/total', '/pressure', '/config', '/sessions'].forEach(
    (route) => {
      it(`should 404 on ${route}`, async () => {
        await start();
        const { status } = await fetch(
          `http://localhost:3000${route}?token=${token}`,
          {
            method: 'GET',
          },
        );
        expect(status).to.equal(404);
      });
    },
  );

  it('calls the API to get available tokens on startup', async () => {
    await start();
    expect(apiMock.getCloudCredits.called).to.be.true;
  });

  it('records credits after sessions have ran', async () => {
    const credits = new CloudCredits(apiMock, serverEventsMock);
    const config = new CloudUnitConfig(credits);
    const hooks = new CloudHooks(
      config,
      credits,
      apiMock,
      '',
      sessionEventsPublisherMock,
    );
    await start({ credits, hooks });
    const browser = await puppeteer.connect({
      browserWSEndpoint: `ws://localhost:3000/chromium?token=${token}`,
    });
    await sleep(1000);
    await browser.close();
    await sleep(100);

    // @ts-ignore
    expect(hooks.currentUsageStats).to.haveOwnProperty(token);
    expect(sessionEventsPublisherMock.publishEvents.calledOnce).to.equal(true);
  });

  it('save credits to the API when shutting down', async () => {
    const bless = await start();
    const browser = await puppeteer.connect({
      browserWSEndpoint: `ws://localhost:3000/chromium?token=${token}`,
    });
    await sleep(1000);
    await browser.close();
    await sleep(100);
    await bless.stop();
    expect(apiMock.saveCloudMetrics.called).to.be.true;
    expect(sessionEventsPublisherMock.publishFailedEntries.called).to.be.true;
  });

  it('should allow BQL calls', async () => {
    await start();
    const res = await fetch(
      `http://localhost:3000/chromium/bql?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          query: 'mutation a { goto(url: "https://example.com") { status } }',
        }),
      },
    );

    expect(res.status).to.equal(200);
    const body = await res.json();
    expect(body).to.eql({ data: { goto: { status: 200 } } });
  });

  it('should allow BQL calls to Chrome', async () => {
    await start();
    const res = await fetch(`http://localhost:3000/chrome/bql?token=${token}`, {
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      body: JSON.stringify({
        query: `mutation Example { goto(url: "https://one.one.one.one", waitUntil: networkIdle) { status } text { text } }`,
      }),
    });

    expect(res.status).to.equal(200);
    const body = await res.json();
    expect(body.data.goto.status).to.equal(200);
    expect(body.data.text.text).to.include('The free app that makes');
  });

  it('should limit BQL reconnections', async () => {
    await start();
    const res = await fetch(
      `http://localhost:3000/chromium/bql?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          query:
            'mutation a { goto(url: "https://example.com", waitUntil: networkIdle) { status } reconnect(timeout: 31000) { browserQLEndpoint } }',
        }),
      },
    );

    expect(res.status).to.equal(200);
    const body = await res.json();
    expect(body.errors[0].message).to.eql(
      'Reconnect time exceeds your current plans limits.',
    );
  });

  it('should allow BQL re-connections when within limits', async () => {
    const token = randomID(16);
    const apiMock = Sinon.createStubInstance(CloudAPI, {
      getCloudCredits: Sinon.stub().resolves({
        [token]: ['Infinity', cloudUnitPlanNumbers.oneHundredEightyThousand],
      }) as any,
      saveCloudMetrics: Sinon.stub().resolves({}) as any,
    });
    const credits = new CloudCredits(apiMock, serverEventsMock);
    const config = new CloudUnitConfig(credits);
    const hooks = new CloudHooks(
      config,
      credits,
      apiMock,
      '',
      sessionEventsPublisherMock,
    );
    await start({ credits, hooks });
    const res = await fetch(
      `http://localhost:3000/chromium/bql?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          query:
            'mutation a { goto(url: "https://example.com", waitUntil: networkIdle) { status } reconnect(timeout: 30000) { browserQLEndpoint } }',
        }),
      },
    );

    expect(res.status).to.equal(200);
    const body = await res.json();
    expect(body.data.reconnect.browserQLEndpoint);
  });

  it('should allow chromium /unblock calls', async () => {
    await start();
    const { status } = await fetch(
      `http://localhost:3000/chromium/unblock?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          url: 'https://example.com',
          cookies: false,
          content: false,
          screenshot: false,
        }),
      },
    );

    expect(status).to.equal(200);
  });

  it('allows running /function API calls', async () => {
    await start();
    const res = await fetch(
      `http://localhost:3000/chromium/function?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/javascript',
        },
        body: `
          export default async ({ page }) => {
            await page.goto('https://example.com');
            return await page.title();
          }
        `,
      },
    );
    const body = await res.text();
    expect(res.status).to.equal(200);
    expect(body);
  });

  it('should allow chrome /unblock calls', async () => {
    await start();
    const res = await fetch(
      `http://localhost:3000/chrome/unblock?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          url: 'https://example.com',
          cookies: false,
          content: false,
          screenshot: false,
        }),
      },
    );

    expect(res.status).to.equal(200);
  });

  it('should return a null `browserWSEndpoint` by default', async () => {
    await start();
    const res = await fetch(`http://localhost:3000/unblock?token=${token}`, {
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://example.com',
        cookies: false,
        content: false,
        screenshot: false,
      }),
    }).then((r) => r.json());

    expect(res).to.have.property('browserWSEndpoint', null);
  });

  it('should allow connecting to a `browserWSEndpoint` when specified', async () => {
    await start();
    const { browserWSEndpoint } = await fetch(
      `http://localhost:3000/unblock?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          url: 'https://example.com',
          cookies: false,
          content: false,
          screenshot: false,
          browserWSEndpoint: true,
          ttl: 1000,
        }),
      },
    ).then((r) => r.json());
    const withToken = browserWSEndpoint + `?token=${token}`;
    const browser = await puppeteer.connect({ browserWSEndpoint: withToken });
    const page = await browser.newPage();
    const response = await page.goto('https://example.com', {
      waitUntil: 'networkidle2',
    });
    expect(browser);
    expect(response?.status()).to.equal(200);
    await browser.close();
  });

  it('should not allow connections when the TTL is hit', async () => {
    await start();
    const { browserWSEndpoint } = await fetch(
      `http://localhost:3000/unblock?token=${token}`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          url: 'https://example.com',
          cookies: false,
          content: false,
          screenshot: false,
          browserWSEndpoint: true,
          ttl: 10,
        }),
      },
    ).then((r) => r.json());
    await sleep(15);

    return puppeteer.connect({ browserWSEndpoint }).catch((e) => {
      expect(e);
    });
  });

  it('should not allow third party proxy in free plan', async () => {
    const api = Sinon.createStubInstance(CloudAPI, {
      getCloudCredits: Sinon.stub().resolves({
        [token]: ['Infinity', cloudUnitPlanNumbers.free],
      }) as any,
      saveCloudMetrics: Sinon.stub().resolves({}) as any,
    });
    await start({ api });

    const response = await fetch(
      `http://localhost:3000/unblock?token=${token}&--proxy-server=serverUrl`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          url: 'https://example.com',
          cookies: false,
          content: false,
          screenshot: false,
          browserWSEndpoint: true,
          ttl: 1000,
        }),
      },
    ).then((r) => r.text());

    expect(response).to.not.be.null;
    expect(response).to.contain(
      'Only paid cloud-unit plans can utilize a third-party proxy',
    );
  });

  it('should not allow timeout greater than 60,000ms for free accounts', async () => {
    const api = Sinon.createStubInstance(CloudAPI, {
      getCloudCredits: Sinon.stub().resolves({
        [token]: ['Infinity', cloudUnitPlanNumbers.free],
      }) as any,
      saveCloudMetrics: Sinon.stub().resolves({}) as any,
    });
    await start({ api });

    const response = await fetch(
      `http://localhost:3000/unblock?token=${token}&timeout=61000`,
      {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          url: 'https://example.com',
          cookies: false,
          content: false,
          screenshot: false,
          browserWSEndpoint: true,
          ttl: 1000,
        }),
      },
    ).then((r) => r.text());

    expect(response).to.not.be.null;
    expect(response).to.contain(
      'Timeout must be a integer between 1 and 60,000',
    );
  });

  describe('Sessions workflow', () => {
    afterEach(() => fileSystem.dangerouslyDeleteAllSessions());

    it('should not allow TTLs greater than 1 day for free accounts', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });

      const response = await fetch(
        `http://localhost:3000/session?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            ttl: 2 * 24 * 60 * 60 * 1000,
          }),
        },
      ).then((r) => r.text());
      expect(response).to.not.be.null;
      expect(response).to.contain(
        'ttl cannot exceed the maximum retention time of ******** milliseconds for your plan',
      );
    });

    it('should format connect urls with appropriate options', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });

      const response = await fetch(
        `http://localhost:3000/session?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            ttl: 1 * 24 * 60 * 60 * 1000,
            proxy: {
              country: 'us',
            },
          }),
        },
      ).then((r) => r.json());
      console.log('>>>', response.connect);
      expect(response.connect).to.contain('proxy=residential');
      expect(response.connect).to.contain('proxyCountry=us');
      expect(response.connect).to.contain('proxySticky=true');
    });
  });
});

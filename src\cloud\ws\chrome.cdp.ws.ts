import { WebsocketRoutes } from '@browserless.io/browserless';
import { EnterpriseChromeCDP } from '../../browsers/core.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';
import {
  QuerySchema,
  default as ChromiumWebsocketRoute,
} from './chromium.cdp.ws.js';

export { QuerySchema };

export default class ChromeWebsocketRoute extends ChromiumWebsocketRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromeCDPWebSocketRoute;
  browser = EnterpriseChromeCDP;
  path = [WebsocketRoutes.chrome];
}

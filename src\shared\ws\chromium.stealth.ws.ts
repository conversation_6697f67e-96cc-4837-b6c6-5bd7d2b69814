import {
  APITags,
  BrowserWebsocketRoute,
  CDPLaunchOptions,
  Logger,
  SystemQueryParameters,
  dedent,
} from '@browserless.io/browserless';
import { Duplex } from 'stream';
import { ChromiumStealthBrowser } from '../../browsers/chromium.stealth.js';
import { EnterpriseRoutes } from '../../paths.js';
import { AugmentedRequest, BrowserlessEnterpriseRoutes } from '../../types.js';

export interface QuerySchema extends SystemQueryParameters {
  launch?: CDPLaunchOptions | string;
}

export default class ChromiumStealthWebsocketRoute extends BrowserWebsocketRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromiumStealthWebsocketRoute;
  auth = true;

  // @ts-ignore @todo Request extensions
  browser = ChromiumStealthBrowser;
  concurrency = true;
  description = dedent(`
    > This API is only available for Enterprise and Cloud-unit plans. [Contact us for more information here.](https://www.browserless.io/contact/), or [sign-up here](https://www.browserless.io/pricing/).

    Launch and connect to Stealthy Chromium with a library like puppeteer or others that work over chrome-devtools-protocol for scraping in a more stealth-like fashion.`);
  path = [EnterpriseRoutes.stealth, EnterpriseRoutes.chromiumStealth];
  tags = [APITags.browserWS];

  // @ts-ignore @todo Request extensions
  handler = async (
    req: AugmentedRequest,
    socket: Duplex,
    head: Buffer,
    _logger: Logger,
    browser: ChromiumStealthBrowser,
  ): Promise<void> => browser.proxyWebSocket(req, socket, head);
}

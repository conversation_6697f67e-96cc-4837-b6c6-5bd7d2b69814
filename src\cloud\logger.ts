import { Logger, getTokenFromRequest } from '@browserless.io/browserless';
import { AugmentedRequest } from '../types.js';

const customerIdLength = 14;

export class CloudLogger extends Logger {
  constructor(
    protected prefix: string,
    protected request?: AugmentedRequest,
  ) {
    super(prefix, request);
  }

  protected getTrackingId() {
    if (!this.request) {
      return '';
    }
    let token = getTokenFromRequest(this.request);
    if (!token) {
      return this.request.__bless__.requestId;
    }
    const isLegacyToken = token.length > 32;
    if (!isLegacyToken) {
      token = token.slice(0, customerIdLength);
    }
    return `${token} ${this.request.__bless__.requestId}`;
  }

  public trace = (...messages: unknown[]) => {
    this._trace(`${this.getTrackingId()} %o`, [...messages]);
  };

  public debug = (...messages: unknown[]) => {
    this._debug(`${this.getTrackingId()} %o`, [...messages]);
  };

  public info = (...messages: unknown[]) => {
    this._info(`${this.getTrackingId()} %o`, [...messages]);
  };

  public warn = (...messages: unknown[]) => {
    this._warn(`${this.getTrackingId()} %o`, [...messages]);
  };

  public error = (...messages: unknown[]) => {
    this._error(`${this.getTrackingId()} %o`, [...messages]);
  };

  public fatal = (...messages: unknown[]) => {
    this._fatal(`${this.getTrackingId()} %o`, [...messages]);
  };
}

let mediaRecorder = null;
let chunks = [];
let tabId;
let recordingId;

// Listen for messages from the service worker
chrome.runtime.onMessage.addListener((message) => {
  // Start recording
  if (message.name == 'startRecording') {
    tabId = message.body.tabId;
    recordingId = message.body.recordingId;
    startRecording(message.body.currentTab.id);
  }

  // Stop recording
  if (message.name == 'stopRecording') {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
      mediaRecorder.stop();
    } else {
      chrome.runtime.sendMessage({
        type: 'REC_BLOB_READY',
        id: message.body.recordingId,
        error: 'MediaRecorder not active',
      });
    }
  }
});

const blobToString = async (blob) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.onerror = reject;
    reader.readAsBinaryString(blob);
  });
};

const convertBlobToBase64 = (blob) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onloadend = () => {
      const base64data = reader.result;
      resolve(base64data);
    };
  });
};

function startRecording(currentTabId) {
  // We have to keep track of the started recordings because the service worker is ephemeral,
  // and a started recording may not be stopped before the service worker is restarted.
  chrome.storage.local.get(['activeRecordings'], (result) => {
    const activeRecordings = result.activeRecordings || {};
    // add the current service-worker recording to the list of active recordings
    activeRecordings[recordingId] = {
      tabId,
      startTime: Date.now(),
    };
    chrome.storage.local.set({ activeRecordings });
  });

  // Prompt user to choose screen or window (automatically chosen if only one tab is open)
  chrome.desktopCapture.chooseDesktopMedia(
    ['audio', 'tab'],
    function (streamId) {
      if (streamId == null) {
        chrome.runtime.sendMessage({
          type: 'REC_BLOB_READY',
          id: recordingId,
          error: 'User cancelled screen selection',
        });
        return;
      }

      // Once user has chosen screen or window, create a stream from it and start recording
      navigator.mediaDevices
        .getUserMedia({
          audio: {
            mandatory: {
              chromeMediaSource: 'desktop',
              chromeMediaSourceId: streamId,
            },
          },
          video: {
            mandatory: {
              chromeMediaSource: 'desktop',
              chromeMediaSourceId: streamId,
            },
          },
        })
        .then((stream) => {
          mediaRecorder = new MediaRecorder(stream);

          chunks = [];

          mediaRecorder.ondataavailable = function (e) {
            chunks.push(e.data);
            saveChunkToStorage(e.data);
          };

          mediaRecorder.onstop = async function () {
            const blobFile = new Blob(chunks, { type: 'video/webm' });
            const url = URL.createObjectURL(blobFile);

            // Stop all tracks of stream
            stream.getTracks().forEach((track) => track.stop());

            // Send the blob URL back to the original page
            chrome.tabs.sendMessage(currentTabId, {
              type: 'RECORDING_COMPLETE',
              blobUrl: url,
              currentTabId,
              recordingId,
            });

            // Clean up storage
            chrome.storage.local.get(['activeRecordings'], (result) => {
              const activeRecordings = result.activeRecordings || {};
              delete activeRecordings[recordingId];
              chrome.storage.local.set({ activeRecordings });

              // Also clean up stored chunks
              chrome.storage.local.remove([`recording_chunks_${recordingId}`]);
            });
          };

          // Set up periodic dataavailable events to ensure chunks are captured
          // even if the service worker restarts
          mediaRecorder.start(10000); // Capture chunks every 10 seconds
        })
        .catch((error) => {
          console.error('Error starting recording:', error);
          chrome.runtime.sendMessage({
            type: 'REC_BLOB_READY',
            id: recordingId,
            error: error.message || 'Failed to start recording',
          });
        })
        .finally(async () => {
          // After all setup, focus on previous tab (where the recording was requested)
          await chrome.tabs.update(currentTabId, {
            active: true,
            selected: true,
          });
        });
    },
  );
}

// Save recording chunk to storage. Service workers are ephemeral, so we need to save chunks to storage.
async function saveChunkToStorage(chunk) {
  try {
    const base64 = await convertBlobToBase64(chunk);

    // Get existing chunks
    chrome.storage.local.get([`recording_chunks_${recordingId}`], (result) => {
      const storedChunks = result[`recording_chunks_${recordingId}`] || [];
      storedChunks.push(base64);

      // Store updated chunks
      chrome.storage.local.set({
        [`recording_chunks_${recordingId}`]: storedChunks,
      });
    });
  } catch (error) {
    console.error('Error saving chunk to storage:', error);
  }
}


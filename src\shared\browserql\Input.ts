import { sleep } from '@browserless.io/browserless';
import { Connection } from 'puppeteer-core';
import Queue from 'queue';

import { path, pressKey, randomEnglishLetter, sleepRandom } from './cursor.js';
import { getRandomArbitrary } from '../../utils.js';

export default class InputHelper {
  private lastMousePosition = { x: 0, y: 0 };
  private queue = new Queue({
    concurrency: 1,
    autostart: true,
  });

  public type = async ({
    text,
    cdp,
    humanLike,
    delay,
  }: {
    text: string;
    cdp: Connection;
    humanLike: boolean;
    delay: [number, number];
  }): Promise<void> =>
    new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const items = [...text];
          for (const item of items) {
            const keyEvents = pressKey(item);
            await sleepRandom(...delay);
            await cdp.send(`Input.dispatchKeyEvent`, keyEvents.keyDown as any);
            await cdp.send(`Input.dispatchKeyEvent`, keyEvents.keyUp as any);

            if (humanLike) {
              const oneIn50 = getRandomArbitrary(0, 15) === 0;

              if (oneIn50) {
                // "clumsiness"
                const key = randomEnglishLetter();
                const keyEvents = pressKey(key);

                await cdp.send(
                  `Input.dispatchKeyEvent`,
                  keyEvents.keyDown as any,
                );
                await sleepRandom(500, 700);

                await cdp.send(
                  `Input.dispatchKeyEvent`,
                  keyEvents.keyUp as any,
                );
                await sleepRandom(200, 450);

                const backSpaceEvents = pressKey('Backspace');

                await cdp.send(
                  `Input.dispatchKeyEvent`,
                  backSpaceEvents.keyDown as any,
                );
                await sleepRandom(100, 150);
                await cdp.send(
                  `Input.dispatchKeyEvent`,
                  backSpaceEvents.keyUp as any,
                );
              }
            }
          }
          return resolve();
        } catch (e) {
          return reject(e);
        }
      });
    });

  public click = async ({
    cdp,
    x,
    y,
  }: {
    cdp: Connection;
    x: number;
    y: number;
  }): Promise<void> =>
    new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          await sleep(getRandomArbitrary(5, 25));
          await cdp.send('Input.dispatchMouseEvent', {
            type: 'mousePressed',
            button: 'left',
            clickCount: 1,
            force: 0.125,
            x,
            y,
          });
          await sleep(getRandomArbitrary(5, 25));
          await cdp.send('Input.dispatchMouseEvent', {
            type: 'mouseReleased',
            button: 'left',
            clickCount: 1,
            force: 0.125,
            x,
            y,
          });
          return resolve();
        } catch (e) {
          return reject();
        }
      });
    });

  public move = async ({
    cdp,
    x,
    y,
    smooth,
  }: {
    cdp: Connection;
    x: number;
    y: number;
    smooth: boolean;
  }): Promise<void> =>
    new Promise((resolve, reject) => {
      this.queue.push(async () => {
        const to = { x, y };
        try {
          if (smooth) {
            const vectors = path(this.lastMousePosition, to);
            for (const v of vectors) {
              await cdp.send('Input.dispatchMouseEvent', {
                type: 'mouseMoved',
                x: v.x,
                y: v.y,
              });
            }
          } else {
            await cdp.send('Input.dispatchMouseEvent', {
              type: 'mouseMoved',
              ...to,
            });
          }
          this.lastMousePosition = to;
          return resolve();
        } catch (e) {
          return reject(e);
        }
      });
    });
}

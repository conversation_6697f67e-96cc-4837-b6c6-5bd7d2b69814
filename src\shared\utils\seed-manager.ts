import { randomBytes } from 'crypto';

/**
 * Seed Manager for consistent randomness across protection scripts
 * Provides a single source of truth for seed generation and noise functions
 */
export class SeedManager {
  private static instance: SeedManager;
  private readonly seed: number;
  private readonly baseNoise: Float64Array;

  private constructor() {
    this.seed = randomBytes(4).readUInt32LE(0);
    this.baseNoise = new Float64Array(1000).map(
      () => Math.sin(this.seed * 12.9898) * Math.cos(Date.now() * 78.233),
    );
  }

  public static getInstance(): SeedManager {
    if (!SeedManager.instance) {
      SeedManager.instance = new SeedManager();
    }
    return SeedManager.instance;
  }

  /**
   * Get the shared seed value
   */
  public getSeed(): number {
    return this.seed;
  }

  /**
   * Create a noise function for DOM measurements
   */
  public createDOMRectNoise(): (value: number) => number {
    return (value: number) => {
      const idx = Math.abs(Math.floor(value * 100)) % 1000;
      return (this.baseNoise[idx] - Math.floor(this.baseNoise[idx])) * 0.02;
    };
  }

  /**
   * Create a noise function for Math operations
   */
  public createMathNoise(): (value: number) => number {
    return (value: number) => {
      const noise = Math.sin(this.seed * 12.9898) * Math.cos(value * 78.233);
      return (noise - Math.floor(noise)) * 1e-10;
    };
  }

  /**
   * Create a noise function for profile values
   */
  public createProfileNoise(): (value: number, range: number) => number {
    return (value: number, range: number) => {
      const idx = Math.abs(Math.floor(value * 100)) % 1000;
      return (
        value + (this.baseNoise[idx] - Math.floor(this.baseNoise[idx])) * range
      );
    };
  }

  /**
   * Create a noise function for hardware profile simulation
   */
  public createHardwareNoise(): (index: number) => number {
    return (index: number) => {
      const idx = Math.abs(index) % 1000;
      return (this.baseNoise[idx] - Math.floor(this.baseNoise[idx])) * 2 - 1;
    };
  }

  /**
   * Create a noise function for canvas operations
   */
  public createCanvasNoise(): (index: number) => number {
    return (index: number) => {
      const idx = Math.abs(index) % 1000;
      return (this.baseNoise[idx] - Math.floor(this.baseNoise[idx])) * 2 - 1;
    };
  }
}

// Export singleton instance
export const seedManager = SeedManager.getInstance();

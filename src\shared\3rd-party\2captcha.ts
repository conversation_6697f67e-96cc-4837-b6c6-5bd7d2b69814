import TwoCaptcha from '@2captcha/captcha-solver';

const apiKey = '076e225c66e1cb71d99431885489885b';
const pollingInterval = 100;
const solver = new TwoCaptcha.Solver(apiKey, pollingInterval);

export const solveHCaptcha = async (
  pageurl: string,
  sitekey: string,
  userAgent: string,
): ReturnType<typeof solver.hcaptcha> => {
  const result = await solver.hcaptcha({
    pageurl,
    sitekey,
    userAgent,
  });

  return result;
};

export const solveRecaptcha = async (
  pageurl: string,
  googlekey: string,
  userAgent: string,
  options?: {
    datas?: string; // Google Service Captcha data-s parameter
    action?: string; // reCAPTCHA v3 action
    enterprise?: boolean; // Enterprise reCAPTCHA
    invisible?: boolean; // Invisible reCAPTCHA v2
  },
): ReturnType<typeof solver.recaptcha> => {
  const { datas, action, enterprise, invisible } = options || {};

  // Build solve parameters
  const solveParams: any = {
    pageurl,
    googlekey,
    userAgent,
  };

  // Add optional parameters
  if (datas) {
    solveParams.datas = datas;
  }
  if (action) {
    solveParams.action = action;
  }
  if (enterprise) {
    solveParams.enterprise = 1;
  }
  if (invisible) {
    solveParams.invisible = 1;
  }

  const result = await solver.recaptcha(solveParams);

  return result;
};

export const solveRecaptchaV3 = async (
  pageurl: string,
  googlekey: string,
  userAgent: string,
): ReturnType<typeof solver.recaptcha> => {
  const result = await solver.recaptcha({
    pageurl,
    googlekey,
    userAgent,
    min_score: 0.7,
  });

  return result;
};

export const solveCloudflare = async (
  pageurl: string,
  sitekey: string,
): ReturnType<typeof solver.cloudflareTurnstile> => {
  const result = await solver.cloudflareTurnstile({
    pageurl,
    sitekey,
  });

  return result;
};

export const solveFriendlyCaptcha = async (
  pageurl: string,
  sitekey: string,
): ReturnType<typeof solver.friendlyCaptcha> => {
  const result = await solver.friendlyCaptcha({
    pageurl,
    sitekey,
  });

  return result;
};

export const solveNormalCaptcha = async (
  imageData: string,
): Promise<{ data: string }> => {
  const result = await solver.imageCaptcha({
    body: imageData,
  });

  return { data: result.data };
};

export const solveTextCaptcha = async (
  textQuestion: string,
): Promise<{ data: string }> => {
  const result = await solver.text({
    textcaptcha: textQuestion,
    lang: 'en', // Default to English, can be made configurable
  });

  return { data: result.data };
};

export async function solveGeeTest(
  pageurl: string,
  gt: string,
  challenge: string,
) {
  const result = await solver.geetest({
    pageurl,
    gt,
    challenge,
  });

  return result as unknown as {
    geetest_challenge: string;
    geetest_validate: string;
    geetest_seccode: string;
  };
}

export async function solveAmazonWAF({
  website,
  captchaScript,
  challengeScript,
  context,
  iv,
  key,
}: {
  website: string;
  captchaScript: string;
  challengeScript: string;
  context: string;
  iv: string;
  key: string;
}): Promise<{
  status: number;
  data: {
    captcha_voucher: string;
    existing_token: string;
  };
}> {
  const result = await solver.amazonWaf({
    pageurl: website,
    sitekey: key,
    iv,
    context,
    captcha_script: captchaScript,
    challenge_script: challengeScript,
  });

  return result as unknown as {
    status: number;
    data: {
      captcha_voucher: string;
      existing_token: string;
    };
    id: string;
  };
}

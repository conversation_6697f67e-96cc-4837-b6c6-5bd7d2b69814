import { ISharedFleetGetPayload, ISharedFleetPutPayload } from '../types.js';

export class CloudAPI {
  constructor(
    private baseURL: string,
    private token: string,
  ) {}

  public saveCloudMetrics(
    stats: ISharedFleetPutPayload,
  ): Promise<ISharedFleetGetPayload> {
    return fetch(new URL('/v2/system/usage', this.baseURL).href, {
      method: 'PUT',
      body: JSON.stringify(stats),
      headers: {
        Authorization: this.token,
        'Content-Type': 'application/json',
      },
    }).then(async (r) => {
      if (r.ok) {
        return r.json();
      }
      throw new Error(`${r.status}: ${r.statusText}`);
    });
  }

  public getCloudCredits(): Promise<ISharedFleetGetPayload> {
    return fetch(new URL('/v2/system/usage', this.baseURL).href, {
      method: 'GET',
      headers: {
        Authorization: this.token,
        'Content-Type': 'application/json',
      },
    }).then(async (r) => {
      if (r.ok) {
        return r.json();
      }
      throw new Error(`${r.status}: ${r.statusText}`);
    });
  }
}

/**
 * Utility function to detect programming language from user-agent strings
 */
export function detectProgrammingLanguage(
  userAgent?: string,
): string | undefined {
  if (!userAgent) return undefined;

  if (/python|requests|urllib/i.test(userAgent)) return 'python';
  if (/node|axios|fetch/i.test(userAgent)) return 'node.js';
  if (/ruby|faraday/i.test(userAgent)) return 'ruby';
  if (/java|okhttp/i.test(userAgent)) return 'java';
  if (/go-http-client/i.test(userAgent)) return 'go';
  if (/php|guzzle/i.test(userAgent)) return 'php';

  return 'unknown';
}

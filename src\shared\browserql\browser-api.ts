import {
  BadRequest,
  CDPJSONPayload,
  dedent,
  fetchJson,
  getTokenFromRequest,
  id,
  Logger,
  makeExternalURL,
  sleep,
} from '@browserless.io/browserless';
import {
  catchError,
  combineLatest,
  filter,
  firstValueFrom,
  from,
  fromEvent,
  map,
  timeout as throwTimeout,
  TimeoutError,
} from 'rxjs';
import fetch from 'node-fetch';
import sharp from 'sharp';
import { Connection, CookieSameSite, Protocol } from 'puppeteer-core';
import { NodeWebSocketTransport } from 'puppeteer-core/lib/esm/puppeteer/node/NodeWebSocketTransport.js';
import {
  logLiveUrlInvoked,
  logReconnectUrlCreated,
  logUrlVisit,
} from './utils/url-logging.js';

import * as captchas from '../3rd-party/capsolver.js';
import { ChromiumStealthBrowser } from '../../browsers/chromium.stealth.js';
import {
  callFriendlyCaptchaCallback,
  callRecaptchaCallback,
  findIframeURLs,
  getCaptchaImg,
  getContent,
  getNodeText,
  getQuerySelector,
  getQuerySelectorAll,
  getRecaptchaWidgetInfo,
  getTextCaptchaQuestion,
  mapSelector,
  removeAttributes,
  removeSelectors,
  scrollIntoView,
  selectFn,
  selectorIsChecked,
  setCaptchaValue,
  setContent,
  setFriendlyCaptchaResponse,
  setHcaptchaResponse,
  setInputValue,
  setTextCaptchaAnswer,
} from './browser.utils.js';
import { SharedConfig } from '../../config.js';
import {
  blockSelectors,
  getProxyURL,
  getRandomArbitrary,
  getWAFData,
  identifyCaptchaBQL,
  isMatch,
  modalBlocker,
  recaptchaSiteKeyReducer,
  setGeetestResponse,
  truncate,
} from '../../utils.js';
import InputHelper from './Input.js';
import {
  FetchInterception,
  operators,
  PDFOptions,
  ScreenshotOpts,
  solveTypes,
  verifyTypes,
  waitUntil,
} from './types.js';
import { ChromeStealthBrowser } from '../../browsers/chrome.stealth.js';
import {
  asyncPoll,
  cssUnitToInches,
  fetchWithProxy,
  findPatternForRequest,
  getDebuggerURL,
  networkMatchesPattern,
} from './utils.js';
import {
  cloudFlare,
  geetest,
  friendlyCaptcha,
  hCaptcha,
  isDeepSelector,
  paperFormats,
  recaptcha,
} from './constants.js';
import { match, parse } from './deep-selector.js';
import fileSystem from '../../file-system.js';
import { BQLRequest } from './bql-post.chromium.http.js';
import {
  captchaLoggerFactory,
  detectCloudflareType,
  getBoundingClientRect,
  waitForSelector,
} from '../utils/cdp-utils.js';
import { RecaptchaWidgetInfo } from '../../types.js';

export class BrowserQLAPI {
  #browser: ChromiumStealthBrowser | ChromeStealthBrowser;
  #config: SharedConfig;
  #defaultTimeout = 30_000;
  #errored = false;
  #fetchEnabled = false;
  #fetchInterceptors: FetchInterception[] = [];
  #humanLike = false;
  #blockConsentModals = false;
  #inputHelper = new InputHelper();
  #jsEnabled = true;
  #logger: Logger;
  #port: string;
  #req: BQLRequest;
  #requests: Map<
    string,
    { url: string; type: string; headers: object; method: string }
  > = new Map();
  #responses: Map<
    string,
    {
      id: string;
      url: string;
      type: string;
      headers: object;
      method: string;
      status: number;
    }
  > = new Map();
  #running: boolean = true;
  #timers: NodeJS.Timeout[] = [];
  #ttl = 0;
  #webSocketDebuggerUrl?: string;
  browserCDP!: Connection;
  mainCDP!: Connection;

  constructor(
    browser: ChromiumStealthBrowser | ChromeStealthBrowser,
    config: SharedConfig,
    logger: Logger,
    humanLike: boolean,
    blockConsentModals: boolean,
    req: BQLRequest,
  ) {
    this.#browser = browser;
    this.#config = config;
    this.#logger = logger;
    this.#humanLike = humanLike;
    this.#blockConsentModals = blockConsentModals;
    this.#req = req;

    const browserWS = this.#browser.wsEndpoint()!;
    const { port } = new URL(browserWS);
    this.#port = port;
  }

  public async start() {
    if (!this.mainCDP) {
      const browserWS = this.#browser.wsEndpoint()!;
      const targets = (await fetchJson(
        `http://127.0.0.1:${this.#port}/json/list`,
      )) as Array<CDPJSONPayload>;
      const { webSocketDebuggerUrl } = targets.find(
        (t) => t.type === 'page' && !t.url.startsWith('chrome'),
      ) as CDPJSONPayload;
      this.#logger.debug(webSocketDebuggerUrl, `Got URL, connecting...`);
      this.#webSocketDebuggerUrl = webSocketDebuggerUrl;
      this.mainCDP = new Connection(
        webSocketDebuggerUrl,
        await NodeWebSocketTransport.create(webSocketDebuggerUrl),
      );
      await this.cdpSetup(this.mainCDP);

      if (this.#blockConsentModals) {
        // Inject a script to block modals, to be run on every page load
        await this.mainCDP.send('Page.addScriptToEvaluateOnNewDocument', {
          source: `(() => {
            document.addEventListener("DOMContentLoaded", function() {
              (${modalBlocker.toString()})(${JSON.stringify(await blockSelectors())})
            });
          })()`,
        });
      }

      this.browserCDP = new Connection(
        browserWS,
        await NodeWebSocketTransport.create(browserWS),
      );
    }
  }

  private async cdpSetup(cdp: Connection) {
    if (!this.#running) {
      return;
    }
    this.#logger.trace('Setting up CDP domains');

    await Promise.all([
      cdp.send('Page.enable'),
      cdp.send('Network.enable'),
      cdp.send('DOM.enable'),
    ]);
    await cdp.send('Page.setLifecycleEventsEnabled', { enabled: true });

    cdp.on('Network.requestWillBeSent', (params) => {
      this.#requests.set(params.requestId, {
        method: params.request.method,
        url: params.request.url,
        type: (params.type || '').toLowerCase(),
        headers: params.request.headers,
      });
    });

    cdp.on('Network.responseReceived', (params) => {
      const request = this.#requests.get(params.requestId);
      if (!request) {
        return;
      }

      this.#responses.set(params.requestId, {
        id: params.requestId,
        status: params.response.status,
        url: params.response.url,
        type: (params.type || '').toLowerCase(),
        headers: params.response.headers,
        method: request.method,
      });
    });
  }

  /**
   * Extract captcha type and subtype from reCAPTCHA widget information for accurate logging
   */
  private extractRecaptchaTypeAndSubtype(widgetInfo: any): {
    captchaType: string;
    captchaSubtype?: string;
  } {
    if (!widgetInfo) {
      return { captchaType: 'recaptcha' };
    }

    const isEnterprise = widgetInfo.enterprise || false;
    const version = widgetInfo.version;

    if (version === 'v3') {
      return {
        captchaType: 'recaptcha',
        captchaSubtype: isEnterprise ? 'v3-enterprise' : 'v3',
      };
    } else if (version === 'v2_invisible') {
      return {
        captchaType: 'recaptcha',
        captchaSubtype: isEnterprise ? 'invisible-enterprise' : 'invisible',
      };
    } else {
      return {
        captchaType: 'recaptcha',
        captchaSubtype: isEnterprise ? 'v2-enterprise' : 'v2-checkbox',
      };
    }
  }

  /**
   * Extract Cloudflare captcha type and subtype for accurate logging
   * Uses shared detection logic from cdp-utils
   */
  private async extractCloudflareTypeAndSubtype(
    cdp: Connection,
  ): Promise<{ captchaType: string; captchaSubtype?: string }> {
    try {
      // Use shared Cloudflare detection logic
      const { value: cloudflareInfo } = await this.evaluate({
        cdp,
        content: `(${detectCloudflareType.toString()})()`,
      });

      // Use detection result if available, otherwise default to turnstile
      const captchaSubtype = cloudflareInfo?.type || 'turnstile';
      return {
        captchaType: 'cloudflare',
        captchaSubtype,
      };
    } catch (error) {
      this.#logger.debug(
        'Failed to get detailed Cloudflare info, using fallback',
        error,
      );
    }

    // Fallback for any errors during detection - default assumption for undetectable cases
    return {
      captchaType: 'cloudflare',
      captchaSubtype: 'turnstile',
    };
  }

  private async solveCloudFlare({
    wait,
    timeout,
    cdp = this.mainCDP,
  }: {
    wait: boolean;
    timeout: number;
    cdp?: Connection;
  }) {
    this.#logger.debug(`Attempting to find and solve Cloudflare`);
    const start = Date.now();
    const {
      value: { website },
    } = await this.evaluate({
      cdp,
      content: `{ website: location.href };`,
    });
    const apiToken = getTokenFromRequest(this.#req);

    // Initialize logger with fallback type, will be updated with detailed type after detection
    let captchaLogger = captchaLoggerFactory(
      apiToken!,
      'cloudflare',
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    try {
      // Update logger with detailed type and subtype based on detected widget info
      const { captchaType, captchaSubtype } =
        await this.extractCloudflareTypeAndSubtype(cdp);
      captchaLogger = captchaLoggerFactory(
        apiToken!,
        captchaType,
        website,
        this.#logger,
        this.#config,
        'bql',
        captchaSubtype,
      );
      captchaLogger.attempt();

      const res = await this.click({
        selector: cloudFlare.selector,
        wait,
        timeout,
        cdp,
        visible: true,
        scroll: false,
      }).catch((error) => {
        captchaLogger.error(error);
        return null;
      });

      if (!res) {
        captchaLogger.error('Cloudflare captcha not found');
        return {
          found: false,
          solved: false,
          time: Date.now() - start,
        };
      }

      captchaLogger.success();

      return {
        found: true,
        solved: true,
        time: Date.now() - start,
      };
    } catch (error: any) {
      const errorMessage =
        error?.message || 'Unknown error during Cloudflare solving';
      captchaLogger.error(errorMessage);
      return {
        found: false,
        solved: false,
        time: Date.now() - start,
      };
    }
  }

  private async getAllBrowserRequests({ cdp }: { cdp: Connection }) {
    const requests = [...this.#requests]
      .map(([, { url }]) => url)
      .filter((u) => u.startsWith('http'));
    const [iframeURLs, cdpTargetURLs] = await Promise.all([
      this.evaluate({
        cdp,
        content: `(${findIframeURLs.toString()})()`,
      }).then((r) => r.value as string[]),
      this.browserCDP.send('Target.getTargets').then(({ targetInfos }) => {
        return targetInfos.filter((t) => t.type === 'iframe').map((t) => t.url);
      }),
    ]);

    return [...iframeURLs, ...cdpTargetURLs, ...requests];
  }

  private async solveHCaptcha({
    wait,
    timeout,
    cdp = this.mainCDP,
  }: {
    wait: boolean;
    timeout: number;
    cdp?: Connection;
  }) {
    this.#logger.debug(`Attempting to find and solve hCaptcha`);
    const response: {
      token: null | string;
      found: boolean;
      solved: boolean;
      time: number;
    } = {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
    const start = Date.now();
    const poll = 200;
    const time = wait ? timeout : 0;
    const apiToken = getTokenFromRequest(this.#req);
    const {
      value: { website, userAgent },
    } = await this.evaluate({
      cdp,
      content: `{ website: location.href, userAgent: navigator.userAgent };`,
    });
    const captchaLogger = captchaLoggerFactory(
      apiToken!,
      'hcaptcha',
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    captchaLogger.attempt();

    try {
      const sitekey = await asyncPoll(
        async () => {
          if (!this.#running) {
            return null;
          }
          const requests = await this.getAllBrowserRequests({ cdp });
          return requests.reduce((result: null | string, uri) => {
            if (result) return result;
            if (!isMatch(uri, hCaptcha.url)) {
              return null;
            }
            const url = new URL(uri);
            const params = url.hash
              ? new URLSearchParams(url.hash)
              : url.searchParams;
            const siteKey = params.get(hCaptcha.param);
            return siteKey;
          }, null);
        },
        poll,
        time,
      ).catch((error) => {
        console.error(error);
        return null;
      });

      if (!sitekey) {
        this.#logger.debug(`No hCaptcha site-key could be found.`);
        response.time = Date.now() - start;

        captchaLogger.error('No hCaptcha site-key could be found');

        return response;
      }

      response.found = true;

      this.#logger.info(
        `Found hCaptcha sitekey of "${sitekey}" and url of "${website}", solving..."`,
      );

      const res = await captchas.solveHCaptcha(website, sitekey, userAgent);
      await this.evaluate({
        cdp,
        content: `(${setHcaptchaResponse.toString()})(\`${hCaptcha.input}\`, \`${hCaptcha.iframe}\`, \`${res.data}\`)`,
      });
      await sleep(100);

      captchaLogger.success();
      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.#req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;

      if (apiToken) {
        const errorMessage =
          error?.message || 'Unknown error during hCaptcha solving';

        captchaLogger.error(errorMessage);
      }

      return response;
    }
  }

  private async solveRecaptcha({
    wait,
    timeout,
    cdp = this.mainCDP,
  }: {
    wait: boolean;
    timeout: number;
    cdp?: Connection;
  }) {
    const response: {
      token: null | string;
      found: boolean;
      solved: boolean;
      time: number;
    } = {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
    const start = Date.now();
    const poll = 200;
    const time = wait ? timeout : 0;
    const apiToken = getTokenFromRequest(this.#req);
    const {
      value: { website, userAgent },
    } = await this.evaluate({
      cdp,
      content: `{ website: location.href, userAgent: navigator.userAgent };`,
    });

    // Initialize logger with fallback type, will be updated with detailed type after detection
    let captchaLogger = captchaLoggerFactory(
      apiToken!,
      'recaptcha', // Fallback type
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    try {
      let sitekey: string | null = null;
      let widgetInfo: any = null;

      // Step 1: Try DOM-based detection first (more comprehensive)
      try {
        const { value: domWidgetInfo } = await this.evaluate({
          cdp,
          content: `(${getRecaptchaWidgetInfo.toString()})()`,
        });

        if (domWidgetInfo && domWidgetInfo.sitekey) {
          sitekey = domWidgetInfo.sitekey;
          widgetInfo = domWidgetInfo;
        }
      } catch (domError) {
        // DOM-based detection failed, continue to network-based
      }

      // Step 2: Fallback to network-based detection if DOM method fails
      if (!sitekey) {
        sitekey = await asyncPoll(
          async () => {
            if (!this.#running) {
              return null;
            }
            const requests = await this.getAllBrowserRequests({ cdp });
            widgetInfo = requests.reduce(recaptchaSiteKeyReducer, null);
            return widgetInfo?.sitekey || null;
          },
          poll,
          time,
        ).catch((error) => {
          this.#logger.debug('Network detection failed', { error });
          return null;
        });
      }

      // Update logger with detailed type and subtype based on detected widget info
      const { captchaType, captchaSubtype } =
        this.extractRecaptchaTypeAndSubtype(widgetInfo);
      captchaLogger = captchaLoggerFactory(
        apiToken!,
        captchaType,
        website,
        this.#logger,
        this.#config,
        'bql',
        captchaSubtype, // Pass subtype explicitly
      );
      captchaLogger.attempt();

      if (!sitekey) {
        response.time = Date.now() - start;

        captchaLogger.error('No g-recaptcha site-key could be found');

        return response;
      }

      response.found = true;

      const solveOptions: any = {
        token: apiToken || undefined,
        endpointType: 'bql',
        config: this.#config,
        logger: this.#logger,
      };

      // Add V2 widget-specific parameters
      if (widgetInfo) {
        if (widgetInfo.s) {
          solveOptions.datas = widgetInfo.s; // Critical: Google Service Captcha data-s parameter
        }
        if (widgetInfo.enterprise) {
          solveOptions.enterprise = widgetInfo.enterprise;
        }
        if (widgetInfo.version === 'v2_invisible') {
          solveOptions.invisible = true;
        }
      }

      const res = await captchas.solveRecaptcha(
        website,
        sitekey,
        userAgent,
        solveOptions,
      );

      await this.waitForSelector({
        selector: recaptcha.input,
        timeout,
        visible: false,
      });
      const escaped = res.data.replace(/\"/gi, '\\"') || '';

      // Set the solved token into the hidden input in the DOM
      await this.evaluate({
        cdp,
        content: `(${setInputValue.toString()})("${recaptcha.input}", "${escaped}")`,
      });

      // If a [data-callback] node or function is found, try and call it with the solution
      await this.evaluate({
        cdp,
        content: `(${callRecaptchaCallback})("${recaptcha.callback}", "${escaped}")`,
      });

      captchaLogger.success();
      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.#req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;

      if (apiToken) {
        const errorMessage =
          error?.message || 'Unknown error during g-recaptcha solving';

        captchaLogger.error(errorMessage);
      }

      return response;
    }
  }

  private async solveRecaptchaV3({
    wait,
    timeout,
    cdp = this.mainCDP,
  }: {
    wait: boolean;
    timeout: number;
    cdp?: Connection;
  }) {
    const response: {
      token: null | string;
      found: boolean;
      solved: boolean;
      time: number;
    } = {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
    const start = Date.now();
    const poll = 200;
    const time = wait ? timeout : 0;
    const apiToken = getTokenFromRequest(this.#req);

    const {
      value: { website, userAgent },
    } = await this.evaluate({
      cdp,
      content: `{ website: location.href, userAgent: navigator.userAgent };`,
    });

    // Initialize logger with fallback type, will be updated with detailed type after detection
    let captchaLogger = captchaLoggerFactory(
      apiToken!,
      'recaptcha-v3', // Fallback type
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    try {
      let sitekey: string | null = null;
      let widgetInfo: RecaptchaWidgetInfo | null = null;

      // Step 1: Try DOM-based detection first (more comprehensive for V3)
      try {
        const { value: domWidgetInfo } = await this.evaluate({
          cdp,
          content: `(${getRecaptchaWidgetInfo.toString()})()`,
        });

        if (
          domWidgetInfo &&
          domWidgetInfo.sitekey &&
          domWidgetInfo.version === 'v3'
        ) {
          sitekey = domWidgetInfo.sitekey;
          widgetInfo = domWidgetInfo;
        }
      } catch (domError) {
        // DOM-based V3 detection failed, continue to network-based
      }

      // Step 2: Fallback to network-based detection if DOM method fails
      if (!sitekey) {
        const networkWidgetInfo = await asyncPoll(
          async () => {
            if (!this.#running) {
              return null;
            }
            const requests = await this.getAllBrowserRequests({ cdp });
            const detectedWidgetInfo = requests.reduce(
              recaptchaSiteKeyReducer,
              null,
            );
            return detectedWidgetInfo;
          },
          poll,
          time,
        ).catch((error) => {
          this.#logger.debug('Network detection failed', { error });
          return null;
        });

        if (networkWidgetInfo) {
          sitekey = networkWidgetInfo.sitekey;
          widgetInfo = networkWidgetInfo;
        }
      }

      // Update logger with detailed type and subtype based on detected widget info
      const { captchaType, captchaSubtype } =
        this.extractRecaptchaTypeAndSubtype(widgetInfo);
      captchaLogger = captchaLoggerFactory(
        apiToken!,
        captchaType,
        website,
        this.#logger,
        this.#config,
        'bql',
        captchaSubtype, // Pass subtype explicitly
      );
      captchaLogger.attempt();

      if (!sitekey) {
        response.time = Date.now() - start;
        captchaLogger.error('No g-recaptcha-v3 site-key could be found');

        return response;
      }

      response.found = true;

      // Build V3 solve options with extracted widget information
      const solveOptions: any = {
        token: apiToken || undefined,
        endpointType: 'bql',
        config: this.#config,
        logger: this.#logger,
        min_score: 0.7, // V3 specific parameter
      };

      // Add V3 widget-specific parameters
      if (widgetInfo) {
        if (widgetInfo.s) {
          solveOptions.datas = widgetInfo.s; // Critical: Google Service Captcha data-s parameter
        }
        if (widgetInfo.action) {
          solveOptions.action = widgetInfo.action; // V3 action parameter
        }
        if (widgetInfo.enterprise) {
          solveOptions.enterprise = widgetInfo.enterprise; // Enterprise flag
        }
      }

      const res = await captchas.solveRecaptcha(
        website,
        sitekey,
        userAgent,
        solveOptions,
      );

      await this.evaluate({
        cdp,
        content: `(() => {
          window.verifyRecaptcha("${res.data}");
        })()`,
      });

      response.token = res.data;
      response.solved = true;
      response.time = Date.now() - start;
      captchaLogger.success();

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;

      if (apiToken) {
        const errorMessage =
          error?.message || 'Unknown error during g-recaptcha-v3 solving';
        captchaLogger.error(errorMessage);
      }

      return response;
    }
  }

  private async solveNormalCaptcha({
    wait,
    timeout,
    cdp = this.mainCDP,
  }: {
    wait: boolean;
    timeout: number;
    cdp?: Connection;
  }) {
    this.#logger.debug(`Attempting to find and solve normal captcha`);
    const response: {
      token: null | string;
      found: boolean;
      solved: boolean;
      time: number;
    } = {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
    const start = Date.now();
    const poll = 200;
    const time = wait ? timeout : 0;
    const apiToken = getTokenFromRequest(this.#req);
    const {
      value: { website },
    } = await this.evaluate({
      cdp,
      content: `{ website: location.href };`,
    });
    const captchaLogger = captchaLoggerFactory(
      apiToken!,
      'normalcaptcha',
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    captchaLogger.attempt();

    try {
      const imageData = await asyncPoll(
        async () => {
          if (!this.#running) {
            return null;
          }
          const result = await this.evaluate({
            cdp,
            content: `(${getCaptchaImg.toString()})()`,
          });
          return result.value ? result : null;
        },
        poll,
        time,
      ).catch((error) => {
        this.#logger.error('Error solving normal captcha:', error);
        return null;
      });

      if (!imageData || !imageData.value) {
        response.time = Date.now() - start;
        captchaLogger.error('No normal captcha image could be found');
        return response;
      }

      response.found = true;

      this.#logger.info(`Found normal captcha image, solving...`);

      const res = await captchas.solveNormalCaptcha(imageData.value);

      // Find input field and insert solution
      await this.evaluate({
        cdp,
        content: `(${setCaptchaValue.toString()})("${res.data}")`,
      });

      captchaLogger.success();
      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.#req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;

      if (apiToken) {
        const errorMessage =
          error?.message || 'Unknown error during normal captcha solving';
        captchaLogger.error(errorMessage);
      }

      return response;
    }
  }

  private async solveGeeTest({
    timeout,
    wait,
  }: {
    timeout: number;
    wait: boolean;
  }) {
    const response: {
      token: null | string;
      found: boolean;
      solved: boolean;
      time: number;
    } = {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
    const start = Date.now();
    const poll = 200;
    const apiToken = getTokenFromRequest(this.#req);
    const time = wait ? timeout : 0;
    const {
      value: { website },
    } = await this.evaluate({
      content: `{ website: location.href };`,
    });
    const captchaLogger = captchaLoggerFactory(
      apiToken!,
      'geetest',
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    captchaLogger.attempt();

    const sitekey = await asyncPoll(
      async () => {
        const requests = await this.getAllBrowserRequests({
          cdp: this.mainCDP,
        });

        const data = requests.reduce(
          (result: null | { siteKey: string; challenge: string }, request) => {
            if (result) return result;
            if (!isMatch(request, geetest.url)) {
              return null;
            }

            const url = new URL(request);
            const params = url.hash
              ? new URLSearchParams(url.hash)
              : url.searchParams;

            const siteKey = params.get(geetest.gtParam);
            const challenge = params.get(geetest.challengeParam);

            if (siteKey && challenge) {
              return { siteKey, challenge };
            }
            return null;
          },
          null as null | { siteKey: string; challenge: string },
        );

        return data;
      },
      poll,
      time,
    ).catch((error) => {
      this.#logger.error('Error solving GeeTest:', error);
      return null;
    });

    if (!sitekey) {
      this.#logger.info(`No GeeTest sitekey found`);
      response.time = Date.now() - start;
      return response;
    }

    response.found = true;

    const res = await captchas.solveGeeTest(
      website,
      sitekey.siteKey,
      sitekey.challenge,
    );

    await this.evaluate({
      cdp: this.mainCDP,
      content: `(${setGeetestResponse.toString()})(${res.geetest_challenge}, ${res.geetest_validate}, ${res.geetest_seccode})`,
    });

    response.token = res.geetest_seccode;
    response.solved = true;
    response.time = Date.now() - start;
    captchaLogger.success();
    ++this.#req.__bless__.captchasSolved;

    return response;
  }

  private async solveAmazonWAF({
    timeout,
    wait,
  }: {
    timeout: number;
    wait: boolean;
  }) {
    this.#logger.info('Attempting to solve AmazonWAF');
    const response: {
      token: null | string;
      found: boolean;
      solved: boolean;
      time: number;
    } = {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
    const start = Date.now();
    const poll = 200;
    const time = wait ? timeout : 0;
    const apiToken = getTokenFromRequest(this.#req);
    const {
      value: { website },
    } = await this.evaluate({
      content: `{ website: location.href };`,
    });

    const captchaLogger = captchaLoggerFactory(
      apiToken!,
      'amazonwaf',
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    captchaLogger.attempt();

    const data = await asyncPoll(
      async () => {
        const res = await this.evaluate({
          content: `(${getWAFData.toString()})()`,
        });

        return res.value;
      },
      poll,
      time,
    ).catch((error) => {
      this.#logger.error('Error solving AmazonWAF:', error);
      return null;
    });

    if (!data) {
      this.#logger.info(`No AmazonWAF data found`);
      response.time = Date.now() - start;
      captchaLogger.error('No AmazonWAF data found');
      return response;
    }

    response.found = true;

    try {
      const res = await captchas.solveAmazonWAF({
        website,
        captchaScript: data.captchaScript,
        challengeScript: data.challengeScript,
        context: data.context,
        iv: data.iv,
        key: data.key,
      });

      const injectResponse = async (token: string) => {
        // @ts-ignore
        await window.ChallengeScript.submitCaptcha(token);
      };

      await this.evaluate({
        content: `(${injectResponse.toString()})("${res.data.captcha_voucher}")`,
      });

      captchaLogger.success();
      response.token = res.data.captcha_voucher;
      response.solved = true;
      response.time = Date.now() - start;
      ++this.#req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;
      const errorMessage =
        error?.message || 'Unknown error during AmazonWAF solving';
      captchaLogger.error(errorMessage);
      return response;
    }
  }

  private async solveFriendlyCaptcha({
    wait,
    timeout,
    cdp = this.mainCDP,
  }: {
    wait: boolean;
    timeout: number;
    cdp?: Connection;
  }) {
    this.#logger.debug(`Attempting to find and solve Friendly Captcha`);
    const response: {
      token: null | string;
      found: boolean;
      solved: boolean;
      time: number;
    } = {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
    const start = Date.now();
    const poll = 200;
    const time = wait ? timeout : 0;
    const apiToken = getTokenFromRequest(this.#req);
    const {
      value: { website },
    } = await this.evaluate({
      cdp,
      content: `{ website: location.href };`,
    });
    const captchaLogger = captchaLoggerFactory(
      apiToken!,
      'friendlycaptcha',
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    captchaLogger.attempt();

    try {
      const sitekey = await asyncPoll(
        async () => {
          if (!this.#running) {
            return null;
          }
          this.#logger.debug('Waiting for Friendly Captcha key selector');
          await this.waitForSelector({
            selector: friendlyCaptcha.keySelector,
            timeout: timeout,
            visible: false,
            cdp,
          });
          this.#logger.debug('Found Friendly Captcha key selector');
          const { value: key } = await this.evaluate({
            cdp,
            content: `(() => document.querySelector('${friendlyCaptcha.keySelector}').getAttribute('${friendlyCaptcha.param}'))()`,
          });
          return key;
        },
        poll,
        time,
      ).catch((error) => {
        this.#logger.error('Error finding Friendly Captcha sitekey:', error);
        return null;
      });

      if (!sitekey) {
        response.time = Date.now() - start;
        captchaLogger.error('No Friendly Captcha sitekey could be found');
        return response;
      }

      response.found = true;

      this.#logger.info(
        `Found Friendly Captcha sitekey of "${sitekey}" and url of "${website}", solving...`,
      );

      const res = await captchas.solveFriendlyCaptcha(website, sitekey);

      // Set the solution in the appropriate input field
      await this.evaluate({
        cdp,
        content: `(${setFriendlyCaptchaResponse.toString()})('${friendlyCaptcha.keySelector}', "${res.data}")`,
      });

      // Call the callback function if it exists
      await this.evaluate({
        cdp,
        content: `(${callFriendlyCaptchaCallback.toString()})('${friendlyCaptcha.keySelector}', "${res.data}")`,
      });

      captchaLogger.success();
      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.#req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;

      if (apiToken) {
        const errorMessage =
          error?.message || 'Unknown error during Friendly Captcha solving';
        captchaLogger.error(errorMessage);
      }

      return response;
    }
  }

  private async solveTextCaptcha({
    wait,
    timeout,
    cdp = this.mainCDP,
  }: {
    wait: boolean;
    timeout: number;
    cdp?: Connection;
  }) {
    this.#logger.debug(`Attempting to find and solve text captcha`);
    const response: {
      token: null | string;
      found: boolean;
      solved: boolean;
      time: number;
    } = {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
    const start = Date.now();
    const poll = 200;
    const time = wait ? timeout : 0;
    const apiToken = getTokenFromRequest(this.#req);
    const {
      value: { website },
    } = await this.evaluate({
      cdp,
      content: `{ website: location.href };`,
    });
    const captchaLogger = captchaLoggerFactory(
      apiToken!,
      'textcaptcha',
      website,
      this.#logger,
      this.#config,
      'bql',
    );

    captchaLogger.attempt();

    try {
      const questionData = await asyncPoll(
        async () => {
          if (!this.#running) {
            return null;
          }
          const result = await this.evaluate({
            cdp,
            content: `(${getTextCaptchaQuestion.toString()})()`,
          });
          return result.value ? result : null;
        },
        poll,
        time,
      ).catch((error) => {
        this.#logger.error('Error finding text captcha question:', error);
        return null;
      });

      if (!questionData || !questionData.value) {
        response.time = Date.now() - start;
        captchaLogger.error('No text captcha question could be found');
        return response;
      }

      response.found = true;

      this.#logger.info(
        `Found text captcha question: "${questionData.value}", solving...`,
      );

      const res = await captchas.solveTextCaptcha(questionData.value);

      // Set the answer in the appropriate input field
      await this.evaluate({
        cdp,
        content: `(${setTextCaptchaAnswer.toString()})("${res.data}")`,
      });

      captchaLogger.success();
      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.#req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;

      if (apiToken) {
        const errorMessage =
          error?.message || 'Unknown error during text captcha solving';
        captchaLogger.error(errorMessage);
      }

      return response;
    }
  }

  private async fetchRequestPaused(request: Protocol.Fetch.RequestPausedEvent) {
    if (
      !this.#running ||
      request.request?.url?.startsWith('chrome') ||
      request.request?.url?.startsWith('data')
    ) {
      return;
    }

    if (!this.#fetchInterceptors.length) {
      return this.mainCDP.send('Fetch.continueRequest', {
        requestId: request.requestId,
      });
    }

    const match = findPatternForRequest(request, this.#fetchInterceptors);
    if (!match) {
      return this.mainCDP.send('Fetch.continueRequest', {
        requestId: request.requestId,
      });
    }

    if (match.action === 'abort') {
      this.#logger.info(
        'Rejecting',
        request.resourceType.toLowerCase(),
        truncate(request.request.url),
      );
      return this.mainCDP.send('Fetch.failRequest', {
        requestId: request.requestId,
        errorReason: 'Failed',
      });
    }

    if (match.action === 'proxy') {
      this.#logger.info(
        'Proxying',
        request.resourceType.toLowerCase(),
        truncate(request.request.url),
      );
      try {
        const isBlessProxy = !match.server;
        const proxy =
          match.server ??
          getProxyURL({
            username: this.#config.getProxyUsername(),
            password: this.#config.getProxyPassword()!,
            country: match.country,
            city: match.city,
            state: match.state,
            sessionId: match.session,
          });
        const { url, method, headers, postData: body } = request.request;
        const response = await fetchWithProxy({
          url,
          proxy,
          body,
          headers,
          method,
        });
        this.mainCDP.send('Fetch.fulfillRequest', {
          requestId: request.requestId,
          responseCode: response.status,
          body: response.body,
          responseHeaders: [...response.headers].map(([name, value]) => ({
            name,
            value,
          })),
        });

        if (isBlessProxy) {
          this.#req.__bless__.bqlProxyBytes +=
            response.sentBytes + response.receivedBytes;
        }
      } catch (e) {
        this.mainCDP.send('Fetch.failRequest', {
          requestId: request.requestId,
          errorReason: 'Failed',
        });
      }
      return;
    }

    return this.mainCDP.send('Fetch.continueRequest', {
      requestId: request.requestId,
    });
  }

  private async toggleFetchDomain(enabled: boolean) {
    if (!enabled) {
      await this.mainCDP.send('Fetch.disable');
      this.#fetchEnabled = false;
      return enabled;
    }

    await this.mainCDP.send('Fetch.enable');
    this.#fetchEnabled = true;
    this.mainCDP.on('Fetch.requestPaused', this.fetchRequestPaused.bind(this));
    return enabled;
  }

  private async addFetchInterceptors(interceptor: FetchInterception) {
    this.#fetchInterceptors.push(interceptor);

    if (!this.#fetchEnabled) {
      await this.toggleFetchDomain(true);
    }
  }

  private async removeInterceptorsByAction(
    action: FetchInterception['action'],
  ) {
    this.#fetchInterceptors = this.#fetchInterceptors.filter(
      (i) => i.action !== action,
    );

    if (!this.#fetchInterceptors.length) {
      await this.toggleFetchDomain(false);
    }
  }

  public async waitForNavigation(
    waitUntil: waitUntil = 'load',
    time: number = this.#defaultTimeout,
  ): Promise<{
    url: string | null;
    text: string | null;
    status: number | null;
    time: number;
  }> {
    if (!this.#running) {
      throw new Error(`Browser is no longer running`);
    }
    const cdp = this.mainCDP;
    const start = Date.now();
    const wait = waitUntil.toLowerCase();
    const {
      frameTree: {
        frame: { id: mainFrameId },
      },
    } = await cdp.send('Page.getFrameTree');
    const page$ = fromEvent<Protocol.Page.LifecycleEventEvent>(
      cdp as any,
      'Page.lifecycleEvent',
    ).pipe(
      filter((page) => page.frameId === mainFrameId),
      map((event) => event.name.toLowerCase().replace('candidate', '')),
    );
    const navigated$ = fromEvent<Protocol.Page.FrameNavigatedEvent>(
      cdp as any,
      'Page.frameNavigated',
    ).pipe(
      filter((page) => page.frame.id === mainFrameId),
      filter((event) => event.frame.url !== 'about:blank'),
    );

    // Group together events that are equal to a Back/Forward Cache restore
    // as Chrome won't re-emit these events when BackForwardCacheRestore happens
    const backForwardEvents = [
      'commit',
      'load',
      'domcontentloaded',
      'firstmeangingfulpaint',
      'firstcontentfulpaint',
      'interactivetime',
    ];

    return firstValueFrom(
      combineLatest({
        navigated$,
        page$,
      }).pipe(
        filter(({ page$, navigated$ }) => {
          if (
            navigated$.type === 'BackForwardCacheRestore' &&
            backForwardEvents.includes(wait)
          ) {
            return true;
          }
          return (
            wait === 'commit' ||
            (wait === 'networkidle' && page$ === 'networkalmostidle') ||
            page$ === wait
          );
        }),
        throwTimeout(time),
        catchError((err) => {
          if (err instanceof TimeoutError) {
            throw new Error(`Timeout of ${time.toLocaleString()}ms reached`);
          }
          throw err;
        }),
      ),
    ).then(async ({ navigated$ }) => {
      this.#logger.debug('Navigation ended:', navigated$.frame.url);
      const response = this.#responses.get(navigated$.frame.loaderId);

      return {
        url: navigated$.frame.url,
        status: response?.status ?? null,
        text: 'ok',
        time: Date.now() - start,
      };
    });
  }

  private async goInHistory(
    delta: number,
    waitUntil: waitUntil,
    timeout: number,
  ) {
    if (!this.#running) {
      return;
    }
    const history = await this.mainCDP.send('Page.getNavigationHistory');
    const entry = history.entries[history.currentIndex + delta];

    if (!entry) {
      return null;
    }

    const [response] = await Promise.all([
      this.waitForNavigation(waitUntil, timeout),
      this.mainCDP.send('Page.navigateToHistoryEntry', {
        entryId: entry.id,
      }),
    ]);

    return response;
  }

  public hasErrored() {
    return this.#errored;
  }

  public async waitForResponse({
    urls,
    methods,
    statuses,
    types,
    operator,
    timeout = this.#defaultTimeout,
  }: {
    urls?: string[];
    methods?: string[];
    statuses?: number[];
    types?: string[];
    operator: operators;
    timeout?: number;
  }): Promise<
    {
      id: string;
      url: string;
      method: string;
      type: string;
      headers: object;
      status: number;
    }[]
  > {
    return new Promise((resolve, reject) => {
      const responses = this.hasReceivedResponse({
        methods,
        operator,
        statuses,
        types,
        urls,
      });

      if (responses && responses.length) {
        return resolve(responses);
      }

      const cleanup = () => {
        clearTimeout(timer);
        this.mainCDP.off('Network.responseReceived', onResponse);
      };
      const timer = setTimeout(() => {
        cleanup();
        return reject(
          new Error(
            `Timed-out waiting for response to be made in ${timeout.toLocaleString()}ms`,
          ),
        );
      }, timeout);
      this.#timers.push(timer);
      const onResponse = (params: Protocol.Network.ResponseReceivedEvent) => {
        const id = params.requestId;
        const request = this.#requests.get(id);
        if (!request) {
          return null;
        }
        const url = params.response.url;
        const method = request.method;
        const type = params.type.toLowerCase();
        const headers = params.response.headers;
        const status = params.response.status;

        if (
          networkMatchesPattern({
            url,
            urls,
            method,
            methods,
            type,
            types,
            status,
            statuses,
            operator,
          })
        ) {
          cleanup();
          return resolve([
            {
              id,
              url,
              method,
              type,
              headers,
              status,
            },
          ]);
        }
      };

      this.mainCDP.on('Network.responseReceived', onResponse);
    });
  }

  public async waitForRequest({
    urls,
    methods,
    types,
    operator,
    timeout = this.#defaultTimeout,
  }: {
    urls?: string[];
    methods?: string[];
    types?: string[];
    operator: operators;
    timeout?: number;
  }): Promise<
    {
      url: string;
      method: string;
      type: string;
      headers: object;
    }[]
  > {
    return new Promise((resolve, reject) => {
      const requests = this.hasMadeRequest({
        urls,
        methods,
        types,
        operator,
      });

      if (requests && requests.length > 0) {
        return resolve(requests);
      }
      const cleanup = () => {
        clearTimeout(timer);
        this.mainCDP.off('Network.requestWillBeSent', onRequest);
      };
      const timer = setTimeout(() => {
        cleanup();
        return reject(
          new Error(
            `Timed-out waiting for request pattern to be made in ${timeout.toLocaleString()}ms`,
          ),
        );
      }, timeout);
      this.#timers.push(timer);
      const onRequest = (params: Protocol.Network.RequestWillBeSentEvent) => {
        const url = params.request.url;
        const method = params.request.method;
        const type = (params.type || '').toLowerCase();
        const headers = params.request.headers;
        if (
          networkMatchesPattern({
            method,
            methods,
            operator,
            type,
            types,
            url,
            urls,
          })
        ) {
          cleanup();
          return resolve([
            {
              url,
              method,
              type,
              headers,
            },
          ]);
        }
      };

      this.mainCDP.on('Network.requestWillBeSent', onRequest);
    });
  }

  public hasMadeRequest({
    urls,
    types,
    methods,
    operator,
  }: {
    urls?: string[];
    types?: string[];
    methods?: string[];
    operator: operators;
  }) {
    const requests = [...this.#requests].filter(([, { method, url, type }]) => {
      return networkMatchesPattern({
        method,
        methods,
        operator,
        type,
        types,
        url,
        urls,
      });
    });

    if (requests.length) {
      return requests.map(([id, { url, method, type, headers }]) => ({
        id,
        url,
        method,
        type,
        headers,
      }));
    }

    return null;
  }

  public hasReceivedResponse({
    urls,
    types,
    methods,
    operator,
    statuses,
  }: {
    urls?: string[];
    types?: string[];
    methods?: string[];
    statuses?: number[];
    operator: operators;
  }) {
    const responses = [...this.#responses].filter(
      ([, { method, url, type, status }]) => {
        return networkMatchesPattern({
          method,
          methods,
          operator,
          status,
          statuses,
          type,
          types,
          url,
          urls,
        });
      },
    );

    if (responses.length) {
      return responses.map(([id, { url, method, type, headers, status }]) => ({
        headers,
        id,
        method,
        status,
        type,
        url,
      }));
    }

    return null;
  }

  public async response({
    method,
    operator,
    timeout,
    type,
    url,
    wait,
    status,
  }: {
    method?: string[];
    operator: operators;
    timeout?: number;
    type?: string[];
    url?: string[];
    wait: boolean;
    status?: number[];
  }): Promise<Array<{
    id: string;
    type: string;
    method: string;
    url: string;
    headers: object;
    status: number;
    body: string;
  }> | null> {
    const responses = await this.waitForResponse({
      methods: method,
      urls: url,
      types: type,
      operator,
      timeout: wait ? timeout : 100,
      statuses: status,
    }).catch(() => null);

    if (!responses) {
      return null;
    }

    return await Promise.all(
      responses.map(async (r) => ({
        id: r.id,
        url: r.url,
        method: r.method,
        type: r.type,
        status: r.status,
        headers: Object.entries(r.headers).map(([name, value]) => ({
          name,
          value,
        })),
        body: await this.mainCDP
          .send('Network.getResponseBody', {
            requestId: r.id,
          })
          .then((d) => d.body),
      })),
    );
  }

  public async request({
    method,
    operator,
    timeout,
    type,
    url,
    wait,
  }: {
    method?: string[];
    operator: operators;
    timeout?: number;
    type?: string[];
    url?: string[];
    wait: boolean;
  }) {
    const requests = await this.waitForRequest({
      methods: method,
      urls: url,
      types: type,
      operator,
      timeout: wait ? timeout : 100,
    }).catch(() => null);

    if (!requests) {
      return null;
    }

    return requests.map((r) => ({
      ...r,
      headers: Object.entries(r.headers).map(([name, value]) => ({
        name,
        value,
      })),
    }));
  }

  public preferences({ timeout }: { timeout?: number }) {
    if (timeout) {
      this.#defaultTimeout = timeout;
    }
    return {
      timeout: this.#defaultTimeout,
    };
  }

  public async getDeepClientRect({ selector }: { selector: string }) {
    const query = parse(selector);
    const urlPattern = query.urlPattern || (await this.url()).value;
    const { targetInfos } = await this.browserCDP.send('Target.getTargets');
    const t = targetInfos.reverse().find((t) => isMatch(t.url, urlPattern));

    if (!t) {
      return null;
    }
    const wsURL = getDebuggerURL(t.targetId, this.#port);
    const connection = await new Connection(
      wsURL,
      await NodeWebSocketTransport.create(wsURL),
    );
    await connection.send('DOM.enable');
    const { nodes } = await connection.send('DOM.getFlattenedDocument', {
      pierce: true,
      depth: -1,
    });
    const node = match(query, nodes);
    if (!node) {
      connection.dispose();
      return null;
    }
    const box = await connection
      .send('DOM.getBoxModel', {
        nodeId: node.nodeId,
        backendNodeId: node.backendNodeId,
      })
      .catch(() => null);

    if (!box) {
      connection.dispose();
      return box;
    }

    return {
      cdp: connection,
      x: box.model.content[0],
      y: box.model.content[1],
      width: box.model.width,
      height: box.model.height,
    };
  }

  public async getBoundingClientRect({
    cdp = this.mainCDP,
    selector,
    visible,
  }: {
    cdp?: Connection;
    selector: string;
    visible: boolean;
  }): Promise<{
    x: number;
    y: number;
    width: number;
    height: number;
    cdp: Connection;
  } | null> {
    if (!this.#running) {
      return null;
    }

    if (isDeepSelector(selector)) {
      return this.getDeepClientRect({ selector });
    }

    return getBoundingClientRect({
      browser: cdp,
      selector,
      visible,
      sessionId: undefined,
      logger: this.#logger,
    }) as any;
  }

  public async url() {
    return this.evaluate({
      content: `window.location.href`,
    });
  }

  public async title() {
    return this.evaluate({
      content: `document.title`,
    });
  }

  public async evaluate({
    content,
    url,
    timeout = 10_000,
    cdp = this.mainCDP,
  }: {
    cdp?: Connection;
    content?: string;
    url?: string;
    timeout?: number;
  }): Promise<{ value: any; time: number }> {
    if (!content && !url) {
      throw new Error('One of "content" or "url" is required');
    }
    if (content && url) {
      throw new Error('Only one of "content" or "url" is allowed');
    }
    if (url) {
      content = await fetch(url, { signal: AbortSignal.timeout(timeout) })
        .then(async (response) => response.text())
        .catch((error) => {
          throw new Error(
            `Error getting script from URL: ${error.cause.message}`,
          );
        });
    }
    const start = Date.now();
    const res = await cdp.send('Runtime.evaluate', {
      returnByValue: true,
      awaitPromise: true,
      expression: `(async() => {return ${dedent(content as string)}})();`,
    });

    if (res.exceptionDetails) {
      throw new Error(res.exceptionDetails.exception?.description);
    }

    return {
      value: res.result.value,
      time: Date.now() - start,
    };
  }

  public async cookies({
    cookies,
    cdp = this.mainCDP,
  }: {
    cookies?: Protocol.Network.CookieParam[];
    cdp?: Connection;
  }): Promise<{
    cookies: {
      name: string;
      value: string;
      url: string;
      path: string;
      domain: string;
      expires?: number;
      secure?: boolean;
      httpOnly?: boolean;
      sameSite?: CookieSameSite;
    }[];
    time: number;
  }> {
    const start = Date.now();

    // Validate and filter expired cookies
    const expiredCookies = cookies?.filter(
      (c) => c.expires && c.expires * 1000 < Date.now(),
    );
    if (expiredCookies?.length) {
      const expiredCookieNames = expiredCookies.map((c) => c.name).join(', ');
      throw new Error(`Expired cookies detected: ${expiredCookieNames}`);
    }

    // Get current page location for fallback context
    const { value: location } = await this.evaluate({
      cdp,
      content: `{ domain: location.hostname, path: location.pathname, url: location.href }`,
    });

    if (cookies && cookies.length > 0) {
      // Validate and prepare cookies for CDP
      const items = cookies.map((cookie) => {
        // Validate required fields
        if (!cookie.name || !cookie.value) {
          throw new Error('Cookie name and value are required');
        }

        // Ensure domain or URL is specified for proper context
        if (!cookie.domain && !cookie.url) {
          throw new Error(
            `Cookie '${cookie.name}' must specify either domain or url`,
          );
        }

        // Build the cookie item for CDP
        const cookieItem: any = {
          name: cookie.name,
          value: cookie.value,
          // Use cookie-specified URL or construct from domain
          url: cookie.url || `https://${cookie.domain}${cookie.path || '/'}`,
          path: cookie.path || '/',
          domain: cookie.domain,
          secure: !!cookie.secure,
          httpOnly: !!cookie.httpOnly,
          sameSite: cookie.sameSite || 'Lax',
        };

        // Only include expires if it's a valid number (fixes null expires issue)
        if (typeof cookie.expires === 'number' && cookie.expires > 0) {
          cookieItem.expires = cookie.expires;
        }
        // Note: Omitting expires entirely creates a session cookie, which is what we want for null values

        return cookieItem;
      });

      this.#logger.debug('Setting cookies:', JSON.stringify(items, null, 2));

      try {
        await this.mainCDP.send('Network.setCookies', {
          cookies: items,
        });
      } catch (error) {
        this.#logger.error('Failed to set cookies:', error);
        throw new Error(
          `Failed to set cookies: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }

    // Collect URLs from all cookies to get comprehensive cookie list
    const urlsToCheck = new Set<string>();

    // Add current page URL
    urlsToCheck.add(location.url);

    // Add URLs from cookies that were just set
    if (cookies) {
      cookies.forEach((cookie) => {
        if (cookie.url) {
          urlsToCheck.add(cookie.url);
        } else if (cookie.domain) {
          // Construct URLs for both HTTP and HTTPS
          urlsToCheck.add(`https://${cookie.domain}${cookie.path || '/'}`);
          urlsToCheck.add(`http://${cookie.domain}${cookie.path || '/'}`);
        }
      });
    }

    const allUrls = Array.from(urlsToCheck);
    this.#logger.debug('Getting cookies for URLs:', allUrls);

    const siteCookies = (
      await this.mainCDP.send('Network.getCookies', {
        urls: allUrls,
      })
    ).cookies
      ?.map((cookie) => {
        delete (cookie as unknown as Record<string, unknown>)['priority'];
        return cookie;
      })
      .map((cookie) => ({
        name: cookie.name,
        value: cookie.value,
        // Use the cookie's actual URL context, not just current page
        url: cookie.domain
          ? `https://${cookie.domain}${cookie.path || '/'}`
          : location.url,
        path: cookie.path || location.path,
        domain: cookie.domain || location.domain,
        expires: cookie.expires,
        secure: cookie.secure || false,
        httpOnly: cookie.httpOnly || false,
        sameSite: cookie.sameSite || 'Lax',
      }));

    this.#logger.debug(`Retrieved ${siteCookies?.length || 0} cookies`);

    return {
      cookies: siteCookies || [],
      time: Date.now() - start,
    };
  }

  public async setExtraHTTPHeaders({ headers }: { headers: any }) {
    const start = Date.now();
    const extraHTTPHeaders: any = {};
    for (const [, obj] of Object.entries(headers)) {
      // @ts-ignore
      extraHTTPHeaders[obj.name] = obj?.value;
    }
    await this.mainCDP.send('Network.setExtraHTTPHeaders', {
      headers: extraHTTPHeaders,
    });
    return {
      headers: Object.entries(extraHTTPHeaders).map(([name, value]) => ({
        name,
        value,
      })),
      time: Date.now() - start,
    };
  }

  public async userAgent({ userAgent }: { userAgent: string }) {
    if (!this.#running) {
      throw new Error(`Browser is no longer running`);
    }

    await this.mainCDP.send('Network.setUserAgentOverride', {
      userAgent,
    });

    this.#logger.debug(`User-Agent set to: ${userAgent}`);

    return {
      userAgent,
    };
  }

  public async screenshot({
    options = {
      type: 'png',
      fullPage: false,
      captureBeyondViewport: false,
      timeout: this.#defaultTimeout,
    },
    cdp = this.mainCDP,
    selector,
  }: {
    selector?: string;
    options?: ScreenshotOpts;
    cdp?: Connection;
  }): Promise<{
    base64: string;
    format: string;
    time: number;
  }> {
    const start = Date.now();
    if (options.quality !== undefined) {
      if (options.quality < 0 && options.quality > 100) {
        throw new Error(
          `Expected 'quality' (${options.quality}) to be between 0 and 100, inclusive.`,
        );
      }
      if (
        options.type === undefined ||
        !['jpeg', 'webp'].includes(options.type)
      ) {
        throw new Error(
          `${options.type ?? 'png'} screenshots do not support 'quality'.`,
        );
      }
    }

    if (options.clip) {
      if (options.clip.width <= 0) {
        throw new Error("'width' in 'clip' must be positive.");
      }
      if (options.clip.height <= 0) {
        throw new Error("'height' in 'clip' must be positive.");
      }
    }

    if (options.fullPage) {
      if (selector) {
        throw new Error("'selector' and 'options.fullPage' are exclusive");
      }

      if (options.clip) {
        throw new Error("'clip' and 'fullPage' are exclusive");
      }

      if (options.captureBeyondViewport) {
        throw new Error("'clip' and 'captureBeyondViewport' are exclusive");
      }
    }

    let clip;
    let finalOptions = { ...options };
    let elementRect;

    const scrollToElem = selector && selector !== 'body';

    // Hierarchy of cliping:
    // 1. options.clip
    // 2. selector
    // 3. full page
    // 4. current viewport

    if (options.clip) {
      clip = {
        x: options.clip.x,
        y: options.clip.y,
        width: options.clip.width,
        height: options.clip.height,
      };
    } else if (scrollToElem) {
      // Get the position of the element *before* taking a full page screenshot
      elementRect = await this.waitForSelector({
        cdp,
        selector,
        visible: true,
      });
      finalOptions.clip = undefined;
      finalOptions.captureBeyondViewport = true;
    } else if (options?.fullPage || scrollToElem) {
      finalOptions.clip = undefined;
      finalOptions.captureBeyondViewport = true;
    } else {
      const viewport = await this.getViewport();
      clip = {
        x: viewport.pageLeft,
        y: viewport.pageTop,
        width: viewport.width,
        height: viewport.height,
      };
    }

    this.#logger.debug('screenshot', 'params: ', {
      ...finalOptions,
    });

    const screenshotPromise = cdp.send('Page.captureScreenshot', {
      ...finalOptions,
      clip: clip
        ? {
            x: clip.x,
            y: clip.y,
            width: clip.width,
            height: clip.height,
            scale: 1,
          }
        : undefined,
    });

    const { data } = await firstValueFrom(
      from(screenshotPromise).pipe(
        throwTimeout(options.timeout || this.#defaultTimeout),
        catchError((err) => {
          if (err instanceof TimeoutError) {
            throw new Error(
              `Screenshot timed out after ${options.timeout || this.#defaultTimeout}ms`,
            );
          }
          throw err;
        }),
      ),
    );

    if (scrollToElem && elementRect) {
      // If the screenshot is to a selector, then a full page screenshot was taken
      // and now we need to crop it to the element
      this.#logger.debug('screenshot', 'cropping screenshot params: ', {
        ...finalOptions,
      });

      const clip = {
        left: Math.floor(elementRect.x),
        top: Math.floor(elementRect.y),
        width: Math.floor(elementRect.width),
        height: Math.floor(elementRect.height),
      };

      const cropped = await sharp(Buffer.from(data, 'base64'))
        .extract(clip)
        .toBuffer();

      return {
        base64: cropped.toString('base64'),
        format: options.type as string,
        time: Date.now() - start,
      };
    }

    return {
      base64: data,
      format: options.type as string,
      time: Date.now() - start,
    };
  }

  public async mapSelector({
    cdp = this.mainCDP,
    selector,
    timeout = this.#defaultTimeout,
    wait,
    parent,
    parentIdx,
  }: {
    cdp?: Connection;
    selector: string;
    timeout?: number;
    wait: boolean;
    parent?: string;
    parentIdx?: number;
  }) {
    const time = wait ? timeout : 100;
    const escaped = selector.replace(/\"/gi, '\\"');
    await this.waitForSelector({
      selector,
      timeout: time,
      cdp,
      visible: false,
    });

    const { value: selectors } = await this.evaluate({
      cdp,
      content: `(${mapSelector.toString()})({
        selector: "${escaped}",
        parent: ${parent ? `"${parent}"` : 'undefined'},
        parentIdx: ${parentIdx}
      })`,
    });

    return Promise.all(
      selectors.map((s: any, parentIdx: number) => ({
        ...s,
        attribute({ name }: { name: string }) {
          return {
            name,
            value: s.attributes[name],
          };
        },
        mapSelector: async ({
          selector: s,
          timeout,
          wait,
        }: {
          selector: string;
          timeout: number;
          wait: boolean;
        }) =>
          this.mapSelector({
            selector: s,
            timeout,
            wait,
            parent: selector,
            parentIdx,
          }),
      })),
    );
  }

  public proxy({
    country,
    city,
    state,
    sticky,
    method,
    operator,
    server,
    type,
    url,
  }: {
    country?: string;
    city?: string;
    state?: string;
    sticky?: boolean;
    method?: string[];
    operator: operators;
    server: string;
    type?: string[];
    url?: string[];
  }) {
    const start = Date.now();
    const session = sticky ? id() : undefined;

    this.addFetchInterceptors({
      action: 'proxy',
      method,
      operator,
      server,
      type,
      url,
      country,
      city,
      state,
      session,
    });

    return {
      time: Date.now() - start,
    };
  }

  public async pdf({
    options,
    cdp = this.mainCDP,
  }: {
    options?: PDFOptions;
    cdp?: Connection;
  }): Promise<{
    base64: string;
    time: number;
    size: number;
  }> {
    const start = Date.now();

    // Default width and height to letter size, in case one of them is not provided
    let finalSize = {
      width: paperFormats.letter.width,
      height: paperFormats.letter.height,
    };

    if (options?.format) {
      finalSize = {
        ...paperFormats[options.format],
      };
    } else {
      if (options?.width) finalSize.width = cssUnitToInches(options.width);
      if (options?.height) finalSize.height = cssUnitToInches(options.height);
    }

    const finalOptions = {
      ...options,
      paperWidth: finalSize.width,
      paperHeight: finalSize.height,
    };

    // We don't need to set a default margin, as the API will use the default values
    if (options?.marginTop) {
      finalOptions.marginTop = cssUnitToInches(options.marginTop);
    }
    if (options?.marginLeft) {
      finalOptions.marginLeft = cssUnitToInches(options.marginLeft);
    }
    if (options?.marginRight) {
      finalOptions.marginRight = cssUnitToInches(options.marginRight);
    }
    if (options?.marginBottom) {
      finalOptions.marginBottom = cssUnitToInches(options.marginBottom);
    }

    this.#logger.trace(
      'pdf',
      'params: ',
      JSON.stringify(finalOptions, null, 2),
    );

    const pdfPromise = cdp.send(
      'Page.printToPDF',
      finalOptions || {},
    ) as Promise<{ data: string }>;
    const { data } = await firstValueFrom(
      from(pdfPromise).pipe(
        throwTimeout(options?.timeout || this.#defaultTimeout),
        catchError((err) => {
          if (err instanceof TimeoutError) {
            throw new Error(
              `PDF generation timed out after ${options?.timeout || this.#defaultTimeout}ms`,
            );
          }
          throw err;
        }),
      ),
    );

    return {
      base64: data,
      time: Date.now() - start,
      size: Buffer.from(data, 'base64').length,
    };
  }

  public async javaScriptEnabled({
    enabled,
    cdp = this.mainCDP,
  }: {
    enabled?: boolean;
    cdp?: Connection;
  }) {
    const start = Date.now();
    this.#jsEnabled = enabled ?? true;

    await cdp.send('Emulation.setScriptExecutionDisabled', {
      value: !this.#jsEnabled,
    });

    return {
      enabled: this.#jsEnabled,
      time: Date.now() - start,
    };
  }

  public async select({
    selector,
    value,
    cdp = this.mainCDP,
    timeout = this.#defaultTimeout,
    wait,
    visible,
    scroll,
  }: {
    selector: string;
    value: string | string[];
    cdp?: Connection;
    timeout?: number;
    wait: boolean;
    visible: boolean;
    scroll: boolean;
  }): Promise<{
    cdp: Connection;
    selector: string;
    value: string[];
    time: number;
  }> {
    const start = Date.now();
    const values = Array.isArray(value) ? value : [value];
    const escaped = selector.replace(/\"/gi, '\\"');
    const time = wait ? timeout : 100;

    const res = await this.waitForSelector({
      selector,
      timeout: time,
      cdp,
      visible,
    });

    if (scroll && (!selector || !isDeepSelector(selector))) {
      await this.scroll({
        visible,
        wait,
        x: res.x,
        y: res.y,
        cdp: res.cdp,
      });
    }

    const { value: selectedValues } = await this.evaluate({
      cdp: res.cdp,
      content: `(${selectFn.toString()})("${escaped}", ${values.map(
        (v) => `"${v}"`,
      )})`,
    });

    return {
      cdp: res.cdp,
      selector,
      value: selectedValues,
      time: Date.now() - start,
    };
  }

  public async waitForSelector({
    selector,
    timeout = this.#defaultTimeout,
    visible,
    cdp = this.mainCDP,
    checkInteractable = false,
  }: {
    selector: string;
    timeout?: number;
    visible: boolean;
    cdp?: Connection;
    checkInteractable?: boolean;
  }): Promise<{
    x: number;
    y: number;
    width: number;
    height: number;
    cdp: Connection;
  }> {
    return waitForSelector({
      selector,
      visible,
      browser: cdp,
      timeout: timeout,
      sessionId: undefined,
      logger: this.#logger,
      checkInteractable,
      browserAPI: this,
    }) as any;
  }

  public async scroll({
    x,
    y,
    selector,
    visible,
    wait,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
  }: {
    x?: number;
    y?: number;
    selector?: string;
    visible: boolean;
    timeout?: number;
    wait: boolean;
    cdp?: Connection;
  }): Promise<{
    cdp: Connection;
    x?: number;
    y?: number;
    selector?: string;
    time: number;
  }> {
    const start = Date.now();
    if (!selector && !x && !y) {
      throw new Error(
        `Scroll needs one of x and y, or selector, to work properly`,
      );
    }

    if (x || y) {
      this.#logger.debug('scroll', { x, y });
      await this.evaluate({
        cdp,
        timeout,
        content: `window.scrollTo(${x ?? 0}, ${y ?? 0})`,
      });
    }

    if (selector) {
      const escaped = selector.replace(/\"/gi, '\\"') || '';
      await this.waitForSelector({
        cdp,
        selector,
        timeout: wait ? timeout : 100,
        visible,
      });
      await this.evaluate({
        cdp,
        timeout,
        content: `(${scrollIntoView.toString()})("${escaped}", ${this.#humanLike})`,
      });
      await sleep(100);
    }

    return {
      cdp,
      x,
      y,
      selector,
      time: Date.now() - start,
    };
  }

  public async type({
    text,
    selector,
    delay,
    wait,
    scroll,
    visible,
    interactable: checkInteractable = true,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
  }: {
    text: string;
    selector: string;
    delay: [number, number];
    wait: boolean;
    scroll: boolean;
    visible: boolean;
    interactable?: boolean;
    timeout?: number;
    cdp?: Connection;
  }): Promise<{
    cdp: Connection;
    selector: string;
    text: string;
    x?: number;
    y?: number;
    time: number;
  }> {
    const start = Date.now();
    const res = await this.click({
      selector,
      wait,
      scroll,
      timeout,
      visible,
      cdp,
      checkInteractable,
    });
    this.#logger.debug('type:start', { selector, text, wait, timeout, delay });
    await this.#inputHelper.type({
      cdp: res.cdp,
      text,
      humanLike: this.#humanLike,
      delay,
    });
    this.#logger.debug('type:end', { selector, text, wait, timeout, delay });

    return {
      selector,
      cdp: res.cdp,
      x: res.x,
      y: res.y,
      text,
      time: Date.now() - start,
    };
  }

  public async click({
    x: xPos,
    y: yPos,
    selector,
    wait,
    scroll,
    visible,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
    checkInteractable = false,
  }: {
    selector?: string;
    x?: number;
    y?: number;
    wait: boolean;
    scroll: boolean;
    visible: boolean;
    timeout?: number;
    cdp?: Connection;
    checkInteractable?: boolean;
  }): Promise<{
    cdp: Connection;
    time: number;
    selector?: string;
    x?: number;
    y?: number;
  }> {
    this.#logger.debug('click', {
      selector,
      xPos,
      yPos,
      wait,
      scroll,
      timeout,
      visible,
    });

    if (!selector && !xPos && !yPos) {
      throw new Error(`Click expects one of selector, x, or y`);
    }
    const start = Date.now();
    const isDeep = selector && isDeepSelector(selector);

    if (scroll && selector && !isDeep) {
      // Scroll to element pos about halfway through viewport
      await this.scroll({
        cdp,
        selector,
        wait,
        visible,
      });
    }

    const { x, y, connection } =
      xPos && yPos
        ? {
            x: xPos,
            y: yPos,
            connection: cdp,
          }
        : await (async () => {
            const {
              cdp: connection,
              x: leftPos,
              y: topPos,
              width,
              height,
            } = await this.waitForSelector({
              cdp,
              selector: selector!,
              timeout: wait ? timeout : 100,
              visible,
              checkInteractable: checkInteractable && !isDeep,
            });
            const centerX = leftPos + width / 2;
            const centerY = topPos + height / 2;
            const offsetX = width * 0.33;
            const offsetY = height * 0.33;
            const x = centerX + getRandomArbitrary(-offsetX, offsetX);
            const y = centerY + getRandomArbitrary(-offsetY, offsetY);

            return {
              connection,
              width,
              height,
              x,
              y,
            };
          })();

    await Promise.all([
      this.#inputHelper.move({
        cdp: connection,
        x,
        y,
        smooth: this.#humanLike,
      }),
      this.#inputHelper.click({
        cdp: connection,
        x,
        y,
      }),
    ]);

    this.#logger.debug('click:end', { selector, wait, scroll, timeout });

    return {
      cdp: connection,
      x,
      y,
      selector,
      time: Date.now() - start,
    };
  }

  public async checkbox({
    selector,
    checked,
    wait,
    scroll,
    visible,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
    checkInteractable = false,
  }: {
    selector: string;
    checked: boolean;
    wait: boolean;
    scroll: boolean;
    visible: boolean;
    timeout?: number;
    cdp?: Connection;
    checkInteractable?: boolean;
  }): Promise<{
    time: number;
    selector?: string;
    x?: number;
    y?: number;
  }> {
    this.#logger.debug('checkbox', {
      selector,
      wait,
      scroll,
      timeout,
      visible,
      checkInteractable,
    });
    const start = Date.now();

    const { x, y, connection } = await (async () => {
      const {
        x: leftPos,
        y: topPos,
        width,
        height,
        cdp: connection,
      } = await this.waitForSelector({
        selector: selector!,
        timeout: wait ? timeout : 100,
        visible,
        cdp,
        checkInteractable,
      });
      return {
        connection,
        width,
        height,
        x: leftPos + width / getRandomArbitrary(2, 10),
        y: topPos + height / getRandomArbitrary(2, 10),
      };
    })();

    const escaped = selector.replace(/\"/gi, '\\"');
    const { value: isChecked } = await this.evaluate({
      timeout,
      cdp: connection,
      content: `(${selectorIsChecked.toString()})("${escaped}")`,
    });

    if (typeof isChecked === 'string') {
      throw new Error(isChecked);
    }

    if (isChecked === checked) {
      this.#logger.debug('checkbox:end', {
        selector,
        wait,
        scroll,
        timeout,
        checked,
      });
      return {
        x,
        y,
        selector,
        time: Date.now() - start,
      };
    }

    const clickResponse = await this.click({
      selector,
      wait,
      scroll,
      visible,
      timeout,
      cdp: connection,
      checkInteractable,
    });

    const { value: isCheckedAfterClick } = await this.evaluate({
      timeout,
      cdp: connection,
      content: `(${selectorIsChecked.toString()})("${escaped}")`,
    });
    if (isCheckedAfterClick !== checked) {
      throw new Error('Checked status did not change after clicking');
    }

    this.#logger.debug('checkbox:end', {
      selector,
      wait,
      scroll,
      timeout,
      checked,
    });

    return {
      x: clickResponse.x,
      y: clickResponse.y,
      selector,
      time: Date.now() - start,
    };
  }

  public async hover({
    selector,
    x,
    y,
    wait,
    scroll,
    visible,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
  }: {
    selector?: string;
    x?: number;
    y?: number;
    wait: boolean;
    scroll: boolean;
    visible: boolean;
    timeout?: number;
    cdp?: Connection;
  }): Promise<{
    selector?: string;
    x?: number;
    y?: number;
    time: number;
  }> {
    this.#logger.debug('hover', {
      selector,
      x,
      y,
      wait,
      scroll,
      timeout,
      visible,
    });
    const start = Date.now();
    let connection = cdp;

    const validPoint = x != undefined && y != undefined;
    if (!selector && !validPoint) {
      throw new Error(
        `hover needs one of x and y, or selector, to work properly`,
      );
    }

    if (selector && validPoint) {
      throw new Error(
        `hover needs only one of x and y, or selector, to work properly`,
      );
    }

    if (selector) {
      const {
        cdp,
        x: leftPos,
        y: topPos,
        width,
        height,
      } = await this.waitForSelector({
        selector,
        timeout: wait ? timeout : 100,
        visible,
      });

      connection = cdp;

      if (scroll && !isDeepSelector(selector)) {
        // Scroll to element pos about halfway through viewport
        await this.scroll({
          selector,
          wait,
          visible,
        });
      }
      x = leftPos + width / 2;
      y = topPos + height / 2;
    } else {
      const { value: window } = await this.evaluate({
        cdp,
        timeout,
        content: `{
        width: window.innerWidth,
        height: window.innerHeight,
      }`,
      });

      if (scroll) {
        const scrollTo = {
          x: (x as number) - window.width / 2,
          y: (y as number) - window.height / 2,
        };
        await this.evaluate({
          timeout,
          cdp,
          content: `window.scrollTo(${scrollTo.x}, ${scrollTo.y})`,
        });
      }

      const { value: finalScroll } = await this.evaluate({
        timeout,
        cdp,
        content: `{
          x: window.scrollX,
          y: window.scrollY,
        }`,
      });

      x = (x as number) - finalScroll.x;
      y = (y as number) - finalScroll.y;

      // avoid exceeding window limits
      x = x < 0 ? 0 : x;
      y = y < 0 ? 0 : y;
      x = x > window.width ? window.width : x;
      y = y > window.height ? window.height : y;
    }

    await sleep(100);

    this.#inputHelper.move({
      cdp: connection,
      x: x!,
      y: y!,
      smooth: this.#humanLike,
    });

    const { value: finalScroll } = await this.evaluate({
      timeout,
      cdp: connection,
      content: `{
      x: window.scrollX,
      y: window.scrollY,
    }`,
    });
    const xOnPage = x + finalScroll.x;
    const yOnPage = y + finalScroll.y;
    this.#logger.debug('hover:end', { selector, wait, scroll, timeout });

    return {
      x: xOnPage,
      y: yOnPage,
      selector,
      time: Date.now() - start,
    };
  }

  public async text({
    clean,
    selector = 'html',
    visible,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
  }: {
    clean?: {
      removeNonTextNodes: boolean;
      removeAttributes: boolean;
      removeRegex: boolean;
      regexes: string[];
      selectors: string[];
      attributes?: string[];
    };
    selector?: string;
    visible: boolean;
    timeout?: number;
    cdp?: Connection;
  }): Promise<{
    text: string;
    time: number;
  }> {
    this.#logger.trace('text', { selector, visible, timeout });
    const start = Date.now();

    await this.waitForSelector({ cdp, selector, timeout, visible });

    if (clean) {
      if (clean.removeNonTextNodes) {
        await this.evaluate({
          cdp,
          timeout,
          content: `(${removeSelectors.toString()})([${clean.selectors.map((i) => `"${i}"`).join(', ')}])`,
        });
      }

      if (clean.removeAttributes) {
        await this.evaluate({
          cdp,
          timeout,
          content: `(${removeAttributes.toString()})(${clean.attributes ? `[${clean.attributes.map((i) => `"${i}"`).join(', ')}]` : undefined})`,
        });
      }
    }
    const escaped = selector.replace(/\"/gi, '\\"') || '';
    const { value: innerText } = await this.evaluate({
      cdp,
      timeout,
      content: `(${getNodeText.toString()})("${escaped}")`,
    });

    const text = clean?.removeRegex
      ? clean.regexes.reduce((v, r) => {
          const reg = new RegExp(r, 'gi');
          return v.replace(reg, ' ');
        }, innerText)
      : innerText;

    return {
      text,
      time: Date.now() - start,
    };
  }

  public async querySelector({
    selector,
    visible,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
  }: {
    cdp?: Connection;
    visible: boolean;
    timeout?: number;
    selector: string;
  }) {
    this.#logger.trace('querySelector', { selector, visible, timeout });

    // Escape values so that they're properly stringified into code
    const escaped = selector.replace(/\"/gi, '\\"');
    const { value } = await this.evaluate({
      cdp,
      timeout,
      content: `(${getQuerySelector.toString()})("${escaped}")`,
    });

    return JSON.parse(value);
  }

  public async querySelectorAll({
    selector,
    visible,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
  }: {
    visible: boolean;
    timeout?: number;
    selector: string;
    cdp?: Connection;
  }) {
    this.#logger.trace('querySelectorAll', { selector, visible, timeout });

    // Escape values so that they're properly stringified into code
    const escaped = selector.replace(/\"/gi, '\\"');
    const { value } = await this.evaluate({
      cdp,
      timeout,
      content: `(${getQuerySelectorAll.toString()})("${escaped}")`,
    });

    return JSON.parse(value);
  }

  public async content({
    html,
    waitUntil,
    cdp = this.mainCDP,
    timeout = this.#defaultTimeout,
  }: {
    html: string;
    waitUntil: waitUntil;
    cdp?: Connection;
    timeout?: number;
  }) {
    const start = Date.now();
    this.#logger.trace('content', { html, waitUntil });
    const { value: url } = await this.evaluate({
      cdp,
      timeout,
      content: `window.location.href`,
    });

    // Escape values so that they're properly stringified into code
    const escaped = html.replace(/\"/gi, '\\"');
    await this.evaluate({
      cdp,
      timeout,
      content: `(${setContent.toString()})("${escaped}")`,
    });

    return waitUntil
      ? this.waitForNavigation(waitUntil, timeout)
      : {
          url,
          status: 200,
          time: Date.now() - start,
        };
  }

  public async html({
    clean,
    selector,
    visible,
    timeout = this.#defaultTimeout,
    cdp = this.mainCDP,
  }: {
    clean?: {
      removeNonTextNodes: boolean;
      removeAttributes: boolean;
      removeRegex: boolean;
      selectors: string[];
      attributes?: string[];
      regexes: string[];
    };
    selector?: string;
    visible: boolean;
    timeout?: number;
    cdp?: Connection;
  }) {
    this.#logger.trace('html', { selector, visible, timeout });
    const start = Date.now();
    let connection = cdp;

    if (selector) {
      [
        ({ cdp: connection } = await this.waitForSelector({
          cdp,
          selector,
          timeout,
          visible,
        })),
      ];
    }
    const escaped = selector?.replace(/\"/gi, '\\"') || '';

    if (clean) {
      if (clean.removeNonTextNodes) {
        await this.evaluate({
          cdp: connection,
          timeout,
          content: `(${removeSelectors.toString()})([${clean.selectors.map((i) => `"${i}"`).join(', ')}])`,
        });
      }

      if (clean.removeAttributes) {
        await this.evaluate({
          cdp: connection,
          timeout,
          content: `(${removeAttributes.toString()})(${clean.attributes ? `[${clean.attributes.map((i) => `"${i}"`).join(', ')}]` : undefined})`,
        });
      }
    }

    const { value: innerHTMLResponse } = await this.evaluate({
      cdp: connection,
      timeout,
      content: `(${getContent.toString()})("${escaped}")`,
    });

    const html = clean?.removeRegex
      ? clean.regexes.reduce((v, r) => {
          const reg = new RegExp(r, 'gi');
          return v.replace(reg, ' ');
        }, innerHTMLResponse)
      : innerHTMLResponse;

    return {
      html,
      time: Date.now() - start,
    };
  }

  public async reload({
    waitUntil,
    timeout = this.#defaultTimeout,
  }: {
    waitUntil: waitUntil;
    timeout?: number;
  }) {
    this.#logger.trace('reload', waitUntil, timeout);
    return this.goInHistory(0, waitUntil, timeout);
  }

  public async forward({
    waitUntil,
    timeout = this.#defaultTimeout,
  }: {
    waitUntil: waitUntil;
    timeout?: number;
  }) {
    this.#logger.trace('forward', waitUntil, timeout);
    return this.goInHistory(+1, waitUntil, timeout);
  }

  public async back({
    waitUntil,
    timeout = this.#defaultTimeout,
  }: {
    waitUntil: waitUntil;
    timeout?: number;
  }) {
    this.#logger.trace('back', waitUntil, timeout);
    return this.goInHistory(-1, waitUntil, timeout);
  }

  public async goto({
    url,
    waitUntil,
    timeout = this.#defaultTimeout,
  }: {
    url: string;
    waitUntil?: waitUntil;
    timeout?: number;
  }) {
    this.#logger.trace('goto', { url, waitUntil, timeout });
    const apiKey = getTokenFromRequest(this.#req);
    if (apiKey) {
      logUrlVisit(url, 'bql', apiKey, this.#config).catch((error) => {
        console.error('Failed to log URL visit:', error);
      });
    }

    const [response] = await Promise.all([
      this.waitForNavigation(waitUntil!, timeout),
      this.mainCDP.send('Page.navigate', { url }),
    ]);

    return response;
  }

  public setTTL(ttl: number) {
    this.#logger.trace('setTTL', ttl);
    return (this.#ttl = ttl);
  }

  public async close() {
    this.#logger.info('close', this.#ttl);
    this.#browser.getCDPProxy()?.setKeepUntil(this.#ttl);
    this.#running = false;
    this.#timers.forEach((t) => {
      clearTimeout(t);
      clearInterval(t);
    });
    [this.mainCDP, this.browserCDP].forEach((cdp) => {
      cdp?.removeAllListeners();
      cdp?.dispose();
    });
  }

  public async reconnect(ttl: number) {
    this.#logger.trace('reconnect', ttl);
    this.#ttl = ttl;

    const apiKey = getTokenFromRequest(this.#req);
    const maxReconnectTime = this.#config.getMaxReconnectTime(apiKey);

    if (ttl > maxReconnectTime) {
      if (apiKey) {
        logReconnectUrlCreated(
          apiKey,
          'bql',
          false,
          ttl,
          this.#config,
          this.#logger,
        );
      }
      throw new Error(`Reconnect time exceeds your current plans limits.`);
    }
    const cdpURL = new URL(this.#webSocketDebuggerUrl!);
    const externalAddressURL = new URL(this.#config.getExternalAddress());
    const externalWebSocketURL = new URL(
      this.#config.getExternalWebSocketAddress(),
    );
    const browserWSEndpoint = this.#browser.publicWSEndpoint();
    const browser =
      this.#browser instanceof ChromiumStealthBrowser ? 'chromium' : 'chrome';
    const browserQLEndpoint = makeExternalURL(
      externalAddressURL.href,
      browser,
      '/bql',
      this.#browser.wsEndpoint()?.split('/').pop()!,
    );
    const webSocketDebuggerUrl = makeExternalURL(
      externalWebSocketURL.href,
      cdpURL.pathname,
    );
    const devtoolsFrontendUrl = makeExternalURL(
      externalAddressURL.href,
      `/devtools/devtools_app.html?${externalWebSocketURL.protocol.replace(':', '')}=${externalAddressURL.host}${externalAddressURL.pathname}${cdpURL.pathname}`,
    );

    if (apiKey) {
      logReconnectUrlCreated(
        apiKey,
        'bql',
        true,
        ttl,
        this.#config,
        this.#logger,
      );
    }

    return {
      browserWSEndpoint,
      browserQLEndpoint,
      devtoolsFrontendUrl,
      webSocketDebuggerUrl,
    };
  }

  public async reject({
    enabled,
    url,
    method,
    type,
    operator,
  }: {
    enabled: boolean;
    url?: string[];
    method?: string[];
    type?: string[];
    operator: operators;
  }) {
    const start = Date.now();

    if (enabled && !url && !method && !type) {
      throw new Error(
        `One of "url", "method" or "type" must be specified to reject requests`,
      );
    }

    if (!enabled) {
      await this.removeInterceptorsByAction('abort');
      return {
        enabled,
        time: Date.now() - start,
      };
    }

    await this.addFetchInterceptors({
      action: 'abort',
      method,
      operator,
      type,
      url,
    });

    return {
      enabled,
      time: Date.now() - start,
    };
  }

  public async solve({
    timeout = this.#defaultTimeout,
    type,
    wait,
  }: {
    type?: solveTypes;
    timeout?: number;
    wait: boolean;
  }) {
    // Use the unified solveCaptchaAutoDetect method for all cases
    return this.solveCaptchaAutoDetect({ wait, timeout, type });
  }

  /**
   * Auto-detects and solves any supported CAPTCHA type without manual specification.
   * Uses getRecaptchaWidgetInfo for robust reCAPTCHA detection and identifyCaptcha for other types.
   * Optionally accepts a specific type to skip auto-detection and solve directly.
   */
  public async solveCaptchaAutoDetect({
    timeout = this.#defaultTimeout,
    wait,
    type,
    cdp = this.mainCDP,
  }: {
    timeout?: number;
    wait: boolean;
    type?: solveTypes;
    cdp?: Connection;
  }) {
    // If type is provided, skip auto-detection and use specific solver
    if (type) {
      this.#logger.debug(`Using provided CAPTCHA type: ${type}`);

      if (type === 'hcaptcha') {
        return this.solveHCaptcha({ wait, timeout, cdp });
      }

      if (type === 'recaptcha') {
        // Try V2 solver first
        const v2Result = await this.solveRecaptcha({ wait, timeout, cdp });
        // If V2 solver found no sitekey, automatically fallback to V3 solver
        if (!v2Result.found) {
          const v3Result = await this.solveRecaptchaV3({ wait, timeout, cdp });
          return v3Result;
        }
        return v2Result;
      }

      if (type === 'recaptchaV3') {
        return this.solveRecaptchaV3({ wait, timeout, cdp });
      }

      if (type === 'normal') {
        return this.solveNormalCaptcha({ wait, timeout, cdp });
      }

      if (type === 'geetest') {
        return this.solveGeeTest({ wait, timeout });
      }

      if (type === 'amazonWaf') {
        return this.solveAmazonWAF({ wait, timeout });
      }

      if (type === 'friendlyCaptcha') {
        return this.solveFriendlyCaptcha({ wait, timeout, cdp });
      }

      if (type === 'textCaptcha') {
        return this.solveTextCaptcha({ wait, timeout, cdp });
      }
    }
    this.#logger.debug('Auto-detecting CAPTCHA type...');
    const start = Date.now();
    const poll = 200;
    const time = wait ? timeout : 0;

    try {
      const captchaType = await asyncPoll(
        async () => {
          if (!this.#running) {
            return null;
          }

          try {
            const { value } = await this.evaluate({
              cdp,
              content: `(${identifyCaptchaBQL.toString()})()`,
            });

            if (value && value !== 'unknown') {
              this.#logger.debug(
                `Found CAPTCHA via identifyCaptchaBQL: ${value}`,
              );
              return value;
            }
            return null;
          } catch (e) {
            this.#logger.debug('identifyCaptchaBQL detection failed:', e);
            return null;
          }
        },
        poll,
        time,
      ).catch((error) => {
        this.#logger.error(
          'Error during auto-detection CAPTCHA solving:',
          error,
        );
        return null;
      });

      this.#logger.debug(`Detected CAPTCHA type: ${captchaType || 'none'}`);

      // If no CAPTCHA type detected, return not found
      if (!captchaType || captchaType === 'unknown') {
        return {
          found: false,
          solved: false,
          time: Date.now() - start,
          token: null,
        };
      }

      // Route to the appropriate solver based on detected type
      switch (captchaType) {
        case 'recaptcha':
        case 'recaptcha-enterprise':
        case 'recaptcha-invisible':
        case 'recaptcha-invisible-enterprise':
          return this.solveRecaptcha({ wait, timeout, cdp });

        case 'recaptcha-v3':
        case 'recaptcha-v3-enterprise':
          return this.solveRecaptchaV3({ wait, timeout, cdp });

        case 'hcaptcha':
          return this.solveHCaptcha({ wait, timeout, cdp });

        case 'cloudflare':
          return this.solveCloudFlare({ wait, timeout, cdp });

        case 'geetest':
          return this.solveGeeTest({ wait, timeout });

        case 'friendlycaptcha':
          return this.solveFriendlyCaptcha({ wait, timeout, cdp });

        case 'normal':
          return this.solveNormalCaptcha({ wait, timeout, cdp });

        case 'textcaptcha':
          return this.solveTextCaptcha({ wait, timeout, cdp });

        case 'funcaptcha':
        case 'arkoselabs':
        case 'capy':
        case 'keycaptcha':
        case 'lemin':
        case 'mtcaptcha':
        case 'yandex':
          // Not yet implemented in BQL, return not found
          this.#logger.warn(
            `CAPTCHA type "${captchaType}" detected but not yet supported in BQL`,
          );
          return {
            found: true,
            solved: false,
            time: Date.now() - start,
            token: null,
          };

        default:
          this.#logger.warn(`Unknown CAPTCHA type detected: ${captchaType}`);
          return {
            found: true,
            solved: false,
            time: Date.now() - start,
            token: null,
          };
      }
    } catch (error: any) {
      this.#logger.error('Error during auto-detection CAPTCHA solving:', error);
      return {
        found: false,
        solved: false,
        time: Date.now() - start,
        token: null,
      };
    }
  }

  public async liveURL({
    timeout = this.#defaultTimeout,
    quality,
    type,
    interactable,
    resizable,
  }: {
    timeout?: number;
    quality: number;
    type: string;
    interactable: boolean;
    resizable: boolean;
  }) {
    this.#logger.trace('liveURL', { timeout, quality, type, interactable });
    const apiKey = getTokenFromRequest(this.#req);
    const isFree = this.#config.isFreePlan(apiKey);

    if (isFree) {
      if (apiKey) {
        logLiveUrlInvoked({
          created: false,
          token: apiKey,
          endpointType: 'baas',
          type,
          quality,
          interactable,
          timeout,
          config: this.#config,
          logger: this.#logger,
        });
      }
      throw new BadRequest(`Your plan does not support Live URLs`);
    }

    const browserID = this.#browser.wsEndpoint()!.split('/').pop();
    const pageID = this.mainCDP.url().split('/').pop();
    const liveURLLocation = `${browserID}.${pageID}`;
    const liveURLId = fileSystem.saveId(liveURLLocation, timeout, {
      type,
      quality,
      interactable,
      resizable,
    });

    this.#browser.getCDPProxy()?.setKeepUntil(timeout);

    if (apiKey) {
      logLiveUrlInvoked({
        created: true,
        token: apiKey,
        endpointType: 'baas',
        type,
        quality,
        interactable,
        timeout,
        liveURLId,
        config: this.#config,
        logger: this.#logger,
      });
    }

    return {
      liveURLId,
      liveURL: makeExternalURL(
        this.#config.getExternalAddress(),
        'live',
        `index.html?i=${liveURLId}&t=${timeout}`,
      ),
    };
  }

  public async verify({
    timeout = this.#defaultTimeout,
    type,
    wait,
  }: {
    type: verifyTypes;
    timeout?: number;
    wait: boolean;
  }) {
    if (type === 'cloudflare') {
      return this.solveCloudFlare({ wait, timeout });
    }
    return null;
  }

  public async viewport({
    width,
    height,
    deviceScaleFactor = 1,
    mobile = false,
  }: {
    width: number;
    height: number;
    deviceScaleFactor?: number;
    mobile?: boolean;
  }): Promise<{
    width: number;
    height: number;
    deviceScaleFactor: number;
    mobile: boolean;
    time: number;
  }> {
    const start = Date.now();

    // Validate inputs
    if (width <= 0 || height <= 0) {
      throw new Error('Width and height must be positive numbers');
    }

    if (deviceScaleFactor <= 0) {
      throw new Error('Device scale factor must be a positive number');
    }

    // Use CDP to set viewport dimensions
    await this.mainCDP.send('Emulation.setDeviceMetricsOverride', {
      width: Math.floor(width),
      height: Math.floor(height),
      deviceScaleFactor,
      mobile,
    });

    this.#logger.debug(
      `Set viewport to ${width}x${height} with scale factor ${deviceScaleFactor} (mobile: ${mobile})`,
    );

    return {
      width: Math.floor(width),
      height: Math.floor(height),
      deviceScaleFactor,
      mobile,
      time: Date.now() - start,
    };
  }

  public async getWindowSize() {
    const fn = () => {
      const { height, width } = window.screen!;
      return { height, width, pageTop: 0, pageLeft: 0 };
    };

    const res = await this.evaluate({
      content: `(${fn.toString()})()`,
    });

    return res.value;
  }

  public async getPageSize() {
    const res = await this.mainCDP.send('Page.getLayoutMetrics');

    return {
      height: Math.max(
        res.contentSize.height,
        res.visualViewport.clientHeight,
        res.layoutViewport.clientHeight,
      ),
      width: res.contentSize.width,
      pageTop: res.layoutViewport.pageY,
      pageLeft: res.layoutViewport.pageX,
    };
  }

  private async getViewport(): Promise<VisualViewport> {
    const fn = () => {
      const { height, width, pageTop, pageLeft } = window.visualViewport!;
      return { height, width, pageTop, pageLeft };
    };

    const res = await this.evaluate({
      content: `(${fn.toString()})()`,
    });

    return res.value as VisualViewport;
  }
}

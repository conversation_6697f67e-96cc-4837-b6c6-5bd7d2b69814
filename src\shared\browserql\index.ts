import { Logger } from '@browserless.io/browserless';
import { graphql, buildSchema } from 'graphql';
import { loadSchema } from './schema/index.js';
import resolvers from './schema/resolvers.js';
import { Context } from './context.js';
import { BodySchema, BQLRequest } from './bql-post.chromium.http.js';
import { ChromiumStealthBrowser } from '../../browsers/chromium.stealth.js';
import { SharedConfig } from '../../config.js';

const asyncSchema = (() => loadSchema())();

export const bqlIntrospection = async (body: BodySchema) => {
  const schemaString = await asyncSchema;
  const schema = buildSchema(schemaString);
  const { query, variables, operationName } = body;

  return graphql({
    schema,
    operationName,
    source: query,
    variableValues: variables,
  });
};

export const bql = async (
  browser: ChromiumStealthBrowser,
  config: SharedConfig,
  logger: Logger,
  body: BodySchema,
  humanLike: boolean,
  blockConsentModals: boolean,
  req: BQLRequest,
) => {
  const schemaString = await asyncSchema;
  const schema = buildSchema(schemaString);

  const { query, variables, operationName } = body;
  const context = new Context(
    browser,
    config,
    logger,
    humanLike,
    blockConsentModals,
    req,
  );

  try {
    const res = await graphql({
      schema,
      operationName,
      source: query,
      rootValue: resolvers(),
      contextValue: context,
      variableValues: variables,
    });
    return res;
  } finally {
    context.close();
  }
};

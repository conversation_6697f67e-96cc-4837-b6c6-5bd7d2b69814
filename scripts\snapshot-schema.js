// NOTE: This should be done by a CI box and only on approved changes!
import { dedent, prompt } from '@browserless.io/browserless';
import path from 'path';
import fs from 'fs/promises';
import { graphql, getIntrospectionQuery, buildSchema } from 'graphql';
import { loadSchema } from '../build/shared/browserql/schema/index.js';

const schemaString = await loadSchema();
const schema = buildSchema(schemaString);
const snapshotLocation = path.join(process.cwd(), '__schema_snapshot__.json');

if (!process.env.CI) {
  const proceed = await prompt(
    console.log(
      dedent(`
    🚨 This script is meant to be executed by CI! 🚨

    This script will snapshot the current GraphQL schema, which we
    check in PRs if breaking changes exist. Running this outside of
    CI might mean allowing breaking changes to be introduced.

    Do you wish to proceed (y/n)?
    `),
    ),
  );
  if (!proceed.toLowerCase().includes('y')) {
    console.log('Exiting');
    process.exit(0);
  }
}

// Execute the introspection query on the schema and save the json
graphql({
  schema,
  source: getIntrospectionQuery(),
  rootValue: {},
  contextValue: {},
  variableValues: {},
})
  .then((res) => fs.writeFile(snapshotLocation, JSON.stringify(res.data)))
  .then(() => {
    console.log('Successfully snapshotted the schema!');
    process.exit(0);
  })
  .catch((e) => {
    console.log('Snapshotting was unsuccessful: ', e);
    process.exit(1);
  });

import { expect } from 'chai';
import { Protocol } from 'puppeteer-core';
import { findPatternForRequest } from '../../shared/browserql/utils.js';
import { FetchInterception } from '../../shared/browserql/types.js';

describe('Utils', () => {
  describe('findPatternForRequest', () => {
    describe('or', () => {
      it('matches when no patterns are specified', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('matches URL patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          url: ['*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('matches multiple URL patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          url: ['*cnn.com*', '*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('matches method patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          method: ['get'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('matches multiple method patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          method: ['post', 'get'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('matches type patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          type: ['document'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('matches multiple type patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          type: ['stylesheet', 'document'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('does not match url requests', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          url: ['*cnn.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('does not match method requests', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          method: ['post'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('does not match type requests', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          type: ['xhr'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('matches requests when a method matches but not URL', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          method: ['GET'],
          url: ['*cnn.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('does not match requests when no parameters match', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'or',
          type: ['document'],
          method: ['GET'],
          url: ['*cnn.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'POST',
          },
          resourceType: 'XHR',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });
    });

    describe('and', () => {
      it('matches when no patterns are specified', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('matches URL patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          url: ['*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('does not match multiple URL patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          url: ['*cnn.com*', '*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('matches requests that meet multiple conditions', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          method: ['get'],
          url: ['*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('matches method patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          method: ['get'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('does not match multiple method patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          method: ['post', 'get'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('matches type patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          type: ['document'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.eql(pattern);
      });

      it('does not match multiple type patterns', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          type: ['stylesheet', 'document'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('does not match url requests', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          url: ['*cnn.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('does not match method requests', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          method: ['post'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('does not match type requests', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          type: ['xhr'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });

      it('does not match requests when a method matches but not URL', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          method: ['GET'],
          url: ['*cnn.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).not.to.eql(pattern);
      });

      it('does not match requests when no parameters match', () => {
        const pattern: FetchInterception = {
          action: 'abort',
          operator: 'and',
          type: ['document'],
          method: ['GET'],
          url: ['*cnn.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'POST',
          },
          resourceType: 'XHR',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [pattern])).to.not.eql(pattern);
      });
    });

    describe('cardinality', () => {
      it('matches "abort" patterns over other matching patterns when no patterns are specified', () => {
        const abort: FetchInterception = {
          action: 'abort',
          operator: 'or',
        };
        const proxy: FetchInterception = {
          action: 'proxy',
          operator: 'or',
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [proxy, abort])).to.eql(abort);
      });

      it('matches "abort" patterns over other matching patterns', () => {
        const abort: FetchInterception = {
          action: 'abort',
          operator: 'or',
          url: ['*example.com*'],
        };
        const proxy: FetchInterception = {
          action: 'proxy',
          operator: 'or',
          url: ['*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [proxy, abort])).to.eql(abort);
      });

      it('matches "proxy" patterns over "continue" patterns', () => {
        const cont: FetchInterception = {
          action: 'continue',
          operator: 'or',
          url: ['*example.com*'],
        };
        const proxy: FetchInterception = {
          action: 'proxy',
          operator: 'or',
          url: ['*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [cont, proxy])).to.eql(proxy);
      });

      it('matches "abort" as highest priority over all others', () => {
        const proxy: FetchInterception = {
          action: 'proxy',
          operator: 'or',
          url: ['*example.com*'],
        };
        const cont: FetchInterception = {
          action: 'continue',
          operator: 'or',
          url: ['*example.com*'],
        };
        const abort: FetchInterception = {
          action: 'abort',
          operator: 'or',
          url: ['*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [proxy, cont, abort])).to.eql(
          abort,
        );
      });

      it('matches "proxy" by pattern even though "abort" might be present', () => {
        const cont: FetchInterception = {
          action: 'continue',
          operator: 'or',
          url: ['*example.com*'],
        };
        const abort: FetchInterception = {
          action: 'abort',
          operator: 'or',
          url: ['*cnn.com*'],
        };
        const proxy: FetchInterception = {
          action: 'proxy',
          operator: 'or',
          url: ['*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [cont, abort, proxy])).to.eql(
          proxy,
        );
      });

      it('matches "continue" by pattern even though "abort" and "proxy" might be present if they do not match', () => {
        const abort: FetchInterception = {
          action: 'abort',
          operator: 'or',
          url: ['*cnn.com*'],
        };
        const proxy: FetchInterception = {
          action: 'proxy',
          operator: 'or',
          url: ['*cnn.com*'],
        };
        const cont: FetchInterception = {
          action: 'continue',
          operator: 'or',
          url: ['*example.com*'],
        };
        const request = {
          request: {
            url: 'https://example.com',
            method: 'get',
          },
          resourceType: 'Document',
        } as Protocol.Fetch.RequestPausedEvent;

        expect(findPatternForRequest(request, [abort, proxy, cont])).to.eql(
          cont,
        );
      });
    });
  });
});

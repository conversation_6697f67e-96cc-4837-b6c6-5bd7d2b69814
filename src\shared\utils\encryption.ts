import { Logger } from '@browserless.io/browserless';
import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 12; // For GCM mode
const SALT_LENGTH = 16;
const TAG_LENGTH = 16;
const KEY_LENGTH = 32; // 256 bits
const ITERATIONS = 100000;

export class SessionEncryption {
  protected logger = new Logger(`session-encryption`);
  private sessionEncryptionKey: Buffer;

  constructor(sessionEncryptionKey: string) {
    if (!sessionEncryptionKey || sessionEncryptionKey.length < 32) {
      throw new Error('Session encryption key must be at least 32 bytes long');
    }
    // Derive a consistent key from the session key
    this.sessionEncryptionKey = crypto.pbkdf2Sync(
      sessionEncryptionKey,
      'browserless-session-encryption-salt',
      ITERATIONS,
      KEY_LENGTH,
      'sha256',
    );
    this.logger.debug('Session encryption initialized');
  }

  /**
   * Encrypts session data using a combination of the session key and session-specific salt
   * @param sessionId The unique session ID to use in encryption
   * @param data The data to encrypt
   * @returns Encrypted data as a string (includes IV, salt, and auth tag)
   */
  public encrypt(sessionId: string, data: unknown): string {
    const start = Date.now();
    try {
      // Generate a unique salt for this session
      const salt = crypto.randomBytes(SALT_LENGTH);

      // Derive a session-specific key using the session key and session ID
      const sessionKey = crypto.pbkdf2Sync(
        this.sessionEncryptionKey,
        sessionId + salt.toString('hex'),
        ITERATIONS,
        KEY_LENGTH,
        'sha256',
      );

      // Generate a random IV
      const iv = crypto.randomBytes(IV_LENGTH);

      // Create cipher
      const cipher = crypto.createCipheriv(ALGORITHM, sessionKey, iv);

      // Encrypt the data
      const jsonData = JSON.stringify(data);
      const encrypted = Buffer.concat([
        cipher.update(jsonData, 'utf8'),
        cipher.final(),
      ]);

      // Get the auth tag
      const tag = cipher.getAuthTag();

      // Combine all components: salt + iv + tag + encrypted data
      const result = Buffer.concat([salt, iv, tag, encrypted]);

      this.logger.debug(
        `Session data encrypted successfully in ${Date.now() - start}ms`,
        { sessionId },
      );
      return result.toString('base64');
    } catch (error: any) {
      this.logger.error('Encryption failed:', {
        error: error.message,
        sessionId,
      });
      throw new Error('Failed to encrypt session data');
    }
  }

  /**
   * Decrypts session data using the session key and session ID
   * @param sessionId The session ID used in encryption
   * @param encryptedData The encrypted data string
   * @returns The decrypted data
   */
  public decrypt(sessionId: string, encryptedData: string): any {
    const start = Date.now();
    try {
      // Convert from base64
      const buffer = Buffer.from(encryptedData, 'base64');

      // Extract components
      const salt = buffer.subarray(0, SALT_LENGTH);
      const iv = buffer.subarray(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
      const tag = buffer.subarray(
        SALT_LENGTH + IV_LENGTH,
        SALT_LENGTH + IV_LENGTH + TAG_LENGTH,
      );
      const encrypted = buffer.subarray(SALT_LENGTH + IV_LENGTH + TAG_LENGTH);

      // Recreate the session key
      const sessionKey = crypto.pbkdf2Sync(
        this.sessionEncryptionKey,
        sessionId + salt.toString('hex'),
        ITERATIONS,
        KEY_LENGTH,
        'sha256',
      );

      // Create decipher
      const decipher = crypto.createDecipheriv(ALGORITHM, sessionKey, iv);
      decipher.setAuthTag(tag);

      // Decrypt
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final(),
      ]);

      this.logger.debug(
        `Session "${sessionId}" decrypted successfully in ${Date.now() - start}ms`,
      );
      return JSON.parse(decrypted.toString('utf8'));
    } catch (error: any) {
      this.logger.error('Decryption failed:', {
        error: error.message,
        sessionId,
      });
      throw new Error('Failed to decrypt session data');
    }
  }
}

import { exists, noop, sleep } from '@browserless.io/browserless';
import { expect } from 'chai';
import puppeteer from 'puppeteer-core';
import playwright from 'playwright-core';
import Sinon from 'sinon';
import { readFile } from 'fs/promises';

import BrowserlessEnterprise from '../browserless.js';
import { EnterpriseConfig } from '../config.js';
import { EnterpriseAPI } from '../api.js';
import EnterpriseHooks from '../hooks.js';
import { join } from 'path';
import { rm } from 'fs/promises';

/**
 * @constant
 * @type {Buffer}
 *
 * All WebM files start with 4 bytes that read `1A 45 DF A3`.
 * We check for these bytes in the screencast response to determine if the
 * buffer is an actual webm and not buffered error strings.
 * See https://mimesniff.spec.whatwg.org/#signature-for-webm and
 * https://en.wikipedia.org/wiki/List_of_file_signatures for more info
 */
const webmSignature: Buffer = Buffer.from([0x1a, 0x45, 0xdf, 0xa3]);

const apiMock = Sinon.createStubInstance(EnterpriseAPI, {
  saveEnterpriseMetrics: Sinon.stub().resolves({}) as any,
});
const { playwrightVersions } = JSON.parse(
  await readFile('package.json', 'utf-8'),
);
const versions = Object.values(playwrightVersions as Record<string, string>);

describe('Enterprise WebSocket APIs', function () {
  let browserless: BrowserlessEnterprise;

  const start = async ({
    config: configOverride,
    api: apiOverride,
    hooks: hooksOverride,
  }: {
    config?: EnterpriseConfig;
    api?: EnterpriseAPI;
    hooks?: EnterpriseHooks;
  } = {}) => {
    const api = apiOverride ?? apiMock;
    const config = configOverride ?? new EnterpriseConfig();
    const hooks = hooksOverride ?? new EnterpriseHooks(config, api, '');

    browserless = new BrowserlessEnterprise({
      api,
      hooks,
      config,
    });

    await browserless.start();
    return browserless;
  };

  afterEach(async () => {
    await browserless.stop().catch(noop);
  });

  describe('Playwright multi-versions', () => {
    for (const pwVersion of versions) {
      it(`should work with ${pwVersion}`, async () => {
        await start();

        const pw = await import(pwVersion);
        const b = await pw.chromium.connect(
          'ws://localhost:3000/playwright/chromium',
        );
        expect(b, `Works with playwright-core ${pwVersion}`);
        const page = await b.newPage();
        await page.goto('https://one.one.one.one');
        expect(await page.title()).to.include('*******');
        await b.close();
      });
    }
  });

  describe('Browserless CDP API', () => {
    it('should allow liveURL CDP commands to chromium', async () => {
      await start();
      const browser = await puppeteer.connect({
        browserWSEndpoint: 'ws://localhost:3000/chromium',
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      const { liveURL, error } = await cdp.send('Browserless.liveURL' as any);
      expect(liveURL);
      expect(error).to.equal(null);
      await browser.close();
    });

    it('should emit "Browserless.captchaFound" events when found', async () => {
      await start();
      const browser = await puppeteer.connect({
        browserWSEndpoint: 'ws://localhost:3000/chromium',
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      const captchaFound = new Promise((r) =>
        cdp.on('Browserless.captchaFound', r),
      );

      // Use local HTML with reCAPTCHA instead of external site
      await page.setContent(`
        <html>
          <head>
            <script src="https://www.google.com/recaptcha/api.js" async defer></script>
          </head>
          <body>
            <form>
              <div class="g-recaptcha" data-sitekey="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"></div>
              <input type="submit" value="Submit">
            </form>
          </body>
        </html>
      `);

      // Give more time for captcha detection
      await Promise.race([captchaFound, sleep(5000)]);
      await browser.close();
    });

    it('should allow solveCaptcha commands to chromium/stealth', async () => {
      await start();
      const browser = await puppeteer.connect({
        browserWSEndpoint: 'ws://localhost:3000/chromium/stealth',
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      (cdp.send as any)('Browserless.solveCaptcha');
      await sleep(1000); // Allow error to happen
      await browser.close();
    });

    it('should allow solveCaptcha commands to core chromium', async () => {
      await start();
      const browser = await puppeteer.connect({
        browserWSEndpoint: 'ws://localhost:3000/chromium',
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      const res = await (cdp.send as any)('Browserless.solveCaptcha');
      expect(res.error).to.be.undefined;
      await browser.close();
    });

    it('should allow liveURL CDP commands to chrome', async function () {
      // chrome is not supported on arm64
      if (process.arch !== 'arm64') {
        await start();
        const browser = await puppeteer.connect({
          browserWSEndpoint: 'ws://localhost:3000/chrome',
        });

        const page = await browser.newPage();
        const cdp = await page.createCDPSession();
        const { liveURL } = await (cdp.send as any)('Browserless.liveURL');
        expect(liveURL);
        await browser.close();
      } else {
        this.skip();
      }
    });

    it('should allow pageID CDP commands to chromium', async () => {
      await start();
      const browser = await puppeteer.connect({
        browserWSEndpoint: 'ws://localhost:3000/chromium',
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      const { pageId } = await (cdp.send as any)('Browserless.pageId');
      expect(pageId);
      await browser.close();
    });

    it('should allow pageID CDP commands to chrome', async function () {
      // chrome is not supported on arm64
      if (process.arch !== 'arm64') {
        await start();
        const browser = await puppeteer.connect({
          browserWSEndpoint: 'ws://localhost:3000/chrome',
        });

        const page = await browser.newPage();
        const cdp = await page.createCDPSession();
        const { pageId } = await (cdp.send as any)('Browserless.pageId');
        expect(pageId);
        await browser.close();
      } else {
        this.skip();
      }
    });

    it('should allow heartbeat CDP commands', async () =>
      new Promise(async (resolve) => {
        const config = new EnterpriseConfig();
        config.setHeartBeatInterval(1000);
        await start({ config });
        const browser = await puppeteer.connect({
          browserWSEndpoint: 'ws://localhost:3000/chromium',
        });

        const page = await browser.newPage();
        const cdp = await page.createCDPSession();
        (cdp.on as any)('Browserless.heartbeat', async () => {
          await browser.close();
          resolve();
        });
      }));

    it('should allow recording over CDP', async () => {
      await start();
      const browser = await puppeteer.connect({
        browserWSEndpoint:
          'ws://localhost:3000/chromium?record=true&timeout=60000',
      });

      const page = await browser.newPage();
      await page.goto('http://example.com', { waitUntil: 'networkidle0' });
      const cdp = await page.createCDPSession();

      await cdp.send('Browserless.startRecording' as any);
      await sleep(3000);
      const response = await cdp.send('Browserless.stopRecording' as any);

      expect(response.error).to.equal(null);
      const file = Buffer.from(response.value, 'binary');
      expect(file.subarray(0, 4).equals(webmSignature)).to.be.true;
      await browser.close();
    });

    it('should allow reconnections', async () => {
      await start();
      const browser = await puppeteer.connect({
        browserWSEndpoint: 'ws://localhost:3000?record=true&timeout=60000',
      });

      const page = await browser.newPage();
      await page.goto('http://example.com', { waitUntil: 'networkidle0' });
      const cdp = await page.createCDPSession();

      // @ts-ignore
      // prettier-ignore
      const { error, browserWSEndpoint } = await cdp.send('Browserless.reconnect', {
        timeout: 60000,
      });
      expect(error).to.be.null;
      expect(browserWSEndpoint).to.be.a('string');
      expect(browserWSEndpoint).to.match(/^ws:\/\/.*\/reconnect\/.*$/); // Matches "ws://*/reconnect/*"

      await browser.close();
    });
  });

  describe('Download behavior', () => {
    it('should allow download to a specific place when no download is specified', async () => {
      const config = new EnterpriseConfig();
      await start();
      const browser = await playwright.chromium.connectOverCDP(
        'ws://localhost:3000/chromium',
      );

      const page = await browser.newPage();

      await page.setContent(
        `<html>
          <body>
              <h1>Download Test</h1>
              <a href="data:text/plain;charset=utf-8,Hello%20World!" download="test.txt">Download Test File</a>
          </body>
        </html>`,
      );

      await page.click('a[download]');
      await sleep(1000);
      const downloadBase = await config.getDownloadsDir();
      const file = join(downloadBase, 'test.txt');
      expect(await exists(file)).to.be.true;
      await rm(file, { force: true });
      await browser.close();
    });

    it('should allow download to a specific place with new contexts', async () => {
      const config = new EnterpriseConfig();
      await start();
      const browser = await playwright.chromium.connectOverCDP(
        'ws://localhost:3000/chromium',
      );
      const ctx = await browser.newContext();
      const page = await ctx.newPage();

      await page.setContent(
        `<html>
          <body>
              <h1>Download Test</h1>
              <a href="data:text/plain;charset=utf-8,Hello%20World!" download="test.txt">Download Test File</a>
          </body>
        </html>`,
      );

      await page.click('a[download]');
      await sleep(1000);
      const downloadBase = await config.getDownloadsDir();
      const file = join(downloadBase, 'test.txt');
      expect(await exists(file)).to.be.true;
      await rm(file, { force: true });
      await browser.close();
    });

    it('should allow download to a specific place with new contexts and download paths', async () => {
      const config = new EnterpriseConfig();
      await start();
      const browser = await playwright.chromium.connectOverCDP(
        'ws://localhost:3000/chromium',
      );
      const ctx = await browser.newContext({
        acceptDownloads: true,
      });
      const page = await ctx.newPage();

      await page.setContent(
        `<html>
          <body>
              <h1>Download Test</h1>
              <a href="data:text/plain;charset=utf-8,Hello%20World!" download="test.txt">Download Test File</a>
          </body>
        </html>`,
      );

      await page.click('a[download]');
      await sleep(1000);
      const downloadBase = await config.getDownloadsDir();
      const file = join(downloadBase, 'test.txt');
      expect(await exists(file)).to.be.true;
      await rm(file, { force: true });
      await browser.close();
    });
  });
});

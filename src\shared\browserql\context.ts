import { Logger } from '@browserless.io/browserless';
import { ChromiumStealthBrowser } from '../../browsers/chromium.stealth.js';
import { SharedConfig } from '../../config.js';
import { BrowserQLAPI } from './browser-api.js';
import { BQLRequest } from './bql-post.chromium.http.js';

export class Context {
  private browserAPI?: BrowserQLAPI;
  constructor(
    private browser: ChromiumStealthBrowser,
    private config: SharedConfig,
    private logger: Logger,
    private humanLike: boolean,
    private blockConsentModals: boolean,
    private req: BQLRequest,
  ) {}

  public async getBrowser() {
    if (this.browserAPI) {
      return this.browserAPI;
    }
    this.browserAPI = new BrowserQLAPI(
      this.browser,
      this.config,
      this.logger,
      this.humanLike,
      this.blockConsentModals,
      this.req,
    );
    await this.browserAPI.start();
    return this.browserAPI;
  }

  public getConfig(): SharedConfig {
    return this.config;
  }

  public async close() {
    if (this.browserAPI) {
      await this.browserAPI.close();
    }
  }
}

{"__schema": {"queryType": {"name": "Query"}, "mutationType": {"name": "Mutation"}, "subscriptionType": null, "types": [{"kind": "OBJECT", "name": "HTTPResponse", "description": null, "fields": [{"name": "status", "description": "The status code response of the initial page-load", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "text", "description": "The status text of the response from the initial page-load. Generally 'ok'", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": "The final URL of the page after any potential redirects or URL rewrites", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The amount of time, in milliseconds, elapsed since the start of navigation to completion", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Float", "description": "The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "HTMLResponse", "description": null, "fields": [{"name": "html", "description": "The content of the page's HTML", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The amount of time, in milliseconds, elapsed since the start of content retrieval to completion", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TextResponse", "description": null, "fields": [{"name": "text", "description": "The textual content of the page", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The amount of time, in milliseconds, elapsed since the start of text retrieval to completion", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TypeResponse", "description": null, "fields": [{"name": "selector", "description": "The selector of the element you typed into", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "text", "description": "The textual content that was typed", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "x", "description": "The X coordinate of the element, in pixels, on the page", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "y", "description": "The Y coordinate of the element, in pixels, on the page", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The amount of time, in milliseconds, elapsed since the start of typing to completion", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "HoverResponse", "description": null, "fields": [{"name": "selector", "description": "The selector text", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "x", "description": "The X coordinate in pixels, on the page", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "y", "description": "The Y coordinate in pixels, on the page", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The amount of time, in milliseconds, elapsed since the start to completion", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ClickResponse", "description": null, "fields": [{"name": "selector", "description": "The selector text if specified", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "x", "description": "The X coordinate of the click, in pixels, on the page", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "y", "description": "The Y coordinate of the click, in pixels, on the page", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The amount of time, in milliseconds, elapsed since the start of clicking to completion", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "WaitForTimeout", "description": null, "fields": [{"name": "time", "description": "The period of time elapsed, in milliseconds, waited for", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "WaitForRequest", "description": null, "fields": [{"name": "time", "description": "The period of time elapsed, in milliseconds, waited for", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": "The URL parameter used to match the response with", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "WaitForResponse", "description": null, "fields": [{"name": "time", "description": "The period of time elapsed, in milliseconds, waited for", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": "The status code response of the response", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": "The URL parameter used to match the response with", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "WaitForSelector", "description": null, "fields": [{"name": "time", "description": "The period of time elapsed, in milliseconds, waited for", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "selector", "description": "The selector waited for", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "x", "description": "The position, in pixels, left of the viewport", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "y", "description": "The position, in pixels, top of the viewport", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "width", "description": "The width, in pixels, of the element", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "height", "description": "The height, in pixels, of the element", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ScrollResponse", "description": null, "fields": [{"name": "selector", "description": "The CSS selector of the element on the page you want to scroll to", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "x", "description": "The X coordinate, in pixels, to scroll to", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "y", "description": "The Y coordinate, in pixels, to scroll to", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The amount of time, in milliseconds, elapsed since the start of scrolling to completion", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "DefaultResponse", "description": null, "fields": [{"name": "timeout", "description": "The default timeout for all methods", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TitleResponse", "description": null, "fields": [{"name": "title", "description": "The title of the current page", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "URLResponse", "description": null, "fields": [{"name": "url", "description": "The URL of the current page", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "EvaluateResponse", "description": null, "fields": [{"name": "value", "description": "The returned value of the script, if any.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The time it took for the evaluate call to happen", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VerifyResponse", "description": null, "fields": [{"name": "time", "description": "The total time it took to find, and click, the verification", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "found", "description": "If a verification was found or not", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "solved", "description": "If a verification was found, whether or not it was clicked", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "The `Boolean` scalar type represents `true` or `false`.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "CaptchaResponse", "description": null, "fields": [{"name": "time", "description": "The total time it took to find, and solve, the captcha", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "found", "description": "If a captcha was found or not", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "solved", "description": "If a captcha was found, whether or not it was solved", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "token", "description": "The solved token of the response, if any is provided", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "CaptchaTypes", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "h<PERSON><PERSON>a", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "recaptcha", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "VerifyTypes", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "cloudflare", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "CookieSameSite", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Strict", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Lax", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "None", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CookieInput", "description": null, "fields": null, "inputFields": [{"name": "name", "description": "Cookie name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "value", "description": "Cookie value", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "url", "description": "The request-URI to associate with the setting of the cookie. This value can affect the default domain, path, source port, and source scheme values of the created cookie.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "domain", "description": "Cookie domain", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "path", "description": "<PERSON>ie path", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "secure", "description": "True if cookie is secure", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "httpOnly", "description": "True if cookie is http-only", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "sameSite", "description": "Cookie SameSite type", "type": {"kind": "ENUM", "name": "CookieSameSite", "ofType": null}, "defaultValue": null}, {"name": "expires", "description": "Cookie expiration date, session cookie if not set", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ScreenshotType", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "jpeg", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "png", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "webp", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ScreenshotClip", "description": null, "fields": null, "inputFields": [{"name": "x", "description": "The x coordinate to start clipping, in pixels.", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "y", "description": "The y coordinate to start clipping, in pixels.", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "width", "description": "The width of the clip, in pixels.", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "height", "description": "The height of the clip, in pixels.", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "scale", "description": "The scale factor of the clip.", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "StandardCookie", "description": null, "fields": [{"name": "name", "description": "Cookie name", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "Cookie value", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": "The request-URI to associate with the setting of the cookie. This value can affect the default domain, path, source port, and source scheme values of the created cookie.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "domain", "description": "Cookie domain", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "path", "description": "<PERSON>ie path", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "secure", "description": "True if cookie is secure", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "httpOnly", "description": "True if cookie is http-only", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sameSite", "description": "Cookie SameSite type", "args": [], "type": {"kind": "ENUM", "name": "CookieSameSite", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "expires", "description": "Cookie expiration date, session cookie if not set", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "CookieResponse", "description": null, "fields": [{"name": "cookies", "description": "A standard cookie object with the values of the set cookies", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "StandardCookie", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The time it took to set the cookies", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "JavaScriptResponse", "description": null, "fields": [{"name": "enabled", "description": "Whether or not JavaScript is enabled on the page", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The time it took to perform this action", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ScreenshotResponse", "description": null, "fields": [{"name": "base64", "description": "The base64 encoded image of the screenshot", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "format", "description": "The format of the screenshot", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The time it took to take the screenshot", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "PDFPageFormat", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "letter", "description": "8.5in x 11in", "isDeprecated": false, "deprecationReason": null}, {"name": "legal", "description": "8.5in x 14in", "isDeprecated": false, "deprecationReason": null}, {"name": "tabloid", "description": "11in x 17in", "isDeprecated": false, "deprecationReason": null}, {"name": "ledger", "description": "17in x 11in", "isDeprecated": false, "deprecationReason": null}, {"name": "a0", "description": "33.1102in x 46.811in", "isDeprecated": false, "deprecationReason": null}, {"name": "a1", "description": "23.3858in x 33.1102in", "isDeprecated": false, "deprecationReason": null}, {"name": "a2", "description": "16.5354in x 23.3858in", "isDeprecated": false, "deprecationReason": null}, {"name": "a3", "description": "11.6929in x 16.5354in", "isDeprecated": false, "deprecationReason": null}, {"name": "a4", "description": "8.2677in x 11.6929in", "isDeprecated": false, "deprecationReason": null}, {"name": "a5", "description": "5.8268in x 8.2677in", "isDeprecated": false, "deprecationReason": null}, {"name": "a6", "description": "4.1339in x 5.8268in", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "PDFResponse", "description": null, "fields": [{"name": "base64", "description": "Base64 encoded PDF content", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "size", "description": "The size of the resulting PDF in bytes", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The time it took to generate the PDF", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ReconnectionResponse", "description": null, "fields": [{"name": "browserQLEndpoint", "description": "The fully-qualified URL to reconnect future BrowserQL sessions, eg: https://chrome.browserless.io/bql/$id. Please note that token information is not returned by this API and might be required", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "browserWSEndpoint", "description": "The fully-qualified URL of the browserWSEndpoint which can be used with other libraries like playwright or puppeteer. Please note that token information is not returned by this API and might be required", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "devtoolsFrontendUrl", "description": "The fully-qualified URL of the devtools resources for loading Chrome's developer tools remotely", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "webSocketDebuggerUrl", "description": "The underlying page's webSocketDebuggerUrl, useful for hooking libraries that operate on a page and not a browser object", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "QuerySelectorResponse", "description": null, "fields": [{"name": "id", "description": "The id property of the Element interface represents the element's identifier, reflecting the id global attribute.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "childElementCount", "description": "The Element.childElementCount read-only property returns the number of child elements of this element.", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "className", "description": "The className property of the Element interface gets and sets the value of the class attribute of the specified element.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "innerHTML", "description": "The Element property innerHTML gets the HTML or XML markup contained within the element.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "innerText", "description": "The Element property innerText gets the text contained within the element.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "localName", "description": "The Element.localName read-only property returns the local part of the qualified name of an element.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "outerHTML", "description": "The outerHTML attribute of the Element DOM interface gets the serialized HTML fragment describing the element including its descendants. It can also be set to replace the element with nodes parsed from the given string.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "Method", "description": "The various HTTP-based methods to wait for", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "GET", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "HEAD", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "POST", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PUT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DELETE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "CONNECT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "OPTIONS", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "TRACE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PATCH", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "RequestInput", "description": null, "fields": null, "inputFields": [{"name": "url", "description": "The pattern of the request URL to wait for, using glob-style pattern-matching", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "method", "description": "The HTTP Method of the request", "type": {"kind": "ENUM", "name": "Method", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "ResponseInput", "description": null, "fields": null, "inputFields": [{"name": "url", "description": "The pattern of the response URL to wait for, using glob-style pattern-matching", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "codes", "description": "The HTTP Response code(s) of the URL to wait for. Can be a single HTTP code or a list of desired codes", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "WaitUntilHistory", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "commit", "description": "Fired when network response is received and the document started loading", "isDeprecated": false, "deprecationReason": null}, {"name": "domContentLoaded", "description": "Fired when the DOMContentLoaded event is fired", "isDeprecated": false, "deprecationReason": null}, {"name": "load", "description": "Fired when the 'load' event occurs", "isDeprecated": false, "deprecationReason": null}, {"name": "networkIdle", "description": "Use with caution: Fired when there are no network connections for at least 500 ms", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "WaitUntilGoto", "description": "Options for when to consider the page has loaded and proceed with further execution", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "commit", "description": "Fired when network response is received and the document started loading", "isDeprecated": false, "deprecationReason": null}, {"name": "domContentLoaded", "description": "Fired when the DOMContentLoaded event is fired", "isDeprecated": false, "deprecationReason": null}, {"name": "load", "description": "Fired when the 'load' event occurs", "isDeprecated": false, "deprecationReason": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Indicates when the primary content of a page is visible to the user", "isDeprecated": false, "deprecationReason": null}, {"name": "firstContentful<PERSON><PERSON>t", "description": "The render time of the largest image or text block visible in the viewport, relative to when the user first navigated to the page", "isDeprecated": false, "deprecationReason": null}, {"name": "networkIdle", "description": "Use with caution: Fired when there are no network connections for at least 500 ms", "isDeprecated": false, "deprecationReason": null}, {"name": "interactiveTime", "description": "Use with caution: Chrome's best guess as to when the page becomes interactable", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "SCALAR", "name": "StringOrArray", "description": "Holds the value of a either a string or an array of strings", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "FloatOrString", "description": "Holds the value of a either a float or a string", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SelectResponse", "description": null, "fields": [{"name": "selector", "description": "The selector of the element you selected from", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "The value or values you selected from the select element", "args": [], "type": {"kind": "SCALAR", "name": "StringOrArray", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": "The amount of time, in milliseconds, elapsed since the start of selecting to completion", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Mutation", "description": null, "fields": [{"name": "preferences", "description": "Sets configuration for the entirety of the session, replacing defaults like the 30 second timeout default", "args": [{"name": "timeout", "description": "Sets a default timeout for all methods, including 'goto', 'type', 'wait', etc", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "DefaultResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "goto", "description": "Navigates to a URL with an optional waitUntil parameter and timeout parameter", "args": [{"name": "url", "description": "The fully-qualified URL of the page you'd like to navigate to", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "waitUntil", "description": "When to consider the page fully-loaded and proceed with further execution", "type": {"kind": "ENUM", "name": "WaitUntilGoto", "ofType": null}, "defaultValue": "load"}, {"name": "timeout", "description": "The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "HTTPResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "forward", "description": "Goes forward in browser history, optionally accepting waitUntil and timeout arguments. Returns null if no forward is possible", "args": [{"name": "waitUntil", "description": "When to consider the page fully-loaded and proceed with further execution", "type": {"kind": "ENUM", "name": "WaitUntilHistory", "ofType": null}, "defaultValue": "load"}, {"name": "timeout", "description": "The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "HTTPResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "back", "description": "Goes back in browser history, optionally accepting waitUntil and timeout arguments. Returns null if no back is possible", "args": [{"name": "waitUntil", "description": "When to consider the page fully-loaded and proceed with further execution", "type": {"kind": "ENUM", "name": "WaitUntilHistory", "ofType": null}, "defaultValue": "load"}, {"name": "timeout", "description": "The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "HTTPResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "reload", "description": "Reloads the given page with an optional waitUntil parameter and timeout parameter", "args": [{"name": "waitUntil", "description": "When to consider the page fully-loaded and proceed with further execution", "type": {"kind": "ENUM", "name": "WaitUntilHistory", "ofType": null}, "defaultValue": "load"}, {"name": "timeout", "description": "The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "HTTPResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "content", "description": "Sets the given HTML content on the page with an optional waitUntil parameter", "args": [{"name": "html", "description": "When present, sets the content of page to the value passed, then returns the pages content", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "waitUntil", "description": "When to consider the page fully-loaded and proceed with further execution, used in conjunction with the value parameter", "type": {"kind": "ENUM", "name": "WaitUntilHistory", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "HTTPResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "if", "description": "Triggers a nested branch of work when a given condition is *true*. Does not wait for these items and is a point-in-time check. Use the wait method if you're wanting to await certain behaviors to be present", "args": [{"name": "selector", "description": "Triggers the subsequent conditions if the selector is immediately present", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "visible", "description": "When using selectors in conditionals this options sets whether their or not to consider if they're visible to the viewport", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "request", "description": "Triggers the nested conditions if a request has been made with the following conditions", "type": {"kind": "INPUT_OBJECT", "name": "RequestInput", "ofType": null}, "defaultValue": null}, {"name": "response", "description": "Triggers the nested conditions if a response has been received with the following conditions", "type": {"kind": "INPUT_OBJECT", "name": "ResponseInput", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Mutation", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "ifnot", "description": "Triggers a nested branch of work when a given condition is *false*. This method does not wait for these items and is a point-in-time check. Use the wait method if you're wanting to await certain behaviors to be present", "args": [{"name": "selector", "description": "Triggers the subsequent conditions if the selector is immediately present", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "request", "description": "Triggers the nested conditions if a request has been made with the following conditions", "type": {"kind": "INPUT_OBJECT", "name": "RequestInput", "ofType": null}, "defaultValue": null}, {"name": "response", "description": "Triggers the nested conditions if a response has been received with the following conditions", "type": {"kind": "INPUT_OBJECT", "name": "ResponseInput", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Mutation", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "html", "description": "Returns the HTML content of the page or selector when specified", "args": [{"name": "selector", "description": "The DOM selector of the given element you want to return the HTML of", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "visible", "description": "Whether or not to return the HTMLπ content of the element only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "timeout", "description": "The maximum amount of time, in milliseconds, to wait for the selector to appear, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "HTMLResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "text", "description": "Returns the text content on the given page or by selector when specified", "args": [{"name": "selector", "description": "The DOM selector of the given element you want to return the text of", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "visible", "description": "Whether or not to return the text content of the element only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "timeout", "description": "The maximum amount of time, in milliseconds, to wait for the selector to appear, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TextResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "waitForSelector", "description": "Waits for a given selector to be present in the DOM, with optional visibility", "args": [{"name": "selector", "description": "The selector to wait for until present in the DOM", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "visible", "description": "Whether or not to consider the element as present only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "timeout", "description": "When waiting for a selector applies a timeout to wait for in milliseconds, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "WaitForSelector", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "waitForTimeout", "description": "Wait for a period of time, defined in milliseconds", "args": [{"name": "time", "description": "The amount of time to wait for, in milliseconds", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "WaitForTimeout", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "waitForRequest", "description": "Waits for the browser to make a particular request", "args": [{"name": "url", "description": "The pattern of the request URL to wait for, using glob-style pattern-matching", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "method", "description": "The method of the request to wait for", "type": {"kind": "ENUM", "name": "Method", "ofType": null}, "defaultValue": null}, {"name": "timeout", "description": "How long to wait for the request to be made before timing out, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "WaitForRequest", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "waitForResponse", "description": "Waits for a particular network response to be made back to the browser", "args": [{"name": "url", "description": "The pattern of the response URL to wait for, using glob-style pattern-matching", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "codes", "description": "The HTTP Response code(s) of the URL to wait for. Can be a single HTTP code or a list of desired codes", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "WaitForResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "waitForNavigation", "description": "Waits for a navigation even to fire, useful for clicking an element and waiting for a page load of some", "args": [{"name": "waitUntil", "description": "When to consider the page fully-loaded and proceed with further execution", "type": {"kind": "ENUM", "name": "WaitUntilGoto", "ofType": null}, "defaultValue": "load"}, {"name": "timeout", "description": "The maximum amount of time, in milliseconds, to wait for the page to load, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "HTTPResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "hover", "description": "Waits for the element to be visible, scrolls to it, then hover on it with native events", "args": [{"name": "selector", "description": "The CSS selector of the element on the page you want to hover on", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "x", "description": "The X coordinate, in pixels, to hover on the page", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "y", "description": "The Y coordinate, in pixels, to hover on the page", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "wait", "description": "Whether or not to wait for the element to present in the DOM", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "scroll", "description": "Whether or not to scroll to the element, defaults to true", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "visible", "description": "Whether or not to hover on the element only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "timeout", "description": "How long to wait for the element to appear before timing out, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "HoverResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "click", "description": "Waits for the element to be visible, scrolls to it, then clicks on it with native events", "args": [{"name": "selector", "description": "A query-selector compatible string, JavaScript that returns an HTML Node, OR a Browserless-deep query. Examples include:\nA simple <button /> Element\nselector: \"button\"\n\nA JavaScript snippet that returns a button element\nselector: \"document.querySelector('button')\"\n\nA Browserless Deep query. These queries must start with a \"<\" character.\nDeep queries will traverse all iframes, shadow-doms (open or closed), and more.\nselector: \"< button\"\n\nHere's a deep query that filters by iframes with a url of \"example.com\" and a button with a class of active\nselector: \"< https://example.com/* button.active\"", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "wait", "description": "Whether or not to wait for the element to present in the DOM", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "scroll", "description": "Whether or not to scroll to the element prior to clicking, defaults to true", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "visible", "description": "Whether or not to click the element only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "timeout", "description": "How long to wait for the element to appear before timing out on the click handler, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "ClickResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "evaluate", "description": "Evaluates JavaScript client-side, via raw content or a URL to some JavaScript code, in the browser's page environment", "args": [{"name": "content", "description": "The raw script you'd like to evaluate. This code gets wrapped in an async function so you can use `return` at the end as well as `await` and other async concepts. You can return any stringified value from this function", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "url", "description": "The URL of the script you'd like to evaluate. This code gets wrapped in an async function so you can use `return` at the end as well as `await` and other async concepts. You can return any stringified value from this function", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "timeout", "description": "A timeout to wait for the script to finish evaluating, overriding any defaults. Useful for async scripts that may be longer running", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "EvaluateResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "Types text into an element by scrolling to it, clicking it, then emitting key events for every character", "args": [{"name": "text", "description": "The text content you want to type into the element", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "selector", "description": "The CSS selector of the element on the page you want to type text into", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "delay", "description": "The amount of delay between keystrokes in milliseconds. Values are used as a range and chosen at random", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": "[50, 200]"}, {"name": "wait", "description": "Whether or not to wait for the element to present in the DOM", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "scroll", "description": "Whether or not to scroll to the element prior to typing, defaults to true", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "visible", "description": "Whether or not to type into the element only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "interactable", "description": "Whether or not to check if element can be interacted with by hovering over it and seeing if the element\nis available at that x and y position.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "timeout", "description": "How long to wait for the element to appear before timing out, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TypeResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": "Returns the title of the page that the browser is currently at", "args": [], "type": {"kind": "OBJECT", "name": "TitleResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": "Returns the URL of the page that the browser is currently at", "args": [], "type": {"kind": "OBJECT", "name": "URLResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "querySelector", "description": "Passes through certain properties of the browsers' own querySelector API", "args": [{"name": "selector", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "timeout", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "visible", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "OBJECT", "name": "QuerySelectorResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "querySelectorAll", "description": "Passes through certain properties of the browsers' own querySelectorAll API", "args": [{"name": "selector", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "timeout", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "visible", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "QuerySelectorResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "scroll", "description": "Waits for a selector, then scrolls to it on the page or an x,y coordinate in pixels", "args": [{"name": "selector", "description": "The DOM selector of the element on the page you want to scroll to", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "x", "description": "The X coordinate, in pixels, to scroll to", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "y", "description": "The Y coordinate, in pixels, to scroll to", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "wait", "description": "Whether or not to wait for the element, then scroll to it", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "visible", "description": "Whether or not to scroll to the element only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "timeout", "description": "How long to wait for the element to appear before timing out, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "ScrollResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "reconnect", "description": "Returns a payload with reconnection information in order to reconnect back to the same browser session", "args": [{"name": "timeout", "description": "The amount of time, in milliseconds, to leave the browser open without a connection before it is manually terminated. Defaults to 30 seconds", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": "30000"}], "type": {"kind": "OBJECT", "name": "ReconnectionResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "cookies", "description": "Sets and gets cookies on the page", "args": [{"name": "cookies", "description": "The cookies to set on the page", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CookieInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "CookieResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "javaScriptEnabled", "description": "Sets and gets JavaScript execution on the page", "args": [{"name": "enabled", "description": "Whether or not to enable JavaScript on the page", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "JavaScriptResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "screenshot", "description": "Screenshots the page or a specific selector", "args": [{"name": "selector", "description": "The CSS selector of the element on the page you want to screenshot", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "captureBeyondViewport", "description": "Capture the screenshot beyond the viewport.\nDefault: False if there is no clip. True otherwise.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "clip", "description": "Specifies the region of the page/element to clip.", "type": {"kind": "INPUT_OBJECT", "name": "ScreenshotClip", "ofType": null}, "defaultValue": null}, {"name": "fromSurface", "description": "Capture the screenshot from the surface, rather than the view.\nDefault: True.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "fullPage", "description": "When True, takes a screenshot of the full page.\nDefault: False.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "omitBackground", "description": "Hides default white background and allows capturing screenshots with transparency.\nDefault: False.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "optimizeForSpeed", "description": "Optimize image encoding for speed, not for resulting size.\nDefault: False.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "quality", "description": "Quality of the image, between 0-100. Not applicable to png images.", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "type", "description": "The final format of the screenshot.", "type": {"kind": "ENUM", "name": "ScreenshotType", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "ScreenshotResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "pdf", "description": null, "args": [{"name": "format", "description": "The page format to use for the PDF", "type": {"kind": "ENUM", "name": "PDFPageFormat", "ofType": null}, "defaultValue": null}, {"name": "landscape", "description": "Paper orientation. Defaults to false.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "displayHeader<PERSON>ooter", "description": "Display header and footer. Defaults to false.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "printBackground", "description": "Print background graphics. Defaults to false.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "scale", "description": "Scale of the webpage rendering. Defaults to 1.", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}, {"name": "width", "description": "Width in inches or CSS unit. Defaults to 8.5 inches.", "type": {"kind": "SCALAR", "name": "FloatOrString", "ofType": null}, "defaultValue": null}, {"name": "height", "description": "Height in inches or CSS unit. Defaults to 11 inches.", "type": {"kind": "SCALAR", "name": "FloatOrString", "ofType": null}, "defaultValue": null}, {"name": "marginTop", "description": "Top margin in inches or CSS unit. Defaults to 1cm (~0.4 inches).", "type": {"kind": "SCALAR", "name": "FloatOrString", "ofType": null}, "defaultValue": null}, {"name": "marginBottom", "description": "Bottom margin in inches or CSS unit. Defaults to 1cm (~0.4 inches).", "type": {"kind": "SCALAR", "name": "FloatOrString", "ofType": null}, "defaultValue": null}, {"name": "marginLeft", "description": "Left margin in inches or CSS unit. Defaults to 1cm (~0.4 inches).", "type": {"kind": "SCALAR", "name": "FloatOrString", "ofType": null}, "defaultValue": null}, {"name": "marginRight", "description": "Right margin in inches or CSS unit. Defaults to 1cm (~0.4 inches).", "type": {"kind": "SCALAR", "name": "FloatOrString", "ofType": null}, "defaultValue": null}, {"name": "pageRanges", "description": "Paper ranges to print, one based, e.g., '1-5, 8, 11-13'. Pages are\nprinted in the document order, not in the order specified, and no\nmore than once.\nDefaults to empty string, which implies the entire document is printed.\nThe page numbers are quietly capped to actual page count of the\ndocument, and ranges beyond the end of the document are ignored.\nIf this results in no pages to print, an error is reported.\nIt is an error to specify a range with start greater than end.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "headerTemplate", "description": "HTML template for the print header. Should be valid HTML markup with following\nclasses used to inject printing values into them:\n- `date`: formatted print date\n- `title`: document title\n- `url`: document location\n- `pageNumber`: current page number\n- `totalPages`: total pages in the document\n\nFor example, `<span class=title></span>` would generate span containing the title.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "footerTemplate", "description": "HTML template for the print footer. Should use the same format as the `headerTemplate`.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "preferCSSPageSize", "description": "Whether or not to prefer page size as defined by css. Defaults to false,\nin which case the content will be scaled to fit the paper size.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "transferMode", "description": "Return as stream (PrintToPDFRequestTransferMode enum)", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "generateTaggedPDF", "description": "Whether or not to generate tagged (accessible) PDF. Defaults to embedder choice.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}, {"name": "generateDocumentOutline", "description": "Whether or not to embed the document outline into the PDF.", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "PDFResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "select", "description": "Selects a value from a dropdown or multiple select element", "args": [{"name": "selector", "description": "The CSS selector of the element on the page you want to select a value from", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "value", "description": "The value or values to select from the dropdown or multiple select element", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "StringOrArray", "ofType": null}}, "defaultValue": null}, {"name": "wait", "description": "Whether or not to wait for the select to present in the DOM", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "scroll", "description": "Whether or not to scroll to the select element prior to selecting, defaults to true", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "visible", "description": "Whether or not to select the element only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "timeout", "description": "How long to wait for the element to appear before timing out on the handler, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "SelectResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "checkbox", "description": null, "args": [{"name": "selector", "description": "The CSS selector of the element on the page you want to check/uncheck", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "checked", "description": "Whether or not the input should be checked", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}, {"name": "wait", "description": "Whether or not to wait for the element to present in the DOM", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "scroll", "description": "Whether or not to scroll to the element prior to clicking, defaults to true", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "visible", "description": "Whether or not to check/uncheck the element only if it's visible", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}, {"name": "timeout", "description": "How long to wait for the element to appear before timing out on the handler, overriding any defaults", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "ClickResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "solve", "description": "🚨 **EXPERIMENTAL** 🚨\nSolves a captcha or other challenge, specified by the \"type\" of captcha to solve", "args": [{"name": "type", "description": "An enum of the type of captcha to look for and solve", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "CaptchaTypes", "ofType": null}}, "defaultValue": null}, {"name": "wait", "description": "Whether or not to wait for the <PERSON><PERSON> to be present on the page", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "timeout", "description": "A time, in milliseconds, to wait for a captcha to appear. Only valid when wait = true.\nIf a captcha is found then this timer doesn't have an effect on timing-out the solve.", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "CaptchaResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "verify", "description": "🚨 **EXPERIMENTAL** 🚨\nClicks a verification button to assert human-like", "args": [{"name": "type", "description": "An enum of the type of captcha to look for and solve", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VerifyTypes", "ofType": null}}, "defaultValue": null}, {"name": "wait", "description": "Whether or not to wait for the verification to be present on the page", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "true"}, {"name": "timeout", "description": "A time, in milliseconds, to wait for a verification to appear. Only valid when wait = true", "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "CaptchaResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Query", "description": null, "fields": [{"name": "version", "description": "The Version of this server", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "browser", "description": "The Version of the browser", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.", "fields": [{"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": "The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.", "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "specifiedByURL", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isOneOf", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given `__Type` is.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": "Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": "Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "A GraphQL-formatted string representing the default value for this input value.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": "One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": "A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRepeatable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Location adjacent to a query operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Location adjacent to a mutation operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUBSCRIPTION", "description": "Location adjacent to a subscription operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Location adjacent to a field.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Location adjacent to a fragment definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Location adjacent to a fragment spread.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Location adjacent to an inline fragment.", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIABLE_DEFINITION", "description": "Location adjacent to a variable definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Location adjacent to a schema definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Location adjacent to a scalar definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Location adjacent to an object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Location adjacent to a field definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Location adjacent to an argument definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Location adjacent to an interface definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Location adjacent to a union definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Location adjacent to an enum definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Location adjacent to an enum value definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Location adjacent to an input object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Location adjacent to an input object field definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}], "directives": [{"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}]}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}]}, {"name": "deprecated", "description": "Marks an element of a GraphQL schema as no longer supported.", "locations": ["FIELD_DEFINITION", "ARGUMENT_DEFINITION", "INPUT_FIELD_DEFINITION", "ENUM_VALUE"], "args": [{"name": "reason", "description": "Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\""}]}, {"name": "specifiedBy", "description": "Exposes a URL that specifies the behavior of this scalar.", "locations": ["SCALAR"], "args": [{"name": "url", "description": "The URL that specifies the behavior of this scalar.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}]}, {"name": "oneOf", "description": "Indicates exactly one field must be supplied and this field must not be `null`.", "locations": ["INPUT_OBJECT"], "args": []}]}}
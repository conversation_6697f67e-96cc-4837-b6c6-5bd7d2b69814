name: Publish Branch Tags

on:
  workflow_dispatch:
    inputs:
      imageType:
        description: The type of image to publish
        required: true
        default: cloud-unit
        type: choice
        options:
          - cloud-unit
          - enterprise

jobs:
  publish_cloud_unit:
    if: inputs.imageType == 'cloud-unit'
    name: Push multi-platform Cloud-Unit
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }}
          password: ${{ secrets.GHCR_PASSWORD }}

      - name: Get Tag
        run: echo "IMAGE_TAG=${GITHUB_REF_NAME////-}" >> $GITHUB_ENV

      - name: Publish the Cloud-Unit Image
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          file: ./docker/cloud.dockerfile
          tags: |
            ghcr.io/browserless/cloud-unit:${{ env.IMAGE_TAG }}
          context: .
          push: true
          platforms: |
            linux/amd64
            linux/arm64

  publish_enterprise:
    if: inputs.imageType == 'enterprise'
    name: Push multi-platform Enterprise
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }}
          password: ${{ secrets.GHCR_PASSWORD }}

      - name: Get Tag
        run: echo "IMAGE_TAG=${GITHUB_REF_NAME////-}" >> $GITHUB_ENV

      - name: Publish the Enterprise Image
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          file: ./docker/enterprise.dockerfile
          tags: |
            ghcr.io/browserless/enterprise:${{ env.IMAGE_TAG }}
          context: .
          push: true
          platforms: |
            linux/amd64
            linux/arm64

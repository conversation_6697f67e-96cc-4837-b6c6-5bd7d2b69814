// For some reason, the types are not being picked up from the node_modules
declare module 'puppeteer-extra' {
  import {
    <PERSON><PERSON>er,
    BrowserLaunchArgumentOptions,
    LaunchOptions,
    PuppeteerNode,
  } from 'puppeteer-core';

  interface PuppeteerExtra extends PuppeteerNode {
    launch(
      options?: LaunchOptions & BrowserLaunchArgumentOptions,
    ): Promise<Browser>;
    use(plugin: any): PuppeteerExtra;
  }

  const puppeteer: PuppeteerExtra;
  export default puppeteer;
}

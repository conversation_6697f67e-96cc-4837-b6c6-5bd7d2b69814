import { noop, sleep } from '@browserless.io/browserless';
import { expect } from 'chai';
import puppeteer from 'puppeteer-core';
import playwright from 'playwright-core';
import Sinon from 'sinon';

import BrowserlessCloud from '../browserless.js';
import { CloudAPI } from '../api.js';
import { CloudUnitConfig } from '../config.js';
import { CloudCredits } from '../credit.js';
import { CloudHooks } from '../hooks.js';
import { randomID } from '../../utils.js';
import { cloudUnitPlanNumbers } from '../../types.js';
import { ServerEvents } from '../server-events.js';
import { SessionEndedEventsPublisher } from '../../shared/utils/session-ended-publisher.js';
import fileSystem from '../../file-system.js';

const token = randomID(16);
const webmSignature: Buffer = Buffer.from([0x1a, 0x45, 0xdf, 0xa3]);

const apiMock = Sinon.createStubInstance(CloudAPI, {
  getCloudCredits: Sinon.stub().resolves({
    [token]: ['Infinity', 1, 10],
  }) as any,
  saveCloudMetrics: Sinon.stub().resolves({}) as any,
});

const serverEventsMock = Sinon.createStubInstance(ServerEvents, {
  addEventListener: Sinon.stub().resolves({}) as any,
});

const sessionEventsPublisherMock = Sinon.createStubInstance(
  SessionEndedEventsPublisher,
  {
    publishEvents: Sinon.stub().resolves({}) as any,
    publishFailedEntries: Sinon.stub().resolves({}) as any,
  },
);

describe('Cloud-Unit WebSocket APIs', function () {
  let browserless: BrowserlessCloud;

  const start = async ({
    config: configOverride,
    credits: creditsOverride,
    api: apiOverride,
    sessionEndedPublisher: sessionEndedPublisherOverride,
    hooks: hooksOverride,
  }: {
    config?: CloudUnitConfig;
    credits?: CloudCredits;
    api?: CloudAPI;
    sessionEndedPublisher?: SessionEndedEventsPublisher;
    hooks?: CloudHooks;
  } = {}) => {
    const api = apiOverride ?? apiMock;
    const credits = creditsOverride ?? new CloudCredits(api, serverEventsMock);
    const config = configOverride ?? new CloudUnitConfig(credits);
    const sessionEndedPublisher =
      sessionEndedPublisherOverride ?? sessionEventsPublisherMock;
    const hooks =
      hooksOverride ??
      new CloudHooks(config, credits, api, '', sessionEndedPublisher);

    browserless = new BrowserlessCloud({
      api,
      credits,
      hooks,
      config,
      sessionEndedPublisher,
    });

    await browserless.start();
    return browserless;
  };

  afterEach(async () => {
    await browserless.stop().catch(noop);
  });

  it('should allow connections to the /chromium/stealth route', async () => {
    await start();
    const browser = await puppeteer.connect({
      browserWSEndpoint: `ws://localhost:3000/chromium/stealth?token=${token}`,
    });

    expect(browser);
    await browser.close();
  });

  it('should allow connections to the /chrome/stealth route', async () => {
    await start();
    const browser = await puppeteer.connect({
      browserWSEndpoint: `ws://localhost:3000/chrome/stealth?token=${token}`,
    });

    expect(browser);
    await browser.close();
  });

  it('should block all tokenless requests expect for /live/*', async () => {
    await start();
    const res = await fetch('http://localhost:3000/live/index.html');
    expect(res.status).to.not.equal(401);

    const res2 = await fetch('http://localhost:3000/screenshot');
    expect(res2.status).to.equal(401);
  });

  it('should NOT allow connections when setting a large timeout', async () => {
    await start();
    const timeout = 60 * 60 * 1000;

    await playwright.chromium
      .connectOverCDP(
        `ws://localhost:3000/chromium?token=${token}&timeout=${timeout}`,
      )
      .then(() => {
        throw new Error(`I should have thrown a 400!`);
      })
      .catch((e: Error) => {
        expect(e.message).to.contain('400');
        expect(e.message).to.contain(
          'Timeout must be a integer between 1 and 900,000',
        );
      });
  });

  it('should NOT allow connections when setting a negative timeout', async () => {
    await start();
    const timeout = -60 * 60 * 1000;

    await playwright.chromium
      .connectOverCDP(
        `ws://localhost:3000/chromium?token=${token}&timeout=${timeout}`,
      )
      .then(() => {
        throw new Error(`I should have thrown a 400!`);
      })
      .catch((e: Error) => {
        expect(e.message).to.contain('400');
        expect(e.message).to.contain(
          'Timeout must be a integer between 1 and 900,000',
        );
      });
  });

  it('should NOT allow connections when setting a non-numeric timeout', async () => {
    await start();
    const timeout = 'IMALITTLETEAPOT';

    await playwright.chromium
      .connectOverCDP(
        `ws://localhost:3000/chromium?token=${token}&timeout=${timeout}`,
      )
      .then(() => {
        throw new Error(`I should have thrown a 400!`);
      })
      .catch((e: Error) => {
        expect(e.message).to.contain('400');
        expect(e.message).to.contain(
          'Timeout must be a integer between 1 and 900,000',
        );
      });
  });

  describe('Browserless CDP API', () => {
    it('should NOT allow liveURLs in plans under 180k', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });
      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?token=${token}`,
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      const res = await (cdp.send as any)('Browserless.liveURL');
      expect(res.error).to.contain(`Live URLs are not supported`);
      await browser.close();
    });

    it('should allow liveURLs in 180k plans and over', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.oneHundredEightyThousand],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });
      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?token=${token}`,
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      const res = await (cdp.send as any)('Browserless.liveURL');
      expect(res.error).to.be.null;
      expect(res.liveURL).to.not.be.null;
      expect(res.liveURL).to.be.a('string');
      expect(res.liveURL).to.match(/^[a-zA-Z]+:\/\/[^\/]+\/live\/.*/);
      const liveRes = await fetch(res.liveURL);
      expect(liveRes.status).to.equal(200);

      await browser.close();
    });

    it('should allow captcha solving in all plans', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });
      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium/stealth?token=${token}`,
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      (cdp.send as any)('Browserless.solveCaptcha');
      await sleep(1000); // Allow error to happen
      await browser.close();
    });

    it('should emit "Browserless.captchaFound" events when found', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.fiveHundredThousand],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });
      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?token=${token}`,
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();
      const captchaFound = new Promise((r) =>
        cdp.on('Browserless.captchaFound', r),
      );
      await page.goto('https://www.google.com/recaptcha/api2/demo');
      await captchaFound;
      await browser.close();
    });

    it('should not allow recording over CDP in plans under 180k', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });
      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?record=true&timeout=60000&token=${token}`,
      });

      const page = await browser.newPage();
      await page.goto('http://example.com', { waitUntil: 'networkidle0' });
      const cdp = await page.createCDPSession();

      const response = await cdp.send('Browserless.startRecording' as any);

      expect(response.error).to.contain('Recording is not supported');
      await browser.close();
    });

    it('should allow recording over CDP', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.oneHundredEightyThousand],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });
      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?record=true&timeout=60000&token=${token}`,
      });

      const page = await browser.newPage();
      await page.goto('http://example.com', { waitUntil: 'networkidle0' });
      const cdp = await page.createCDPSession();

      await cdp.send('Browserless.startRecording' as any);
      await sleep(3000);
      const response = await cdp.send('Browserless.stopRecording' as any);

      expect(response.error).to.equal(null);
      const file = Buffer.from(response.value, 'binary');
      expect(file.subarray(0, 4).equals(webmSignature)).to.be.true;
      await browser.close();
    });

    it('should allow reconnection for free plans with timeout limit', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });
      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?record=true&timeout=10000&token=${token}`,
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();

      // @ts-ignore
      // prettier-ignore
      const { error, browserWSEndpoint } = await cdp.send('Browserless.reconnect', {
        timeout: 10000,
      });
      expect(error).to.be.null;
      expect(browserWSEndpoint).to.be.a('string');
      expect(browserWSEndpoint).to.match(/^ws:\/\/.*\/reconnect\/.*$/);

      await browser.close();
    });

    it('should allow reconnection', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.oneHundredEightyThousand],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });
      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?record=true&timeout=60000&token=${token}`,
      });

      const page = await browser.newPage();
      const cdp = await page.createCDPSession();

      // @ts-ignore
      // prettier-ignore
      const { error, browserWSEndpoint } = await cdp.send('Browserless.reconnect', {
        timeout: 60000,
      });
      expect(error).to.be.null;
      expect(browserWSEndpoint).to.be.a('string');
      expect(browserWSEndpoint).to.match(/^ws:\/\/.*\/reconnect\/.*$/); // Matches "ws://*/reconnect/*"

      await browser.close();
    });

    it('should not allow third party proxy in free plan', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });

      let errorThrown: Error | null = null;
      try {
        await puppeteer.connect({
          browserWSEndpoint: `ws://localhost:3000/chromium?timeout=60000&token=${token}&--proxy-server=serverUrl`,
        });
      } catch (error) {
        errorThrown = error as Error;
      }

      expect(errorThrown).to.not.be.null;
      expect(errorThrown?.message).to.contain('401');
    });

    it('should allow third party proxy in paid plans', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.oneHundredEightyThousand],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });

      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?timeout=60000&token=${token}&--proxy-server=serverUrl`,
      });

      expect(browser);
      await browser.close();
    });

    it('should not allow timeout greater than 60,000ms for free accounts', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });

      let errorThrown: Error | null = null;
      try {
        await puppeteer.connect({
          browserWSEndpoint: `ws://localhost:3000/chromium?timeout=61000&token=${token}`,
        });
      } catch (error) {
        errorThrown = error as Error;
      }

      expect(errorThrown).to.not.be.null;
      expect(errorThrown?.message).to.contain('400');
    });

    it('should allow timeout greater than 60,000ms for paid accounts', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.oneHundredEightyThousand],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });

      const browser = await puppeteer.connect({
        browserWSEndpoint: `ws://localhost:3000/chromium?timeout=61000&token=${token}`,
      });

      expect(browser);
      await browser.close();
    });
  });

  describe('Session Workflow', () => {
    afterEach(() => fileSystem.dangerouslyDeleteAllSessions());

    it('should allow connections', async () => {
      const api = Sinon.createStubInstance(CloudAPI, {
        getCloudCredits: Sinon.stub().resolves({
          [token]: ['Infinity', cloudUnitPlanNumbers.free],
        }) as any,
        saveCloudMetrics: Sinon.stub().resolves({}) as any,
      });
      await start({ api });

      const response = await fetch(
        `http://localhost:3000/session?token=${token}`,
        {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            ttl: 1 * 24 * 60 * 60 * 1000,
          }),
        },
      ).then((r) => r.json());

      const browser = await puppeteer.connect({
        browserWSEndpoint: response.connect,
      });

      expect(browser);

      await browser.close();
      await sleep(100);

      const second = await puppeteer.connect({
        browserWSEndpoint: response.connect,
      });
      expect(second);
      await second.close();
    });
  });
});

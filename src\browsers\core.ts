import { Duplex } from 'stream';
import {
  <PERSON><PERSON>erLauncherOptions,
  ChromiumCDP,
  Logger,
  ServerError,
  chromeExecutablePath,
  createLogger,
  getTokenFromRequest,
} from '@browserless.io/browserless';
import { <PERSON>rowser } from 'puppeteer-core';
import { CDPProxy } from '../shared/cdp/proxy.js';
import { SharedConfig } from '../config.js';
import { AugmentedRequest } from '../types.js';
import { parseLaunchOptions } from '../utils.js';

export class EnterpriseChromiumCDP extends ChromiumCDP {
  protected config: SharedConfig;
  protected cdpProxy?: CDPProxy;
  protected apiKey: string | null = null;
  protected debug = createLogger('browsers:enterprise-chromium:cdp');

  constructor({
    blockAds,
    config,
    userDataDir,
    logger,
  }: {
    blockAds: boolean;
    config: SharedConfig;
    userDataDir: ChromiumCDP['userDataDir'];
    logger: Logger;
  }) {
    super({
      blockAds,
      config,
      userDataDir,
      logger,
    });

    this.config = config;
  }

  public getCDPProxy() {
    return this.cdpProxy;
  }

  public keepUntil() {
    if (!this.cdpProxy) {
      return 0;
    }

    return this.cdpProxy.keepUntilTime();
  }

  public async launch(params: BrowserLauncherOptions): Promise<Browser> {
    return super.launch(await parseLaunchOptions(params));
  }

  public setupCDPProxy(req: AugmentedRequest) {
    this.apiKey = getTokenFromRequest(req);
    const proxy = new CDPProxy({
      allowCaptchaSolving: this.config.allowCaptchaSolving(
        this.apiKey as string,
      ),
      allowLiveURLs: this.config.allowLiveURLs(this.apiKey as string),
      allowBrowserClose: this.config.allowReconnect(this.apiKey as string),
      allowReconnect: this.config.allowReconnect(this.apiKey as string),
      allowRecording: this.config.allowRecord(this.apiKey as string),
      config: this.config,
      maxPages: 10,
      retries: 10,
      record:
        this.config.allowRecord(getTokenFromRequest(req) as string) &&
        (req.queryParams.record as boolean),
      req,
      logger: this.logger,
    });

    this.cdpProxy = proxy;
  }

  // @ts-ignore @todo Request extensions
  public async proxyWebSocket(
    req: AugmentedRequest,
    socket: Duplex,
    head: Buffer,
  ) {
    if (!this.browserWSEndpoint) {
      throw new ServerError(
        `No browserWSEndpoint found, did you launch first?`,
      );
    }
    this.setupCDPProxy(req);

    this.debug(
      `Proxying ${req.parsed.href} to browser ${this.browserWSEndpoint}`,
    );

    req.url = '';

    return this.cdpProxy!.ws(req, socket, head, {
      changeOrigin: true,
      target: this.browserWSEndpoint,
    });
  }

  public getApiKey() {
    return this.apiKey;
  }

  public getConfig() {
    return this.config;
  }
}

export class EnterpriseChromeCDP extends EnterpriseChromiumCDP {
  protected executablePath = chromeExecutablePath();
  protected debug = createLogger('browsers:enterprise-chrome:cdp');
}

name: Version Enterprise
on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version you want to set ex. major.minor.patch'
        type: choice
        options:
          - minor
          - patch
          - major
        required: true
jobs:
  publish-new-tag:
    runs-on: ubuntu-latest
    steps:
      - name: Fail if branch is not main
        if: github.ref != 'refs/heads/main'
        run: |
          echo "This workflow should be triggered on main branch"
          exit 1

      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.BROWSERLESS_ACTIONS_APP_ID }}
          private-key: ${{ secrets.BROWSERLESS_ACTIONS_APP_PRIVATE_KEY }}

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ steps.app-token.outputs.token }}

      - name: Set User
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "41898282+github-actions[bot]@users.noreply.github.com"

      - name: Push version and tag
        run: |
          npm version ${{ github.event.inputs.version }}
          git push
          git push --tags

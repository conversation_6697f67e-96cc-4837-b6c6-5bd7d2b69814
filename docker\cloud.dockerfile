ARG FROM=ghcr.io/browserless/multi:v2.33.0

FROM ${FROM}

LABEL org.opencontainers.image.source https://github.com/browserless/enterprise

# Needed for determining if Chrome is eligible for install
ARG TARGETPLATFORM
ENV TARGETPLATFORM=${TARGETPLATFORM}

# Folders that are useful to lookup later
ENV BLESS_INSTALL_NODE_MODULES=${APP_DIR}/node_modules/@browserless.io/browserless
ENV BLESS_ROUTES=${BLESS_INSTALL_NODE_MODULES}/build/routes
ENV DEBUG="browserless*,-**:verbose"
ENV STATE_FILE=${APP_DIR}/bless-state.json

# Change to root for install
USER root

# Cleanup
RUN rm -rf $APP_DIR
RUN mkdir -p $APP_DIR

WORKDIR $APP_DIR

# Copy src
COPY extensions extensions
COPY scripts scripts
COPY src src
COPY static static
COPY types types
COPY mutations.graphql .
COPY package*.json .
COPY tsconfig.json .
COPY *README.md .
COPY *CHANGELOG.md .
COPY __schema_snapshot__.json .

# Remove enterprise-specific code
RUN rm -r src/enterprise

# Install all dependencies, then prune
RUN npm install --production=false --os=linux --cpu=x64 sharp &&\
  npm run build &&\
  npm prune production &&\
  rm -rf src &&\
  chown -R blessuser:blessuser $APP_DIR

# Remove all source code
RUN rm -rf src/*

# Back to non-privileged user
USER blessuser

CMD ["./scripts/start-cloud.sh"]

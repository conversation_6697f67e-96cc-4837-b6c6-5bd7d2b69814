export default function () {
  /*!
   *
   *   simple-keyboard v3.8.48
   *   https://github.com/hodgef/simple-keyboard
   *
   *   Copyright (c) <PERSON> (https://github.com/hodgef) and project contributors.
   *
   *   This source code is licensed under the MIT license found in the
   *   LICENSE file in the root directory of this source tree.
   *
   */
  !(function (t, e) {
    'object' == typeof exports && 'object' == typeof module
      ? (module.exports = e())
      : 'function' == typeof define && define.amd
        ? define([], e)
        : 'object' == typeof exports
          ? (exports.SimpleKeyboard = e())
          : (t.SimpleKeyboard = e());
  })(this, function () {
    return (function () {
      'use strict';
      var t = {
          34: function (t, e, n) {
            var o = n(4901);
            t.exports = function (t) {
              return 'object' == typeof t ? null !== t : o(t);
            };
          },
          81: function (t, e, n) {
            var o = n(9565),
              r = n(9306),
              i = n(8551),
              a = n(6823),
              s = n(851),
              u = TypeError;
            t.exports = function (t, e) {
              var n = arguments.length < 2 ? s(t) : e;
              if (r(n)) return i(o(n, t));
              throw new u(a(t) + ' is not iterable');
            };
          },
          235: function (t, e, n) {
            var o = n(9213).forEach,
              r = n(4598)('forEach');
            t.exports = r
              ? [].forEach
              : function (t) {
                  return o(
                    this,
                    t,
                    arguments.length > 1 ? arguments[1] : void 0,
                  );
                };
          },
          283: function (t, e, n) {
            var o = n(9504),
              r = n(9039),
              i = n(4901),
              a = n(9297),
              s = n(3724),
              u = n(350).CONFIGURABLE,
              c = n(3706),
              l = n(1181),
              f = l.enforce,
              d = l.get,
              p = String,
              h = Object.defineProperty,
              v = o(''.slice),
              y = o(''.replace),
              g = o([].join),
              m =
                s &&
                !r(function () {
                  return 8 !== h(function () {}, 'length', { value: 8 }).length;
                }),
              b = String(String).split('String'),
              x = (t.exports = function (t, e, n) {
                ('Symbol(' === v(p(e), 0, 7) &&
                  (e = '[' + y(p(e), /^Symbol\(([^)]*)\).*$/, '$1') + ']'),
                  n && n.getter && (e = 'get ' + e),
                  n && n.setter && (e = 'set ' + e),
                  (!a(t, 'name') || (u && t.name !== e)) &&
                    (s
                      ? h(t, 'name', { value: e, configurable: !0 })
                      : (t.name = e)),
                  m &&
                    n &&
                    a(n, 'arity') &&
                    t.length !== n.arity &&
                    h(t, 'length', { value: n.arity }));
                try {
                  n && a(n, 'constructor') && n.constructor
                    ? s && h(t, 'prototype', { writable: !1 })
                    : t.prototype && (t.prototype = void 0);
                } catch (t) {}
                var o = f(t);
                return (
                  a(o, 'source') ||
                    (o.source = g(b, 'string' == typeof e ? e : '')),
                  t
                );
              });
            Function.prototype.toString = x(function () {
              return (i(this) && d(this).source) || c(this);
            }, 'toString');
          },
          298: function (t, e, n) {
            var o = n(2195),
              r = n(5397),
              i = n(8480).f,
              a = n(7680),
              s =
                'object' == typeof window &&
                window &&
                Object.getOwnPropertyNames
                  ? Object.getOwnPropertyNames(window)
                  : [];
            t.exports.f = function (t) {
              return s && 'Window' === o(t)
                ? (function (t) {
                    try {
                      return i(t);
                    } catch (t) {
                      return a(s);
                    }
                  })(t)
                : i(r(t));
            };
          },
          350: function (t, e, n) {
            var o = n(3724),
              r = n(9297),
              i = Function.prototype,
              a = o && Object.getOwnPropertyDescriptor,
              s = r(i, 'name'),
              u = s && 'something' === function () {}.name,
              c = s && (!o || (o && a(i, 'name').configurable));
            t.exports = { EXISTS: s, PROPER: u, CONFIGURABLE: c };
          },
          397: function (t, e, n) {
            var o = n(7751);
            t.exports = o('document', 'documentElement');
          },
          421: function (t) {
            t.exports = {};
          },
          511: function (t, e, n) {
            var o = n(9167),
              r = n(9297),
              i = n(1951),
              a = n(4913).f;
            t.exports = function (t) {
              var e = o.Symbol || (o.Symbol = {});
              r(e, t) || a(e, t, { value: i.f(t) });
            };
          },
          566: function (t, e, n) {
            var o = n(9504),
              r = n(9306),
              i = n(34),
              a = n(9297),
              s = n(7680),
              u = n(616),
              c = Function,
              l = o([].concat),
              f = o([].join),
              d = {};
            t.exports = u
              ? c.bind
              : function (t) {
                  var e = r(this),
                    n = e.prototype,
                    o = s(arguments, 1),
                    u = function () {
                      var n = l(o, s(arguments));
                      return this instanceof u
                        ? (function (t, e, n) {
                            if (!a(d, e)) {
                              for (var o = [], r = 0; r < e; r++)
                                o[r] = 'a[' + r + ']';
                              d[e] = c(
                                'C,a',
                                'return new C(' + f(o, ',') + ')',
                              );
                            }
                            return d[e](t, n);
                          })(e, n.length, n)
                        : e.apply(t, n);
                    };
                  return (i(n) && (u.prototype = n), u);
                };
          },
          597: function (t, e, n) {
            var o = n(9039),
              r = n(8227),
              i = n(9519),
              a = r('species');
            t.exports = function (t) {
              return (
                i >= 51 ||
                !o(function () {
                  var e = [];
                  return (
                    ((e.constructor = {})[a] = function () {
                      return { foo: 1 };
                    }),
                    1 !== e[t](Boolean).foo
                  );
                })
              );
            };
          },
          616: function (t, e, n) {
            var o = n(9039);
            t.exports = !o(function () {
              var t = function () {}.bind();
              return 'function' != typeof t || t.hasOwnProperty('prototype');
            });
          },
          655: function (t, e, n) {
            var o = n(6955),
              r = String;
            t.exports = function (t) {
              if ('Symbol' === o(t))
                throw new TypeError(
                  'Cannot convert a Symbol value to a string',
                );
              return r(t);
            };
          },
          687: function (t, e, n) {
            var o = n(4913).f,
              r = n(9297),
              i = n(8227)('toStringTag');
            t.exports = function (t, e, n) {
              (t && !n && (t = t.prototype),
                t && !r(t, i) && o(t, i, { configurable: !0, value: e }));
            };
          },
          706: function (t, e, n) {
            var o = n(350).PROPER,
              r = n(9039),
              i = n(7452);
            t.exports = function (t) {
              return r(function () {
                return !!i[t]() || '​᠎' !== '​᠎'[t]() || (o && i[t].name !== t);
              });
            };
          },
          739: function (t, e, n) {
            var o = n(6518),
              r = n(9039),
              i = n(8981),
              a = n(2777);
            o(
              {
                target: 'Date',
                proto: !0,
                arity: 1,
                forced: r(function () {
                  return (
                    null !== new Date(NaN).toJSON() ||
                    1 !==
                      Date.prototype.toJSON.call({
                        toISOString: function () {
                          return 1;
                        },
                      })
                  );
                }),
              },
              {
                toJSON: function (t) {
                  var e = i(this),
                    n = a(e, 'number');
                  return 'number' != typeof n || isFinite(n)
                    ? e.toISOString()
                    : null;
                },
              },
            );
          },
          741: function (t) {
            var e = Math.ceil,
              n = Math.floor;
            t.exports =
              Math.trunc ||
              function (t) {
                var o = +t;
                return (o > 0 ? n : e)(o);
              };
          },
          744: function (t, e, n) {
            var o = n(9565),
              r = n(9504),
              i = n(9228),
              a = n(8551),
              s = n(4117),
              u = n(7750),
              c = n(2293),
              l = n(7829),
              f = n(8014),
              d = n(655),
              p = n(5966),
              h = n(6682),
              v = n(8429),
              y = n(9039),
              g = v.UNSUPPORTED_Y,
              m = Math.min,
              b = r([].push),
              x = r(''.slice),
              w = !y(function () {
                var t = /(?:)/,
                  e = t.exec;
                t.exec = function () {
                  return e.apply(this, arguments);
                };
                var n = 'ab'.split(t);
                return 2 !== n.length || 'a' !== n[0] || 'b' !== n[1];
              }),
              E =
                'c' === 'abbc'.split(/(b)*/)[1] ||
                4 !== 'test'.split(/(?:)/, -1).length ||
                2 !== 'ab'.split(/(?:ab)*/).length ||
                4 !== '.'.split(/(.?)(.?)/).length ||
                '.'.split(/()()/).length > 1 ||
                ''.split(/.?/).length;
            i(
              'split',
              function (t, e, n) {
                var r = '0'.split(void 0, 0).length
                  ? function (t, n) {
                      return void 0 === t && 0 === n ? [] : o(e, this, t, n);
                    }
                  : e;
                return [
                  function (e, n) {
                    var i = u(this),
                      a = s(e) ? void 0 : p(e, t);
                    return a ? o(a, e, i, n) : o(r, d(i), e, n);
                  },
                  function (t, o) {
                    var i = a(this),
                      s = d(t);
                    if (!E) {
                      var u = n(r, i, s, o, r !== e);
                      if (u.done) return u.value;
                    }
                    var p = c(i, RegExp),
                      v = i.unicode,
                      y =
                        (i.ignoreCase ? 'i' : '') +
                        (i.multiline ? 'm' : '') +
                        (i.unicode ? 'u' : '') +
                        (g ? 'g' : 'y'),
                      w = new p(g ? '^(?:' + i.source + ')' : i, y),
                      O = void 0 === o ? 4294967295 : o >>> 0;
                    if (0 === O) return [];
                    if (0 === s.length) return null === h(w, s) ? [s] : [];
                    for (var S = 0, k = 0, P = []; k < s.length; ) {
                      w.lastIndex = g ? 0 : k;
                      var I,
                        C = h(w, g ? x(s, k) : s);
                      if (
                        null === C ||
                        (I = m(f(w.lastIndex + (g ? k : 0)), s.length)) === S
                      )
                        k = l(s, k, v);
                      else {
                        if ((b(P, x(s, S, k)), P.length === O)) return P;
                        for (var A = 1; A <= C.length - 1; A++)
                          if ((b(P, C[A]), P.length === O)) return P;
                        k = S = I;
                      }
                    }
                    return (b(P, x(s, S)), P);
                  },
                ];
              },
              E || !w,
              g,
            );
          },
          757: function (t, e, n) {
            var o = n(7751),
              r = n(4901),
              i = n(1625),
              a = n(7040),
              s = Object;
            t.exports = a
              ? function (t) {
                  return 'symbol' == typeof t;
                }
              : function (t) {
                  var e = o('Symbol');
                  return r(e) && i(e.prototype, s(t));
                };
          },
          788: function (t, e, n) {
            var o = n(34),
              r = n(2195),
              i = n(8227)('match');
            t.exports = function (t) {
              var e;
              return o(t) && (void 0 !== (e = t[i]) ? !!e : 'RegExp' === r(t));
            };
          },
          825: function (t, e, n) {
            var o = n(6518),
              r = n(7751),
              i = n(8745),
              a = n(566),
              s = n(5548),
              u = n(8551),
              c = n(34),
              l = n(2360),
              f = n(9039),
              d = r('Reflect', 'construct'),
              p = Object.prototype,
              h = [].push,
              v = f(function () {
                function t() {}
                return !(d(function () {}, [], t) instanceof t);
              }),
              y = !f(function () {
                d(function () {});
              }),
              g = v || y;
            o(
              { target: 'Reflect', stat: !0, forced: g, sham: g },
              {
                construct: function (t, e) {
                  (s(t), u(e));
                  var n = arguments.length < 3 ? t : s(arguments[2]);
                  if (y && !v) return d(t, e, n);
                  if (t === n) {
                    switch (e.length) {
                      case 0:
                        return new t();
                      case 1:
                        return new t(e[0]);
                      case 2:
                        return new t(e[0], e[1]);
                      case 3:
                        return new t(e[0], e[1], e[2]);
                      case 4:
                        return new t(e[0], e[1], e[2], e[3]);
                    }
                    var o = [null];
                    return (i(h, o, e), new (i(a, t, o))());
                  }
                  var r = n.prototype,
                    f = l(c(r) ? r : p),
                    g = i(t, f, e);
                  return c(g) ? g : f;
                },
              },
            );
          },
          851: function (t, e, n) {
            var o = n(6955),
              r = n(5966),
              i = n(4117),
              a = n(6269),
              s = n(8227)('iterator');
            t.exports = function (t) {
              if (!i(t)) return r(t, s) || r(t, '@@iterator') || a[o(t)];
            };
          },
          926: function (t, e, n) {
            var o = n(9306),
              r = n(8981),
              i = n(7055),
              a = n(6198),
              s = TypeError,
              u = 'Reduce of empty array with no initial value',
              c = function (t) {
                return function (e, n, c, l) {
                  var f = r(e),
                    d = i(f),
                    p = a(f);
                  if ((o(n), 0 === p && c < 2)) throw new s(u);
                  var h = t ? p - 1 : 0,
                    v = t ? -1 : 1;
                  if (c < 2)
                    for (;;) {
                      if (h in d) {
                        ((l = d[h]), (h += v));
                        break;
                      }
                      if (((h += v), t ? h < 0 : p <= h)) throw new s(u);
                    }
                  for (; t ? h >= 0 : p > h; h += v)
                    h in d && (l = n(l, d[h], h, f));
                  return l;
                };
              };
            t.exports = { left: c(!1), right: c(!0) };
          },
          1034: function (t, e, n) {
            var o = n(9565),
              r = n(9297),
              i = n(1625),
              a = n(7979),
              s = RegExp.prototype;
            t.exports = function (t) {
              var e = t.flags;
              return void 0 !== e || 'flags' in s || r(t, 'flags') || !i(s, t)
                ? e
                : o(a, t);
            };
          },
          1056: function (t, e, n) {
            var o = n(4913).f;
            t.exports = function (t, e, n) {
              n in t ||
                o(t, n, {
                  configurable: !0,
                  get: function () {
                    return e[n];
                  },
                  set: function (t) {
                    e[n] = t;
                  },
                });
            };
          },
          1072: function (t, e, n) {
            var o = n(1828),
              r = n(8727);
            t.exports =
              Object.keys ||
              function (t) {
                return o(t, r);
              };
          },
          1088: function (t, e, n) {
            var o = n(6518),
              r = n(9565),
              i = n(6395),
              a = n(350),
              s = n(4901),
              u = n(3994),
              c = n(2787),
              l = n(2967),
              f = n(687),
              d = n(6699),
              p = n(6840),
              h = n(8227),
              v = n(6269),
              y = n(7657),
              g = a.PROPER,
              m = a.CONFIGURABLE,
              b = y.IteratorPrototype,
              x = y.BUGGY_SAFARI_ITERATORS,
              w = h('iterator'),
              E = 'keys',
              O = 'values',
              S = 'entries',
              k = function () {
                return this;
              };
            t.exports = function (t, e, n, a, h, y, P) {
              u(n, e, a);
              var I,
                C,
                A,
                M = function (t) {
                  if (t === h && R) return R;
                  if (!x && t && t in N) return N[t];
                  switch (t) {
                    case E:
                    case O:
                    case S:
                      return function () {
                        return new n(this, t);
                      };
                  }
                  return function () {
                    return new n(this);
                  };
                },
                D = e + ' Iterator',
                T = !1,
                N = t.prototype,
                j = N[w] || N['@@iterator'] || (h && N[h]),
                R = (!x && j) || M(h),
                L = ('Array' === e && N.entries) || j;
              if (
                (L &&
                  (I = c(L.call(new t()))) !== Object.prototype &&
                  I.next &&
                  (i || c(I) === b || (l ? l(I, b) : s(I[w]) || p(I, w, k)),
                  f(I, D, !0, !0),
                  i && (v[D] = k)),
                g &&
                  h === O &&
                  j &&
                  j.name !== O &&
                  (!i && m
                    ? d(N, 'name', O)
                    : ((T = !0),
                      (R = function () {
                        return r(j, this);
                      }))),
                h)
              )
                if (
                  ((C = { values: M(O), keys: y ? R : M(E), entries: M(S) }), P)
                )
                  for (A in C) (x || T || !(A in N)) && p(N, A, C[A]);
                else o({ target: e, proto: !0, forced: x || T }, C);
              return (
                (i && !P) || N[w] === R || p(N, w, R, { name: h }),
                (v[e] = R),
                C
              );
            };
          },
          1181: function (t, e, n) {
            var o,
              r,
              i,
              a = n(8622),
              s = n(4576),
              u = n(34),
              c = n(6699),
              l = n(9297),
              f = n(7629),
              d = n(6119),
              p = n(421),
              h = 'Object already initialized',
              v = s.TypeError,
              y = s.WeakMap;
            if (a || f.state) {
              var g = f.state || (f.state = new y());
              ((g.get = g.get),
                (g.has = g.has),
                (g.set = g.set),
                (o = function (t, e) {
                  if (g.has(t)) throw new v(h);
                  return ((e.facade = t), g.set(t, e), e);
                }),
                (r = function (t) {
                  return g.get(t) || {};
                }),
                (i = function (t) {
                  return g.has(t);
                }));
            } else {
              var m = d('state');
              ((p[m] = !0),
                (o = function (t, e) {
                  if (l(t, m)) throw new v(h);
                  return ((e.facade = t), c(t, m, e), e);
                }),
                (r = function (t) {
                  return l(t, m) ? t[m] : {};
                }),
                (i = function (t) {
                  return l(t, m);
                }));
            }
            t.exports = {
              set: o,
              get: r,
              has: i,
              enforce: function (t) {
                return i(t) ? r(t) : o(t, {});
              },
              getterFor: function (t) {
                return function (e) {
                  var n;
                  if (!u(e) || (n = r(e)).type !== t)
                    throw new v('Incompatible receiver, ' + t + ' required');
                  return n;
                };
              },
            };
          },
          1240: function (t, e, n) {
            var o = n(9504);
            t.exports = o((1).valueOf);
          },
          1278: function (t, e, n) {
            var o = n(6518),
              r = n(3724),
              i = n(5031),
              a = n(5397),
              s = n(7347),
              u = n(4659);
            o(
              { target: 'Object', stat: !0, sham: !r },
              {
                getOwnPropertyDescriptors: function (t) {
                  for (
                    var e, n, o = a(t), r = s.f, c = i(o), l = {}, f = 0;
                    c.length > f;

                  )
                    void 0 !== (n = r(o, (e = c[f++]))) && u(l, e, n);
                  return l;
                },
              },
            );
          },
          1291: function (t, e, n) {
            var o = n(741);
            t.exports = function (t) {
              var e = +t;
              return e != e || 0 === e ? 0 : o(e);
            };
          },
          1296: function (t, e, n) {
            var o = n(4495);
            t.exports = o && !!Symbol.for && !!Symbol.keyFor;
          },
          1436: function (t, e, n) {
            var o = n(8227)('match');
            t.exports = function (t) {
              var e = /./;
              try {
                '/./'[t](e);
              } catch (n) {
                try {
                  return ((e[o] = !1), '/./'[t](e));
                } catch (t) {}
              }
              return !1;
            };
          },
          1469: function (t, e, n) {
            var o = n(7433);
            t.exports = function (t, e) {
              return new (o(t))(0 === e ? 0 : e);
            };
          },
          1480: function (t, e, n) {
            var o = n(6518),
              r = n(9039),
              i = n(298).f;
            o(
              {
                target: 'Object',
                stat: !0,
                forced: r(function () {
                  return !Object.getOwnPropertyNames(1);
                }),
              },
              { getOwnPropertyNames: i },
            );
          },
          1510: function (t, e, n) {
            var o = n(6518),
              r = n(7751),
              i = n(9297),
              a = n(655),
              s = n(5745),
              u = n(1296),
              c = s('string-to-symbol-registry'),
              l = s('symbol-to-string-registry');
            o(
              { target: 'Symbol', stat: !0, forced: !u },
              {
                for: function (t) {
                  var e = a(t);
                  if (i(c, e)) return c[e];
                  var n = r('Symbol')(e);
                  return ((c[e] = n), (l[n] = e), n);
                },
              },
            );
          },
          1625: function (t, e, n) {
            var o = n(9504);
            t.exports = o({}.isPrototypeOf);
          },
          1699: function (t, e, n) {
            var o = n(6518),
              r = n(9504),
              i = n(5749),
              a = n(7750),
              s = n(655),
              u = n(1436),
              c = r(''.indexOf);
            o(
              { target: 'String', proto: !0, forced: !u('includes') },
              {
                includes: function (t) {
                  return !!~c(
                    s(a(this)),
                    s(i(t)),
                    arguments.length > 1 ? arguments[1] : void 0,
                  );
                },
              },
            );
          },
          1761: function (t, e, n) {
            var o = n(9565),
              r = n(9228),
              i = n(8551),
              a = n(4117),
              s = n(8014),
              u = n(655),
              c = n(7750),
              l = n(5966),
              f = n(7829),
              d = n(6682);
            r('match', function (t, e, n) {
              return [
                function (e) {
                  var n = c(this),
                    r = a(e) ? void 0 : l(e, t);
                  return r ? o(r, e, n) : new RegExp(e)[t](u(n));
                },
                function (t) {
                  var o = i(this),
                    r = u(t),
                    a = n(e, o, r);
                  if (a.done) return a.value;
                  if (!o.global) return d(o, r);
                  var c = o.unicode;
                  o.lastIndex = 0;
                  for (var l, p = [], h = 0; null !== (l = d(o, r)); ) {
                    var v = u(l[0]);
                    ((p[h] = v),
                      '' === v && (o.lastIndex = f(r, s(o.lastIndex), c)),
                      h++);
                  }
                  return 0 === h ? null : p;
                },
              ];
            });
          },
          1828: function (t, e, n) {
            var o = n(9504),
              r = n(9297),
              i = n(5397),
              a = n(9617).indexOf,
              s = n(421),
              u = o([].push);
            t.exports = function (t, e) {
              var n,
                o = i(t),
                c = 0,
                l = [];
              for (n in o) !r(s, n) && r(o, n) && u(l, n);
              for (; e.length > c; )
                r(o, (n = e[c++])) && (~a(l, n) || u(l, n));
              return l;
            };
          },
          1951: function (t, e, n) {
            var o = n(8227);
            e.f = o;
          },
          2008: function (t, e, n) {
            var o = n(6518),
              r = n(9213).filter;
            o(
              { target: 'Array', proto: !0, forced: !n(597)('filter') },
              {
                filter: function (t) {
                  return r(
                    this,
                    t,
                    arguments.length > 1 ? arguments[1] : void 0,
                  );
                },
              },
            );
          },
          2010: function (t, e, n) {
            var o = n(3724),
              r = n(350).EXISTS,
              i = n(9504),
              a = n(2106),
              s = Function.prototype,
              u = i(s.toString),
              c =
                /function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,
              l = i(c.exec);
            o &&
              !r &&
              a(s, 'name', {
                configurable: !0,
                get: function () {
                  try {
                    return l(c, u(this))[1];
                  } catch (t) {
                    return '';
                  }
                },
              });
          },
          2062: function (t, e, n) {
            var o = n(6518),
              r = n(9213).map;
            o(
              { target: 'Array', proto: !0, forced: !n(597)('map') },
              {
                map: function (t) {
                  return r(
                    this,
                    t,
                    arguments.length > 1 ? arguments[1] : void 0,
                  );
                },
              },
            );
          },
          2087: function (t, e, n) {
            var o = n(34),
              r = Math.floor;
            t.exports =
              Number.isInteger ||
              function (t) {
                return !o(t) && isFinite(t) && r(t) === t;
              };
          },
          2106: function (t, e, n) {
            var o = n(283),
              r = n(4913);
            t.exports = function (t, e, n) {
              return (
                n.get && o(n.get, e, { getter: !0 }),
                n.set && o(n.set, e, { setter: !0 }),
                r.f(t, e, n)
              );
            };
          },
          2140: function (t, e, n) {
            var o = {};
            ((o[n(8227)('toStringTag')] = 'z'),
              (t.exports = '[object z]' === String(o)));
          },
          2195: function (t, e, n) {
            var o = n(9504),
              r = o({}.toString),
              i = o(''.slice);
            t.exports = function (t) {
              return i(r(t), 8, -1);
            };
          },
          2211: function (t, e, n) {
            var o = n(9039);
            t.exports = !o(function () {
              function t() {}
              return (
                (t.prototype.constructor = null),
                Object.getPrototypeOf(new t()) !== t.prototype
              );
            });
          },
          2259: function (t, e, n) {
            n(511)('iterator');
          },
          2293: function (t, e, n) {
            var o = n(8551),
              r = n(5548),
              i = n(4117),
              a = n(8227)('species');
            t.exports = function (t, e) {
              var n,
                s = o(t).constructor;
              return void 0 === s || i((n = o(s)[a])) ? e : r(n);
            };
          },
          2360: function (t, e, n) {
            var o,
              r = n(8551),
              i = n(6801),
              a = n(8727),
              s = n(421),
              u = n(397),
              c = n(4055),
              l = n(6119),
              f = 'prototype',
              d = 'script',
              p = l('IE_PROTO'),
              h = function () {},
              v = function (t) {
                return '<' + d + '>' + t + '</' + d + '>';
              },
              y = function (t) {
                (t.write(v('')), t.close());
                var e = t.parentWindow.Object;
                return ((t = null), e);
              },
              g = function () {
                try {
                  o = new ActiveXObject('htmlfile');
                } catch (t) {}
                var t, e, n;
                g =
                  'undefined' != typeof document
                    ? document.domain && o
                      ? y(o)
                      : ((e = c('iframe')),
                        (n = 'java' + d + ':'),
                        (e.style.display = 'none'),
                        u.appendChild(e),
                        (e.src = String(n)),
                        (t = e.contentWindow.document).open(),
                        t.write(v('document.F=Object')),
                        t.close(),
                        t.F)
                    : y(o);
                for (var r = a.length; r--; ) delete g[f][a[r]];
                return g();
              };
            ((s[p] = !0),
              (t.exports =
                Object.create ||
                function (t, e) {
                  var n;
                  return (
                    null !== t
                      ? ((h[f] = r(t)),
                        (n = new h()),
                        (h[f] = null),
                        (n[p] = t))
                      : (n = g()),
                    void 0 === e ? n : i.f(n, e)
                  );
                }));
          },
          2478: function (t, e, n) {
            var o = n(9504),
              r = n(8981),
              i = Math.floor,
              a = o(''.charAt),
              s = o(''.replace),
              u = o(''.slice),
              c = /\$([$&'`]|\d{1,2}|<[^>]*>)/g,
              l = /\$([$&'`]|\d{1,2})/g;
            t.exports = function (t, e, n, o, f, d) {
              var p = n + t.length,
                h = o.length,
                v = l;
              return (
                void 0 !== f && ((f = r(f)), (v = c)),
                s(d, v, function (r, s) {
                  var c;
                  switch (a(s, 0)) {
                    case '$':
                      return '$';
                    case '&':
                      return t;
                    case '`':
                      return u(e, 0, n);
                    case "'":
                      return u(e, p);
                    case '<':
                      c = f[u(s, 1, -1)];
                      break;
                    default:
                      var l = +s;
                      if (0 === l) return r;
                      if (l > h) {
                        var d = i(l / 10);
                        return 0 === d
                          ? r
                          : d <= h
                            ? void 0 === o[d - 1]
                              ? a(s, 1)
                              : o[d - 1] + a(s, 1)
                            : r;
                      }
                      c = o[l - 1];
                  }
                  return void 0 === c ? '' : c;
                })
              );
            };
          },
          2529: function (t) {
            t.exports = function (t, e) {
              return { value: t, done: e };
            };
          },
          2551: function (t, e, n) {
            var o = n(6395),
              r = n(4576),
              i = n(9039),
              a = n(3607);
            t.exports =
              o ||
              !i(function () {
                if (!(a && a < 535)) {
                  var t = Math.random();
                  (__defineSetter__.call(null, t, function () {}), delete r[t]);
                }
              });
          },
          2637: function (t, e, n) {
            n(6518)({ target: 'Number', stat: !0 }, { isInteger: n(2087) });
          },
          2675: function (t, e, n) {
            (n(6761), n(1510), n(7812), n(3110), n(9773));
          },
          2712: function (t, e, n) {
            var o = n(6518),
              r = n(926).left,
              i = n(4598),
              a = n(9519);
            o(
              {
                target: 'Array',
                proto: !0,
                forced: (!n(6193) && a > 79 && a < 83) || !i('reduce'),
              },
              {
                reduce: function (t) {
                  var e = arguments.length;
                  return r(this, t, e, e > 1 ? arguments[1] : void 0);
                },
              },
            );
          },
          2762: function (t, e, n) {
            var o = n(6518),
              r = n(3802).trim;
            o(
              { target: 'String', proto: !0, forced: n(706)('trim') },
              {
                trim: function () {
                  return r(this);
                },
              },
            );
          },
          2777: function (t, e, n) {
            var o = n(9565),
              r = n(34),
              i = n(757),
              a = n(5966),
              s = n(4270),
              u = n(8227),
              c = TypeError,
              l = u('toPrimitive');
            t.exports = function (t, e) {
              if (!r(t) || i(t)) return t;
              var n,
                u = a(t, l);
              if (u) {
                if (
                  (void 0 === e && (e = 'default'),
                  (n = o(u, t, e)),
                  !r(n) || i(n))
                )
                  return n;
                throw new c("Can't convert object to primitive value");
              }
              return (void 0 === e && (e = 'number'), s(t, e));
            };
          },
          2787: function (t, e, n) {
            var o = n(9297),
              r = n(4901),
              i = n(8981),
              a = n(6119),
              s = n(2211),
              u = a('IE_PROTO'),
              c = Object,
              l = c.prototype;
            t.exports = s
              ? c.getPrototypeOf
              : function (t) {
                  var e = i(t);
                  if (o(e, u)) return e[u];
                  var n = e.constructor;
                  return r(n) && e instanceof n
                    ? n.prototype
                    : e instanceof c
                      ? l
                      : null;
                };
          },
          2796: function (t, e, n) {
            var o = n(9039),
              r = n(4901),
              i = /#|\.prototype\./,
              a = function (t, e) {
                var n = u[s(t)];
                return n === l || (n !== c && (r(e) ? o(e) : !!e));
              },
              s = (a.normalize = function (t) {
                return String(t).replace(i, '.').toLowerCase();
              }),
              u = (a.data = {}),
              c = (a.NATIVE = 'N'),
              l = (a.POLYFILL = 'P');
            t.exports = a;
          },
          2839: function (t, e, n) {
            var o = n(4576).navigator,
              r = o && o.userAgent;
            t.exports = r ? String(r) : '';
          },
          2892: function (t, e, n) {
            var o = n(6518),
              r = n(6395),
              i = n(3724),
              a = n(4576),
              s = n(9167),
              u = n(9504),
              c = n(2796),
              l = n(9297),
              f = n(3167),
              d = n(1625),
              p = n(757),
              h = n(2777),
              v = n(9039),
              y = n(8480).f,
              g = n(7347).f,
              m = n(4913).f,
              b = n(1240),
              x = n(3802).trim,
              w = 'Number',
              E = a[w],
              O = s[w],
              S = E.prototype,
              k = a.TypeError,
              P = u(''.slice),
              I = u(''.charCodeAt),
              C = function (t) {
                var e,
                  n,
                  o,
                  r,
                  i,
                  a,
                  s,
                  u,
                  c = h(t, 'number');
                if (p(c))
                  throw new k('Cannot convert a Symbol value to a number');
                if ('string' == typeof c && c.length > 2)
                  if (((c = x(c)), 43 === (e = I(c, 0)) || 45 === e)) {
                    if (88 === (n = I(c, 2)) || 120 === n) return NaN;
                  } else if (48 === e) {
                    switch (I(c, 1)) {
                      case 66:
                      case 98:
                        ((o = 2), (r = 49));
                        break;
                      case 79:
                      case 111:
                        ((o = 8), (r = 55));
                        break;
                      default:
                        return +c;
                    }
                    for (a = (i = P(c, 2)).length, s = 0; s < a; s++)
                      if ((u = I(i, s)) < 48 || u > r) return NaN;
                    return parseInt(i, o);
                  }
                return +c;
              },
              A = c(w, !E(' 0o1') || !E('0b1') || E('+0x1')),
              M = function (t) {
                var e,
                  n =
                    arguments.length < 1
                      ? 0
                      : E(
                          (function (t) {
                            var e = h(t, 'number');
                            return 'bigint' == typeof e ? e : C(e);
                          })(t),
                        );
                return d(S, (e = this)) &&
                  v(function () {
                    b(e);
                  })
                  ? f(Object(n), this, M)
                  : n;
              };
            ((M.prototype = S),
              A && !r && (S.constructor = M),
              o(
                { global: !0, constructor: !0, wrap: !0, forced: A },
                { Number: M },
              ));
            var D = function (t, e) {
              for (
                var n,
                  o = i
                    ? y(e)
                    : 'MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range'.split(
                        ',',
                      ),
                  r = 0;
                o.length > r;
                r++
              )
                l(e, (n = o[r])) && !l(t, n) && m(t, n, g(e, n));
            };
            (r && O && D(s[w], O), (A || r) && D(s[w], E));
          },
          2953: function (t, e, n) {
            var o = n(4576),
              r = n(7400),
              i = n(9296),
              a = n(3792),
              s = n(6699),
              u = n(687),
              c = n(8227)('iterator'),
              l = a.values,
              f = function (t, e) {
                if (t) {
                  if (t[c] !== l)
                    try {
                      s(t, c, l);
                    } catch (e) {
                      t[c] = l;
                    }
                  if ((u(t, e, !0), r[e]))
                    for (var n in a)
                      if (t[n] !== a[n])
                        try {
                          s(t, n, a[n]);
                        } catch (e) {
                          t[n] = a[n];
                        }
                }
              };
            for (var d in r) f(o[d] && o[d].prototype, d);
            f(i, 'DOMTokenList');
          },
          2967: function (t, e, n) {
            var o = n(6706),
              r = n(34),
              i = n(7750),
              a = n(3506);
            t.exports =
              Object.setPrototypeOf ||
              ('__proto__' in {}
                ? (function () {
                    var t,
                      e = !1,
                      n = {};
                    try {
                      ((t = o(Object.prototype, '__proto__', 'set'))(n, []),
                        (e = n instanceof Array));
                    } catch (t) {}
                    return function (n, o) {
                      return (
                        i(n),
                        a(o),
                        r(n) ? (e ? t(n, o) : (n.__proto__ = o), n) : n
                      );
                    };
                  })()
                : void 0);
          },
          3110: function (t, e, n) {
            var o = n(6518),
              r = n(7751),
              i = n(8745),
              a = n(9565),
              s = n(9504),
              u = n(9039),
              c = n(4901),
              l = n(757),
              f = n(7680),
              d = n(6933),
              p = n(4495),
              h = String,
              v = r('JSON', 'stringify'),
              y = s(/./.exec),
              g = s(''.charAt),
              m = s(''.charCodeAt),
              b = s(''.replace),
              x = s((1).toString),
              w = /[\uD800-\uDFFF]/g,
              E = /^[\uD800-\uDBFF]$/,
              O = /^[\uDC00-\uDFFF]$/,
              S =
                !p ||
                u(function () {
                  var t = r('Symbol')('stringify detection');
                  return (
                    '[null]' !== v([t]) ||
                    '{}' !== v({ a: t }) ||
                    '{}' !== v(Object(t))
                  );
                }),
              k = u(function () {
                return (
                  '"\\udf06\\ud834"' !== v('\udf06\ud834') ||
                  '"\\udead"' !== v('\udead')
                );
              }),
              P = function (t, e) {
                var n = f(arguments),
                  o = d(e);
                if (c(o) || (void 0 !== t && !l(t)))
                  return (
                    (n[1] = function (t, e) {
                      if ((c(o) && (e = a(o, this, h(t), e)), !l(e))) return e;
                    }),
                    i(v, null, n)
                  );
              },
              I = function (t, e, n) {
                var o = g(n, e - 1),
                  r = g(n, e + 1);
                return (y(E, t) && !y(O, r)) || (y(O, t) && !y(E, o))
                  ? '\\u' + x(m(t, 0), 16)
                  : t;
              };
            v &&
              o(
                { target: 'JSON', stat: !0, arity: 3, forced: S || k },
                {
                  stringify: function (t, e, n) {
                    var o = f(arguments),
                      r = i(S ? P : v, null, o);
                    return k && 'string' == typeof r ? b(r, w, I) : r;
                  },
                },
              );
          },
          3167: function (t, e, n) {
            var o = n(4901),
              r = n(34),
              i = n(2967);
            t.exports = function (t, e, n) {
              var a, s;
              return (
                i &&
                  o((a = e.constructor)) &&
                  a !== n &&
                  r((s = a.prototype)) &&
                  s !== n.prototype &&
                  i(t, s),
                t
              );
            };
          },
          3179: function (t, e, n) {
            var o = n(2140),
              r = n(6955);
            t.exports = o
              ? {}.toString
              : function () {
                  return '[object ' + r(this) + ']';
                };
          },
          3392: function (t, e, n) {
            var o = n(9504),
              r = 0,
              i = Math.random(),
              a = o((1).toString);
            t.exports = function (t) {
              return (
                'Symbol(' + (void 0 === t ? '' : t) + ')_' + a(++r + i, 36)
              );
            };
          },
          3418: function (t, e, n) {
            var o = n(6518),
              r = n(7916);
            o(
              {
                target: 'Array',
                stat: !0,
                forced: !n(4428)(function (t) {
                  Array.from(t);
                }),
              },
              { from: r },
            );
          },
          3500: function (t, e, n) {
            var o = n(4576),
              r = n(7400),
              i = n(9296),
              a = n(235),
              s = n(6699),
              u = function (t) {
                if (t && t.forEach !== a)
                  try {
                    s(t, 'forEach', a);
                  } catch (e) {
                    t.forEach = a;
                  }
              };
            for (var c in r) r[c] && u(o[c] && o[c].prototype);
            u(i);
          },
          3506: function (t, e, n) {
            var o = n(3925),
              r = String,
              i = TypeError;
            t.exports = function (t) {
              if (o(t)) return t;
              throw new i("Can't set " + r(t) + ' as a prototype');
            };
          },
          3517: function (t, e, n) {
            var o = n(9504),
              r = n(9039),
              i = n(4901),
              a = n(6955),
              s = n(7751),
              u = n(3706),
              c = function () {},
              l = s('Reflect', 'construct'),
              f = /^\s*(?:class|function)\b/,
              d = o(f.exec),
              p = !f.test(c),
              h = function (t) {
                if (!i(t)) return !1;
                try {
                  return (l(c, [], t), !0);
                } catch (t) {
                  return !1;
                }
              },
              v = function (t) {
                if (!i(t)) return !1;
                switch (a(t)) {
                  case 'AsyncFunction':
                  case 'GeneratorFunction':
                  case 'AsyncGeneratorFunction':
                    return !1;
                }
                try {
                  return p || !!d(f, u(t));
                } catch (t) {
                  return !0;
                }
              };
            ((v.sham = !0),
              (t.exports =
                !l ||
                r(function () {
                  var t;
                  return (
                    h(h.call) ||
                    !h(Object) ||
                    !h(function () {
                      t = !0;
                    }) ||
                    t
                  );
                })
                  ? v
                  : h));
          },
          3607: function (t, e, n) {
            var o = n(2839).match(/AppleWebKit\/(\d+)\./);
            t.exports = !!o && +o[1];
          },
          3635: function (t, e, n) {
            var o = n(9039),
              r = n(4576).RegExp;
            t.exports = o(function () {
              var t = r('.', 's');
              return !(t.dotAll && t.test('\n') && 's' === t.flags);
            });
          },
          3640: function (t, e, n) {
            var o = n(8551),
              r = n(4270),
              i = TypeError;
            t.exports = function (t) {
              if ((o(this), 'string' === t || 'default' === t)) t = 'string';
              else if ('number' !== t) throw new i('Incorrect hint');
              return r(this, t);
            };
          },
          3706: function (t, e, n) {
            var o = n(9504),
              r = n(4901),
              i = n(7629),
              a = o(Function.toString);
            (r(i.inspectSource) ||
              (i.inspectSource = function (t) {
                return a(t);
              }),
              (t.exports = i.inspectSource));
          },
          3709: function (t, e, n) {
            var o = n(2839).match(/firefox\/(\d+)/i);
            t.exports = !!o && +o[1];
          },
          3717: function (t, e) {
            e.f = Object.getOwnPropertySymbols;
          },
          3724: function (t, e, n) {
            var o = n(9039);
            t.exports = !o(function () {
              return (
                7 !==
                Object.defineProperty({}, 1, {
                  get: function () {
                    return 7;
                  },
                })[1]
              );
            });
          },
          3763: function (t, e, n) {
            var o = n(2839);
            t.exports = /MSIE|Trident/.test(o);
          },
          3792: function (t, e, n) {
            var o = n(5397),
              r = n(6469),
              i = n(6269),
              a = n(1181),
              s = n(4913).f,
              u = n(1088),
              c = n(2529),
              l = n(6395),
              f = n(3724),
              d = 'Array Iterator',
              p = a.set,
              h = a.getterFor(d);
            t.exports = u(
              Array,
              'Array',
              function (t, e) {
                p(this, { type: d, target: o(t), index: 0, kind: e });
              },
              function () {
                var t = h(this),
                  e = t.target,
                  n = t.index++;
                if (!e || n >= e.length)
                  return ((t.target = null), c(void 0, !0));
                switch (t.kind) {
                  case 'keys':
                    return c(n, !1);
                  case 'values':
                    return c(e[n], !1);
                }
                return c([n, e[n]], !1);
              },
              'values',
            );
            var v = (i.Arguments = i.Array);
            if (
              (r('keys'),
              r('values'),
              r('entries'),
              !l && f && 'values' !== v.name)
            )
              try {
                s(v, 'name', { value: 'values' });
              } catch (t) {}
          },
          3802: function (t, e, n) {
            var o = n(9504),
              r = n(7750),
              i = n(655),
              a = n(7452),
              s = o(''.replace),
              u = RegExp('^[' + a + ']+'),
              c = RegExp('(^|[^' + a + '])[' + a + ']+$'),
              l = function (t) {
                return function (e) {
                  var n = i(r(e));
                  return (
                    1 & t && (n = s(n, u, '')),
                    2 & t && (n = s(n, c, '$1')),
                    n
                  );
                };
              };
            t.exports = { start: l(1), end: l(2), trim: l(3) };
          },
          3851: function (t, e, n) {
            var o = n(6518),
              r = n(9039),
              i = n(5397),
              a = n(7347).f,
              s = n(3724);
            o(
              {
                target: 'Object',
                stat: !0,
                forced:
                  !s ||
                  r(function () {
                    a(1);
                  }),
                sham: !s,
              },
              {
                getOwnPropertyDescriptor: function (t, e) {
                  return a(i(t), e);
                },
              },
            );
          },
          3925: function (t, e, n) {
            var o = n(34);
            t.exports = function (t) {
              return o(t) || null === t;
            };
          },
          3994: function (t, e, n) {
            var o = n(7657).IteratorPrototype,
              r = n(2360),
              i = n(6980),
              a = n(687),
              s = n(6269),
              u = function () {
                return this;
              };
            t.exports = function (t, e, n, c) {
              var l = e + ' Iterator';
              return (
                (t.prototype = r(o, { next: i(+!c, n) })),
                a(t, l, !1, !0),
                (s[l] = u),
                t
              );
            };
          },
          4055: function (t, e, n) {
            var o = n(4576),
              r = n(34),
              i = o.document,
              a = r(i) && r(i.createElement);
            t.exports = function (t) {
              return a ? i.createElement(t) : {};
            };
          },
          4117: function (t) {
            t.exports = function (t) {
              return null == t;
            };
          },
          4209: function (t, e, n) {
            var o = n(8227),
              r = n(6269),
              i = o('iterator'),
              a = Array.prototype;
            t.exports = function (t) {
              return void 0 !== t && (r.Array === t || a[i] === t);
            };
          },
          4213: function (t, e, n) {
            var o = n(3724),
              r = n(9504),
              i = n(9565),
              a = n(9039),
              s = n(1072),
              u = n(3717),
              c = n(8773),
              l = n(8981),
              f = n(7055),
              d = Object.assign,
              p = Object.defineProperty,
              h = r([].concat);
            t.exports =
              !d ||
              a(function () {
                if (
                  o &&
                  1 !==
                    d(
                      { b: 1 },
                      d(
                        p({}, 'a', {
                          enumerable: !0,
                          get: function () {
                            p(this, 'b', { value: 3, enumerable: !1 });
                          },
                        }),
                        { b: 2 },
                      ),
                    ).b
                )
                  return !0;
                var t = {},
                  e = {},
                  n = Symbol('assign detection'),
                  r = 'abcdefghijklmnopqrst';
                return (
                  (t[n] = 7),
                  r.split('').forEach(function (t) {
                    e[t] = t;
                  }),
                  7 !== d({}, t)[n] || s(d({}, e)).join('') !== r
                );
              })
                ? function (t, e) {
                    for (
                      var n = l(t),
                        r = arguments.length,
                        a = 1,
                        d = u.f,
                        p = c.f;
                      r > a;

                    )
                      for (
                        var v,
                          y = f(arguments[a++]),
                          g = d ? h(s(y), d(y)) : s(y),
                          m = g.length,
                          b = 0;
                        m > b;

                      )
                        ((v = g[b++]), (o && !i(p, y, v)) || (n[v] = y[v]));
                    return n;
                  }
                : d;
          },
          4215: function (t, e, n) {
            var o = n(4576),
              r = n(2839),
              i = n(2195),
              a = function (t) {
                return r.slice(0, t.length) === t;
              };
            t.exports = a('Bun/')
              ? 'BUN'
              : a('Cloudflare-Workers')
                ? 'CLOUDFLARE'
                : a('Deno/')
                  ? 'DENO'
                  : a('Node.js/')
                    ? 'NODE'
                    : o.Bun && 'string' == typeof Bun.version
                      ? 'BUN'
                      : o.Deno && 'object' == typeof Deno.version
                        ? 'DENO'
                        : 'process' === i(o.process)
                          ? 'NODE'
                          : o.window && o.document
                            ? 'BROWSER'
                            : 'REST';
          },
          4270: function (t, e, n) {
            var o = n(9565),
              r = n(4901),
              i = n(34),
              a = TypeError;
            t.exports = function (t, e) {
              var n, s;
              if ('string' === e && r((n = t.toString)) && !i((s = o(n, t))))
                return s;
              if (r((n = t.valueOf)) && !i((s = o(n, t)))) return s;
              if ('string' !== e && r((n = t.toString)) && !i((s = o(n, t))))
                return s;
              throw new a("Can't convert object to primitive value");
            };
          },
          4376: function (t, e, n) {
            var o = n(2195);
            t.exports =
              Array.isArray ||
              function (t) {
                return 'Array' === o(t);
              };
          },
          4423: function (t, e, n) {
            var o = n(6518),
              r = n(9617).includes,
              i = n(9039),
              a = n(6469);
            (o(
              {
                target: 'Array',
                proto: !0,
                forced: i(function () {
                  return !Array(1).includes();
                }),
              },
              {
                includes: function (t) {
                  return r(
                    this,
                    t,
                    arguments.length > 1 ? arguments[1] : void 0,
                  );
                },
              },
            ),
              a('includes'));
          },
          4428: function (t, e, n) {
            var o = n(8227)('iterator'),
              r = !1;
            try {
              var i = 0,
                a = {
                  next: function () {
                    return { done: !!i++ };
                  },
                  return: function () {
                    r = !0;
                  },
                };
              ((a[o] = function () {
                return this;
              }),
                Array.from(a, function () {
                  throw 2;
                }));
            } catch (t) {}
            t.exports = function (t, e) {
              try {
                if (!e && !r) return !1;
              } catch (t) {
                return !1;
              }
              var n = !1;
              try {
                var i = {};
                ((i[o] = function () {
                  return {
                    next: function () {
                      return { done: (n = !0) };
                    },
                  };
                }),
                  t(i));
              } catch (t) {}
              return n;
            };
          },
          4488: function (t, e, n) {
            var o = n(7680),
              r = Math.floor,
              i = function (t, e) {
                var n = t.length;
                if (n < 8)
                  for (var a, s, u = 1; u < n; ) {
                    for (s = u, a = t[u]; s && e(t[s - 1], a) > 0; )
                      t[s] = t[--s];
                    s !== u++ && (t[s] = a);
                  }
                else
                  for (
                    var c = r(n / 2),
                      l = i(o(t, 0, c), e),
                      f = i(o(t, c), e),
                      d = l.length,
                      p = f.length,
                      h = 0,
                      v = 0;
                    h < d || v < p;

                  )
                    t[h + v] =
                      h < d && v < p
                        ? e(l[h], f[v]) <= 0
                          ? l[h++]
                          : f[v++]
                        : h < d
                          ? l[h++]
                          : f[v++];
                return t;
              };
            t.exports = i;
          },
          4495: function (t, e, n) {
            var o = n(9519),
              r = n(9039),
              i = n(4576).String;
            t.exports =
              !!Object.getOwnPropertySymbols &&
              !r(function () {
                var t = Symbol('symbol detection');
                return (
                  !i(t) ||
                  !(Object(t) instanceof Symbol) ||
                  (!Symbol.sham && o && o < 41)
                );
              });
          },
          4527: function (t, e, n) {
            var o = n(3724),
              r = n(4376),
              i = TypeError,
              a = Object.getOwnPropertyDescriptor,
              s =
                o &&
                !(function () {
                  if (void 0 !== this) return !0;
                  try {
                    Object.defineProperty([], 'length', {
                      writable: !1,
                    }).length = 1;
                  } catch (t) {
                    return t instanceof TypeError;
                  }
                })();
            t.exports = s
              ? function (t, e) {
                  if (r(t) && !a(t, 'length').writable)
                    throw new i('Cannot set read only .length');
                  return (t.length = e);
                }
              : function (t, e) {
                  return (t.length = e);
                };
          },
          4554: function (t, e, n) {
            var o = n(6518),
              r = n(8981),
              i = n(5610),
              a = n(1291),
              s = n(6198),
              u = n(4527),
              c = n(6837),
              l = n(1469),
              f = n(4659),
              d = n(4606),
              p = n(597)('splice'),
              h = Math.max,
              v = Math.min;
            o(
              { target: 'Array', proto: !0, forced: !p },
              {
                splice: function (t, e) {
                  var n,
                    o,
                    p,
                    y,
                    g,
                    m,
                    b = r(this),
                    x = s(b),
                    w = i(t, x),
                    E = arguments.length;
                  for (
                    0 === E
                      ? (n = o = 0)
                      : 1 === E
                        ? ((n = 0), (o = x - w))
                        : ((n = E - 2), (o = v(h(a(e), 0), x - w))),
                      c(x + n - o),
                      p = l(b, o),
                      y = 0;
                    y < o;
                    y++
                  )
                    (g = w + y) in b && f(p, y, b[g]);
                  if (((p.length = o), n < o)) {
                    for (y = w; y < x - o; y++)
                      ((m = y + n), (g = y + o) in b ? (b[m] = b[g]) : d(b, m));
                    for (y = x; y > x - o + n; y--) d(b, y - 1);
                  } else if (n > o)
                    for (y = x - o; y > w; y--)
                      ((m = y + n - 1),
                        (g = y + o - 1) in b ? (b[m] = b[g]) : d(b, m));
                  for (y = 0; y < n; y++) b[y + w] = arguments[y + 2];
                  return (u(b, x - o + n), p);
                },
              },
            );
          },
          4576: function (t, e, n) {
            var o = function (t) {
              return t && t.Math === Math && t;
            };
            t.exports =
              o('object' == typeof globalThis && globalThis) ||
              o('object' == typeof window && window) ||
              o('object' == typeof self && self) ||
              o('object' == typeof n.g && n.g) ||
              o('object' == typeof this && this) ||
              (function () {
                return this;
              })() ||
              Function('return this')();
          },
          4598: function (t, e, n) {
            var o = n(9039);
            t.exports = function (t, e) {
              var n = [][t];
              return (
                !!n &&
                o(function () {
                  n.call(
                    null,
                    e ||
                      function () {
                        return 1;
                      },
                    1,
                  );
                })
              );
            };
          },
          4606: function (t, e, n) {
            var o = n(6823),
              r = TypeError;
            t.exports = function (t, e) {
              if (!delete t[e])
                throw new r('Cannot delete property ' + o(e) + ' of ' + o(t));
            };
          },
          4659: function (t, e, n) {
            var o = n(3724),
              r = n(4913),
              i = n(6980);
            t.exports = function (t, e, n) {
              o ? r.f(t, e, i(0, n)) : (t[e] = n);
            };
          },
          4782: function (t, e, n) {
            var o = n(6518),
              r = n(4376),
              i = n(3517),
              a = n(34),
              s = n(5610),
              u = n(6198),
              c = n(5397),
              l = n(4659),
              f = n(8227),
              d = n(597),
              p = n(7680),
              h = d('slice'),
              v = f('species'),
              y = Array,
              g = Math.max;
            o(
              { target: 'Array', proto: !0, forced: !h },
              {
                slice: function (t, e) {
                  var n,
                    o,
                    f,
                    d = c(this),
                    h = u(d),
                    m = s(t, h),
                    b = s(void 0 === e ? h : e, h);
                  if (
                    r(d) &&
                    ((n = d.constructor),
                    ((i(n) && (n === y || r(n.prototype))) ||
                      (a(n) && null === (n = n[v]))) &&
                      (n = void 0),
                    n === y || void 0 === n)
                  )
                    return p(d, m, b);
                  for (
                    o = new (void 0 === n ? y : n)(g(b - m, 0)), f = 0;
                    m < b;
                    m++, f++
                  )
                    m in d && l(o, f, d[m]);
                  return ((o.length = f), o);
                },
              },
            );
          },
          4864: function (t, e, n) {
            var o = n(3724),
              r = n(4576),
              i = n(9504),
              a = n(2796),
              s = n(3167),
              u = n(6699),
              c = n(2360),
              l = n(8480).f,
              f = n(1625),
              d = n(788),
              p = n(655),
              h = n(1034),
              v = n(8429),
              y = n(1056),
              g = n(6840),
              m = n(9039),
              b = n(9297),
              x = n(1181).enforce,
              w = n(7633),
              E = n(8227),
              O = n(3635),
              S = n(8814),
              k = E('match'),
              P = r.RegExp,
              I = P.prototype,
              C = r.SyntaxError,
              A = i(I.exec),
              M = i(''.charAt),
              D = i(''.replace),
              T = i(''.indexOf),
              N = i(''.slice),
              j = /^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,
              R = /a/g,
              L = /a/g,
              B = new P(R) !== R,
              K = v.MISSED_STICKY,
              F = v.UNSUPPORTED_Y,
              _ =
                o &&
                (!B ||
                  K ||
                  O ||
                  S ||
                  m(function () {
                    return (
                      (L[k] = !1),
                      P(R) !== R || P(L) === L || '/a/i' !== String(P(R, 'i'))
                    );
                  }));
            if (a('RegExp', _)) {
              for (
                var U = function (t, e) {
                    var n,
                      o,
                      r,
                      i,
                      a,
                      l,
                      v = f(I, this),
                      y = d(t),
                      g = void 0 === e,
                      m = [],
                      w = t;
                    if (!v && y && g && t.constructor === U) return t;
                    if (
                      ((y || f(I, t)) && ((t = t.source), g && (e = h(w))),
                      (t = void 0 === t ? '' : p(t)),
                      (e = void 0 === e ? '' : p(e)),
                      (w = t),
                      O &&
                        ('dotAll' in R) &&
                        (o = !!e && T(e, 's') > -1) &&
                        (e = D(e, /s/g, '')),
                      (n = e),
                      K &&
                        ('sticky' in R) &&
                        (r = !!e && T(e, 'y') > -1) &&
                        F &&
                        (e = D(e, /y/g, '')),
                      S &&
                        ((i = (function (t) {
                          for (
                            var e,
                              n = t.length,
                              o = 0,
                              r = '',
                              i = [],
                              a = c(null),
                              s = !1,
                              u = !1,
                              l = 0,
                              f = '';
                            o <= n;
                            o++
                          ) {
                            if ('\\' === (e = M(t, o))) e += M(t, ++o);
                            else if (']' === e) s = !1;
                            else if (!s)
                              switch (!0) {
                                case '[' === e:
                                  s = !0;
                                  break;
                                case '(' === e:
                                  if (((r += e), '?:' === N(t, o + 1, o + 3)))
                                    continue;
                                  (A(j, N(t, o + 1)) && ((o += 2), (u = !0)),
                                    l++);
                                  continue;
                                case '>' === e && u:
                                  if ('' === f || b(a, f))
                                    throw new C('Invalid capture group name');
                                  ((a[f] = !0),
                                    (i[i.length] = [f, l]),
                                    (u = !1),
                                    (f = ''));
                                  continue;
                              }
                            u ? (f += e) : (r += e);
                          }
                          return [r, i];
                        })(t)),
                        (t = i[0]),
                        (m = i[1])),
                      (a = s(P(t, e), v ? this : I, U)),
                      (o || r || m.length) &&
                        ((l = x(a)),
                        o &&
                          ((l.dotAll = !0),
                          (l.raw = U(
                            (function (t) {
                              for (
                                var e, n = t.length, o = 0, r = '', i = !1;
                                o <= n;
                                o++
                              )
                                '\\' !== (e = M(t, o))
                                  ? i || '.' !== e
                                    ? ('[' === e
                                        ? (i = !0)
                                        : ']' === e && (i = !1),
                                      (r += e))
                                    : (r += '[\\s\\S]')
                                  : (r += e + M(t, ++o));
                              return r;
                            })(t),
                            n,
                          ))),
                        r && (l.sticky = !0),
                        m.length && (l.groups = m)),
                      t !== w)
                    )
                      try {
                        u(a, 'source', '' === w ? '(?:)' : w);
                      } catch (t) {}
                    return a;
                  },
                  H = l(P),
                  $ = 0;
                H.length > $;

              )
                y(U, P, H[$++]);
              ((I.constructor = U),
                (U.prototype = I),
                g(r, 'RegExp', U, { constructor: !0 }));
            }
            w('RegExp');
          },
          4901: function (t) {
            var e = 'object' == typeof document && document.all;
            t.exports =
              void 0 === e && void 0 !== e
                ? function (t) {
                    return 'function' == typeof t || t === e;
                  }
                : function (t) {
                    return 'function' == typeof t;
                  };
          },
          4913: function (t, e, n) {
            var o = n(3724),
              r = n(5917),
              i = n(8686),
              a = n(8551),
              s = n(6969),
              u = TypeError,
              c = Object.defineProperty,
              l = Object.getOwnPropertyDescriptor,
              f = 'enumerable',
              d = 'configurable',
              p = 'writable';
            e.f = o
              ? i
                ? function (t, e, n) {
                    if (
                      (a(t),
                      (e = s(e)),
                      a(n),
                      'function' == typeof t &&
                        'prototype' === e &&
                        'value' in n &&
                        p in n &&
                        !n[p])
                    ) {
                      var o = l(t, e);
                      o &&
                        o[p] &&
                        ((t[e] = n.value),
                        (n = {
                          configurable: d in n ? n[d] : o[d],
                          enumerable: f in n ? n[f] : o[f],
                          writable: !1,
                        }));
                    }
                    return c(t, e, n);
                  }
                : c
              : function (t, e, n) {
                  if ((a(t), (e = s(e)), a(n), r))
                    try {
                      return c(t, e, n);
                    } catch (t) {}
                  if ('get' in n || 'set' in n)
                    throw new u('Accessors not supported');
                  return ('value' in n && (t[e] = n.value), t);
                };
          },
          5031: function (t, e, n) {
            var o = n(7751),
              r = n(9504),
              i = n(8480),
              a = n(3717),
              s = n(8551),
              u = r([].concat);
            t.exports =
              o('Reflect', 'ownKeys') ||
              function (t) {
                var e = i.f(s(t)),
                  n = a.f;
                return n ? u(e, n(t)) : e;
              };
          },
          5276: function (t, e, n) {
            var o = n(6518),
              r = n(7476),
              i = n(9617).indexOf,
              a = n(4598),
              s = r([].indexOf),
              u = !!s && 1 / s([1], 1, -0) < 0;
            o(
              { target: 'Array', proto: !0, forced: u || !a('indexOf') },
              {
                indexOf: function (t) {
                  var e = arguments.length > 1 ? arguments[1] : void 0;
                  return u ? s(this, t, e) || 0 : i(this, t, e);
                },
              },
            );
          },
          5397: function (t, e, n) {
            var o = n(7055),
              r = n(7750);
            t.exports = function (t) {
              return o(r(t));
            };
          },
          5440: function (t, e, n) {
            var o = n(8745),
              r = n(9565),
              i = n(9504),
              a = n(9228),
              s = n(9039),
              u = n(8551),
              c = n(4901),
              l = n(4117),
              f = n(1291),
              d = n(8014),
              p = n(655),
              h = n(7750),
              v = n(7829),
              y = n(5966),
              g = n(2478),
              m = n(6682),
              b = n(8227)('replace'),
              x = Math.max,
              w = Math.min,
              E = i([].concat),
              O = i([].push),
              S = i(''.indexOf),
              k = i(''.slice),
              P = '$0' === 'a'.replace(/./, '$0'),
              I = !!/./[b] && '' === /./[b]('a', '$0');
            a(
              'replace',
              function (t, e, n) {
                var i = I ? '$' : '$0';
                return [
                  function (t, n) {
                    var o = h(this),
                      i = l(t) ? void 0 : y(t, b);
                    return i ? r(i, t, o, n) : r(e, p(o), t, n);
                  },
                  function (t, r) {
                    var a = u(this),
                      s = p(t);
                    if (
                      'string' == typeof r &&
                      -1 === S(r, i) &&
                      -1 === S(r, '$<')
                    ) {
                      var l = n(e, a, s, r);
                      if (l.done) return l.value;
                    }
                    var h = c(r);
                    h || (r = p(r));
                    var y,
                      b = a.global;
                    b && ((y = a.unicode), (a.lastIndex = 0));
                    for (
                      var P, I = [];
                      null !== (P = m(a, s)) && (O(I, P), b);

                    ) {
                      '' === p(P[0]) && (a.lastIndex = v(s, d(a.lastIndex), y));
                    }
                    for (var C, A = '', M = 0, D = 0; D < I.length; D++) {
                      for (
                        var T,
                          N = p((P = I[D])[0]),
                          j = x(w(f(P.index), s.length), 0),
                          R = [],
                          L = 1;
                        L < P.length;
                        L++
                      )
                        O(R, void 0 === (C = P[L]) ? C : String(C));
                      var B = P.groups;
                      if (h) {
                        var K = E([N], R, j, s);
                        (void 0 !== B && O(K, B), (T = p(o(r, void 0, K))));
                      } else T = g(N, s, j, R, B, r);
                      j >= M && ((A += k(s, M, j) + T), (M = j + N.length));
                    }
                    return A + k(s, M);
                  },
                ];
              },
              !!s(function () {
                var t = /./;
                return (
                  (t.exec = function () {
                    var t = [];
                    return ((t.groups = { a: '7' }), t);
                  }),
                  '7' !== ''.replace(t, '$<a>')
                );
              }) ||
                !P ||
                I,
            );
          },
          5548: function (t, e, n) {
            var o = n(3517),
              r = n(6823),
              i = TypeError;
            t.exports = function (t) {
              if (o(t)) return t;
              throw new i(r(t) + ' is not a constructor');
            };
          },
          5610: function (t, e, n) {
            var o = n(1291),
              r = Math.max,
              i = Math.min;
            t.exports = function (t, e) {
              var n = o(t);
              return n < 0 ? r(n + e, 0) : i(n, e);
            };
          },
          5700: function (t, e, n) {
            var o = n(511),
              r = n(8242);
            (o('toPrimitive'), r());
          },
          5745: function (t, e, n) {
            var o = n(7629);
            t.exports = function (t, e) {
              return o[t] || (o[t] = e || {});
            };
          },
          5749: function (t, e, n) {
            var o = n(788),
              r = TypeError;
            t.exports = function (t) {
              if (o(t))
                throw new r("The method doesn't accept regular expressions");
              return t;
            };
          },
          5917: function (t, e, n) {
            var o = n(3724),
              r = n(9039),
              i = n(4055);
            t.exports =
              !o &&
              !r(function () {
                return (
                  7 !==
                  Object.defineProperty(i('div'), 'a', {
                    get: function () {
                      return 7;
                    },
                  }).a
                );
              });
          },
          5966: function (t, e, n) {
            var o = n(9306),
              r = n(4117);
            t.exports = function (t, e) {
              var n = t[e];
              return r(n) ? void 0 : o(n);
            };
          },
          6080: function (t, e, n) {
            var o = n(7476),
              r = n(9306),
              i = n(616),
              a = o(o.bind);
            t.exports = function (t, e) {
              return (
                r(t),
                void 0 === e
                  ? t
                  : i
                    ? a(t, e)
                    : function () {
                        return t.apply(e, arguments);
                      }
              );
            };
          },
          6099: function (t, e, n) {
            var o = n(2140),
              r = n(6840),
              i = n(3179);
            o || r(Object.prototype, 'toString', i, { unsafe: !0 });
          },
          6119: function (t, e, n) {
            var o = n(5745),
              r = n(3392),
              i = o('keys');
            t.exports = function (t) {
              return i[t] || (i[t] = r(t));
            };
          },
          6193: function (t, e, n) {
            var o = n(4215);
            t.exports = 'NODE' === o;
          },
          6198: function (t, e, n) {
            var o = n(8014);
            t.exports = function (t) {
              return o(t.length);
            };
          },
          6269: function (t) {
            t.exports = {};
          },
          6319: function (t, e, n) {
            var o = n(8551),
              r = n(9539);
            t.exports = function (t, e, n, i) {
              try {
                return i ? e(o(n)[0], n[1]) : e(n);
              } catch (e) {
                r(t, 'throw', e);
              }
            };
          },
          6395: function (t) {
            t.exports = !1;
          },
          6469: function (t, e, n) {
            var o = n(8227),
              r = n(2360),
              i = n(4913).f,
              a = o('unscopables'),
              s = Array.prototype;
            (void 0 === s[a] && i(s, a, { configurable: !0, value: r(null) }),
              (t.exports = function (t) {
                s[a][t] = !0;
              }));
          },
          6518: function (t, e, n) {
            var o = n(4576),
              r = n(7347).f,
              i = n(6699),
              a = n(6840),
              s = n(9433),
              u = n(7740),
              c = n(2796);
            t.exports = function (t, e) {
              var n,
                l,
                f,
                d,
                p,
                h = t.target,
                v = t.global,
                y = t.stat;
              if ((n = v ? o : y ? o[h] || s(h, {}) : o[h] && o[h].prototype))
                for (l in e) {
                  if (
                    ((d = e[l]),
                    (f = t.dontCallGetSet ? (p = r(n, l)) && p.value : n[l]),
                    !c(v ? l : h + (y ? '.' : '#') + l, t.forced) &&
                      void 0 !== f)
                  ) {
                    if (typeof d == typeof f) continue;
                    u(d, f);
                  }
                  ((t.sham || (f && f.sham)) && i(d, 'sham', !0),
                    a(n, l, d, t));
                }
            };
          },
          6682: function (t, e, n) {
            var o = n(9565),
              r = n(8551),
              i = n(4901),
              a = n(2195),
              s = n(7323),
              u = TypeError;
            t.exports = function (t, e) {
              var n = t.exec;
              if (i(n)) {
                var c = o(n, t, e);
                return (null !== c && r(c), c);
              }
              if ('RegExp' === a(t)) return o(s, t, e);
              throw new u('RegExp#exec called on incompatible receiver');
            };
          },
          6699: function (t, e, n) {
            var o = n(3724),
              r = n(4913),
              i = n(6980);
            t.exports = o
              ? function (t, e, n) {
                  return r.f(t, e, i(1, n));
                }
              : function (t, e, n) {
                  return ((t[e] = n), t);
                };
          },
          6706: function (t, e, n) {
            var o = n(9504),
              r = n(9306);
            t.exports = function (t, e, n) {
              try {
                return o(r(Object.getOwnPropertyDescriptor(t, e)[n]));
              } catch (t) {}
            };
          },
          6761: function (t, e, n) {
            var o = n(6518),
              r = n(4576),
              i = n(9565),
              a = n(9504),
              s = n(6395),
              u = n(3724),
              c = n(4495),
              l = n(9039),
              f = n(9297),
              d = n(1625),
              p = n(8551),
              h = n(5397),
              v = n(6969),
              y = n(655),
              g = n(6980),
              m = n(2360),
              b = n(1072),
              x = n(8480),
              w = n(298),
              E = n(3717),
              O = n(7347),
              S = n(4913),
              k = n(6801),
              P = n(8773),
              I = n(6840),
              C = n(2106),
              A = n(5745),
              M = n(6119),
              D = n(421),
              T = n(3392),
              N = n(8227),
              j = n(1951),
              R = n(511),
              L = n(8242),
              B = n(687),
              K = n(1181),
              F = n(9213).forEach,
              _ = M('hidden'),
              U = 'Symbol',
              H = 'prototype',
              $ = K.set,
              G = K.getterFor(U),
              V = Object[H],
              z = r.Symbol,
              Y = z && z[H],
              W = r.RangeError,
              J = r.TypeError,
              X = r.QObject,
              q = O.f,
              Q = S.f,
              Z = w.f,
              tt = P.f,
              et = a([].push),
              nt = A('symbols'),
              ot = A('op-symbols'),
              rt = A('wks'),
              it = !X || !X[H] || !X[H].findChild,
              at = function (t, e, n) {
                var o = q(V, e);
                (o && delete V[e], Q(t, e, n), o && t !== V && Q(V, e, o));
              },
              st =
                u &&
                l(function () {
                  return (
                    7 !==
                    m(
                      Q({}, 'a', {
                        get: function () {
                          return Q(this, 'a', { value: 7 }).a;
                        },
                      }),
                    ).a
                  );
                })
                  ? at
                  : Q,
              ut = function (t, e) {
                var n = (nt[t] = m(Y));
                return (
                  $(n, { type: U, tag: t, description: e }),
                  u || (n.description = e),
                  n
                );
              },
              ct = function (t, e, n) {
                (t === V && ct(ot, e, n), p(t));
                var o = v(e);
                return (
                  p(n),
                  f(nt, o)
                    ? (n.enumerable
                        ? (f(t, _) && t[_][o] && (t[_][o] = !1),
                          (n = m(n, { enumerable: g(0, !1) })))
                        : (f(t, _) || Q(t, _, g(1, m(null))), (t[_][o] = !0)),
                      st(t, o, n))
                    : Q(t, o, n)
                );
              },
              lt = function (t, e) {
                p(t);
                var n = h(e),
                  o = b(n).concat(ht(n));
                return (
                  F(o, function (e) {
                    (u && !i(ft, n, e)) || ct(t, e, n[e]);
                  }),
                  t
                );
              },
              ft = function (t) {
                var e = v(t),
                  n = i(tt, this, e);
                return (
                  !(this === V && f(nt, e) && !f(ot, e)) &&
                  (!(
                    n ||
                    !f(this, e) ||
                    !f(nt, e) ||
                    (f(this, _) && this[_][e])
                  ) ||
                    n)
                );
              },
              dt = function (t, e) {
                var n = h(t),
                  o = v(e);
                if (n !== V || !f(nt, o) || f(ot, o)) {
                  var r = q(n, o);
                  return (
                    !r ||
                      !f(nt, o) ||
                      (f(n, _) && n[_][o]) ||
                      (r.enumerable = !0),
                    r
                  );
                }
              },
              pt = function (t) {
                var e = Z(h(t)),
                  n = [];
                return (
                  F(e, function (t) {
                    f(nt, t) || f(D, t) || et(n, t);
                  }),
                  n
                );
              },
              ht = function (t) {
                var e = t === V,
                  n = Z(e ? ot : h(t)),
                  o = [];
                return (
                  F(n, function (t) {
                    !f(nt, t) || (e && !f(V, t)) || et(o, nt[t]);
                  }),
                  o
                );
              };
            (c ||
              ((z = function () {
                if (d(Y, this)) throw new J('Symbol is not a constructor');
                var t =
                    arguments.length && void 0 !== arguments[0]
                      ? y(arguments[0])
                      : void 0,
                  e = T(t),
                  n = function (t) {
                    var o = void 0 === this ? r : this;
                    (o === V && i(n, ot, t),
                      f(o, _) && f(o[_], e) && (o[_][e] = !1));
                    var a = g(1, t);
                    try {
                      st(o, e, a);
                    } catch (t) {
                      if (!(t instanceof W)) throw t;
                      at(o, e, a);
                    }
                  };
                return (
                  u && it && st(V, e, { configurable: !0, set: n }),
                  ut(e, t)
                );
              }),
              I((Y = z[H]), 'toString', function () {
                return G(this).tag;
              }),
              I(z, 'withoutSetter', function (t) {
                return ut(T(t), t);
              }),
              (P.f = ft),
              (S.f = ct),
              (k.f = lt),
              (O.f = dt),
              (x.f = w.f = pt),
              (E.f = ht),
              (j.f = function (t) {
                return ut(N(t), t);
              }),
              u &&
                (C(Y, 'description', {
                  configurable: !0,
                  get: function () {
                    return G(this).description;
                  },
                }),
                s || I(V, 'propertyIsEnumerable', ft, { unsafe: !0 }))),
              o(
                { global: !0, constructor: !0, wrap: !0, forced: !c, sham: !c },
                { Symbol: z },
              ),
              F(b(rt), function (t) {
                R(t);
              }),
              o(
                { target: U, stat: !0, forced: !c },
                {
                  useSetter: function () {
                    it = !0;
                  },
                  useSimple: function () {
                    it = !1;
                  },
                },
              ),
              o(
                { target: 'Object', stat: !0, forced: !c, sham: !u },
                {
                  create: function (t, e) {
                    return void 0 === e ? m(t) : lt(m(t), e);
                  },
                  defineProperty: ct,
                  defineProperties: lt,
                  getOwnPropertyDescriptor: dt,
                },
              ),
              o(
                { target: 'Object', stat: !0, forced: !c },
                { getOwnPropertyNames: pt },
              ),
              L(),
              B(z, U),
              (D[_] = !0));
          },
          6801: function (t, e, n) {
            var o = n(3724),
              r = n(8686),
              i = n(4913),
              a = n(8551),
              s = n(5397),
              u = n(1072);
            e.f =
              o && !r
                ? Object.defineProperties
                : function (t, e) {
                    a(t);
                    for (
                      var n, o = s(e), r = u(e), c = r.length, l = 0;
                      c > l;

                    )
                      i.f(t, (n = r[l++]), o[n]);
                    return t;
                  };
          },
          6823: function (t) {
            var e = String;
            t.exports = function (t) {
              try {
                return e(t);
              } catch (t) {
                return 'Object';
              }
            };
          },
          6837: function (t) {
            var e = TypeError;
            t.exports = function (t) {
              if (t > 9007199254740991)
                throw e('Maximum allowed index exceeded');
              return t;
            };
          },
          6840: function (t, e, n) {
            var o = n(4901),
              r = n(4913),
              i = n(283),
              a = n(9433);
            t.exports = function (t, e, n, s) {
              s || (s = {});
              var u = s.enumerable,
                c = void 0 !== s.name ? s.name : e;
              if ((o(n) && i(n, c, s), s.global)) u ? (t[e] = n) : a(e, n);
              else {
                try {
                  s.unsafe ? t[e] && (u = !0) : delete t[e];
                } catch (t) {}
                u
                  ? (t[e] = n)
                  : r.f(t, e, {
                      value: n,
                      enumerable: !1,
                      configurable: !s.nonConfigurable,
                      writable: !s.nonWritable,
                    });
              }
              return t;
            };
          },
          6910: function (t, e, n) {
            var o = n(6518),
              r = n(9504),
              i = n(9306),
              a = n(8981),
              s = n(6198),
              u = n(4606),
              c = n(655),
              l = n(9039),
              f = n(4488),
              d = n(4598),
              p = n(3709),
              h = n(3763),
              v = n(9519),
              y = n(3607),
              g = [],
              m = r(g.sort),
              b = r(g.push),
              x = l(function () {
                g.sort(void 0);
              }),
              w = l(function () {
                g.sort(null);
              }),
              E = d('sort'),
              O = !l(function () {
                if (v) return v < 70;
                if (!(p && p > 3)) {
                  if (h) return !0;
                  if (y) return y < 603;
                  var t,
                    e,
                    n,
                    o,
                    r = '';
                  for (t = 65; t < 76; t++) {
                    switch (((e = String.fromCharCode(t)), t)) {
                      case 66:
                      case 69:
                      case 70:
                      case 72:
                        n = 3;
                        break;
                      case 68:
                      case 71:
                        n = 4;
                        break;
                      default:
                        n = 2;
                    }
                    for (o = 0; o < 47; o++) g.push({ k: e + o, v: n });
                  }
                  for (
                    g.sort(function (t, e) {
                      return e.v - t.v;
                    }),
                      o = 0;
                    o < g.length;
                    o++
                  )
                    ((e = g[o].k.charAt(0)),
                      r.charAt(r.length - 1) !== e && (r += e));
                  return 'DGBEFHACIJK' !== r;
                }
              });
            o(
              { target: 'Array', proto: !0, forced: x || !w || !E || !O },
              {
                sort: function (t) {
                  void 0 !== t && i(t);
                  var e = a(this);
                  if (O) return void 0 === t ? m(e) : m(e, t);
                  var n,
                    o,
                    r = [],
                    l = s(e);
                  for (o = 0; o < l; o++) o in e && b(r, e[o]);
                  for (
                    f(
                      r,
                      (function (t) {
                        return function (e, n) {
                          return void 0 === n
                            ? -1
                            : void 0 === e
                              ? 1
                              : void 0 !== t
                                ? +t(e, n) || 0
                                : c(e) > c(n)
                                  ? 1
                                  : -1;
                        };
                      })(t),
                    ),
                      n = s(r),
                      o = 0;
                    o < n;

                  )
                    e[o] = r[o++];
                  for (; o < l; ) u(e, o++);
                  return e;
                },
              },
            );
          },
          6933: function (t, e, n) {
            var o = n(9504),
              r = n(4376),
              i = n(4901),
              a = n(2195),
              s = n(655),
              u = o([].push);
            t.exports = function (t) {
              if (i(t)) return t;
              if (r(t)) {
                for (var e = t.length, n = [], o = 0; o < e; o++) {
                  var c = t[o];
                  'string' == typeof c
                    ? u(n, c)
                    : ('number' != typeof c &&
                        'Number' !== a(c) &&
                        'String' !== a(c)) ||
                      u(n, s(c));
                }
                var l = n.length,
                  f = !0;
                return function (t, e) {
                  if (f) return ((f = !1), e);
                  if (r(this)) return e;
                  for (var o = 0; o < l; o++) if (n[o] === t) return e;
                };
              }
            };
          },
          6955: function (t, e, n) {
            var o = n(2140),
              r = n(4901),
              i = n(2195),
              a = n(8227)('toStringTag'),
              s = Object,
              u =
                'Arguments' ===
                i(
                  (function () {
                    return arguments;
                  })(),
                );
            t.exports = o
              ? i
              : function (t) {
                  var e, n, o;
                  return void 0 === t
                    ? 'Undefined'
                    : null === t
                      ? 'Null'
                      : 'string' ==
                          typeof (n = (function (t, e) {
                            try {
                              return t[e];
                            } catch (t) {}
                          })((e = s(t)), a))
                        ? n
                        : u
                          ? i(e)
                          : 'Object' === (o = i(e)) && r(e.callee)
                            ? 'Arguments'
                            : o;
                };
          },
          6969: function (t, e, n) {
            var o = n(2777),
              r = n(757);
            t.exports = function (t) {
              var e = o(t, 'string');
              return r(e) ? e : e + '';
            };
          },
          6980: function (t) {
            t.exports = function (t, e) {
              return {
                enumerable: !(1 & t),
                configurable: !(2 & t),
                writable: !(4 & t),
                value: e,
              };
            };
          },
          7040: function (t, e, n) {
            var o = n(4495);
            t.exports = o && !Symbol.sham && 'symbol' == typeof Symbol.iterator;
          },
          7055: function (t, e, n) {
            var o = n(9504),
              r = n(9039),
              i = n(2195),
              a = Object,
              s = o(''.split);
            t.exports = r(function () {
              return !a('z').propertyIsEnumerable(0);
            })
              ? function (t) {
                  return 'String' === i(t) ? s(t, '') : a(t);
                }
              : a;
          },
          7323: function (t, e, n) {
            var o,
              r,
              i = n(9565),
              a = n(9504),
              s = n(655),
              u = n(7979),
              c = n(8429),
              l = n(5745),
              f = n(2360),
              d = n(1181).get,
              p = n(3635),
              h = n(8814),
              v = l('native-string-replace', String.prototype.replace),
              y = RegExp.prototype.exec,
              g = y,
              m = a(''.charAt),
              b = a(''.indexOf),
              x = a(''.replace),
              w = a(''.slice),
              E =
                ((r = /b*/g),
                i(y, (o = /a/), 'a'),
                i(y, r, 'a'),
                0 !== o.lastIndex || 0 !== r.lastIndex),
              O = c.BROKEN_CARET,
              S = void 0 !== /()??/.exec('')[1];
            ((E || S || O || p || h) &&
              (g = function (t) {
                var e,
                  n,
                  o,
                  r,
                  a,
                  c,
                  l,
                  p = this,
                  h = d(p),
                  k = s(t),
                  P = h.raw;
                if (P)
                  return (
                    (P.lastIndex = p.lastIndex),
                    (e = i(g, P, k)),
                    (p.lastIndex = P.lastIndex),
                    e
                  );
                var I = h.groups,
                  C = O && p.sticky,
                  A = i(u, p),
                  M = p.source,
                  D = 0,
                  T = k;
                if (
                  (C &&
                    ((A = x(A, 'y', '')),
                    -1 === b(A, 'g') && (A += 'g'),
                    (T = w(k, p.lastIndex)),
                    p.lastIndex > 0 &&
                      (!p.multiline ||
                        (p.multiline && '\n' !== m(k, p.lastIndex - 1))) &&
                      ((M = '(?: ' + M + ')'), (T = ' ' + T), D++),
                    (n = new RegExp('^(?:' + M + ')', A))),
                  S && (n = new RegExp('^' + M + '$(?!\\s)', A)),
                  E && (o = p.lastIndex),
                  (r = i(y, C ? n : p, T)),
                  C
                    ? r
                      ? ((r.input = w(r.input, D)),
                        (r[0] = w(r[0], D)),
                        (r.index = p.lastIndex),
                        (p.lastIndex += r[0].length))
                      : (p.lastIndex = 0)
                    : E &&
                      r &&
                      (p.lastIndex = p.global ? r.index + r[0].length : o),
                  S &&
                    r &&
                    r.length > 1 &&
                    i(v, r[0], n, function () {
                      for (a = 1; a < arguments.length - 2; a++)
                        void 0 === arguments[a] && (r[a] = void 0);
                    }),
                  r && I)
                )
                  for (r.groups = c = f(null), a = 0; a < I.length; a++)
                    c[(l = I[a])[0]] = r[l[1]];
                return r;
              }),
              (t.exports = g));
          },
          7347: function (t, e, n) {
            var o = n(3724),
              r = n(9565),
              i = n(8773),
              a = n(6980),
              s = n(5397),
              u = n(6969),
              c = n(9297),
              l = n(5917),
              f = Object.getOwnPropertyDescriptor;
            e.f = o
              ? f
              : function (t, e) {
                  if (((t = s(t)), (e = u(e)), l))
                    try {
                      return f(t, e);
                    } catch (t) {}
                  if (c(t, e)) return a(!r(i.f, t, e), t[e]);
                };
          },
          7400: function (t) {
            t.exports = {
              CSSRuleList: 0,
              CSSStyleDeclaration: 0,
              CSSValueList: 0,
              ClientRectList: 0,
              DOMRectList: 0,
              DOMStringList: 0,
              DOMTokenList: 1,
              DataTransferItemList: 0,
              FileList: 0,
              HTMLAllCollection: 0,
              HTMLCollection: 0,
              HTMLFormElement: 0,
              HTMLSelectElement: 0,
              MediaList: 0,
              MimeTypeArray: 0,
              NamedNodeMap: 0,
              NodeList: 1,
              PaintRequestList: 0,
              Plugin: 0,
              PluginArray: 0,
              SVGLengthList: 0,
              SVGNumberList: 0,
              SVGPathSegList: 0,
              SVGPointList: 0,
              SVGStringList: 0,
              SVGTransformList: 0,
              SourceBufferList: 0,
              StyleSheetList: 0,
              TextTrackCueList: 0,
              TextTrackList: 0,
              TouchList: 0,
            };
          },
          7427: function (t, e, n) {
            var o = n(6518),
              r = n(3724),
              i = n(2551),
              a = n(9306),
              s = n(8981),
              u = n(4913);
            r &&
              o(
                { target: 'Object', proto: !0, forced: i },
                {
                  __defineGetter__: function (t, e) {
                    u.f(s(this), t, {
                      get: a(e),
                      enumerable: !0,
                      configurable: !0,
                    });
                  },
                },
              );
          },
          7433: function (t, e, n) {
            var o = n(4376),
              r = n(3517),
              i = n(34),
              a = n(8227)('species'),
              s = Array;
            t.exports = function (t) {
              var e;
              return (
                o(t) &&
                  ((e = t.constructor),
                  ((r(e) && (e === s || o(e.prototype))) ||
                    (i(e) && null === (e = e[a]))) &&
                    (e = void 0)),
                void 0 === e ? s : e
              );
            };
          },
          7452: function (t) {
            t.exports = '\t\n\v\f\r                　\u2028\u2029\ufeff';
          },
          7476: function (t, e, n) {
            var o = n(2195),
              r = n(9504);
            t.exports = function (t) {
              if ('Function' === o(t)) return r(t);
            };
          },
          7495: function (t, e, n) {
            var o = n(6518),
              r = n(7323);
            o(
              { target: 'RegExp', proto: !0, forced: /./.exec !== r },
              { exec: r },
            );
          },
          7629: function (t, e, n) {
            var o = n(6395),
              r = n(4576),
              i = n(9433),
              a = '__core-js_shared__',
              s = (t.exports = r[a] || i(a, {}));
            (s.versions || (s.versions = [])).push({
              version: '3.41.0',
              mode: o ? 'pure' : 'global',
              copyright: '© 2014-2025 Denis Pushkarev (zloirock.ru)',
              license:
                'https://github.com/zloirock/core-js/blob/v3.41.0/LICENSE',
              source: 'https://github.com/zloirock/core-js',
            });
          },
          7633: function (t, e, n) {
            var o = n(7751),
              r = n(2106),
              i = n(8227),
              a = n(3724),
              s = i('species');
            t.exports = function (t) {
              var e = o(t);
              a &&
                e &&
                !e[s] &&
                r(e, s, {
                  configurable: !0,
                  get: function () {
                    return this;
                  },
                });
            };
          },
          7657: function (t, e, n) {
            var o,
              r,
              i,
              a = n(9039),
              s = n(4901),
              u = n(34),
              c = n(2360),
              l = n(2787),
              f = n(6840),
              d = n(8227),
              p = n(6395),
              h = d('iterator'),
              v = !1;
            ([].keys &&
              ('next' in (i = [].keys())
                ? (r = l(l(i))) !== Object.prototype && (o = r)
                : (v = !0)),
              !u(o) ||
              a(function () {
                var t = {};
                return o[h].call(t) !== t;
              })
                ? (o = {})
                : p && (o = c(o)),
              s(o[h]) ||
                f(o, h, function () {
                  return this;
                }),
              (t.exports = {
                IteratorPrototype: o,
                BUGGY_SAFARI_ITERATORS: v,
              }));
          },
          7680: function (t, e, n) {
            var o = n(9504);
            t.exports = o([].slice);
          },
          7740: function (t, e, n) {
            var o = n(9297),
              r = n(5031),
              i = n(7347),
              a = n(4913);
            t.exports = function (t, e, n) {
              for (var s = r(e), u = a.f, c = i.f, l = 0; l < s.length; l++) {
                var f = s[l];
                o(t, f) || (n && o(n, f)) || u(t, f, c(e, f));
              }
            };
          },
          7750: function (t, e, n) {
            var o = n(4117),
              r = TypeError;
            t.exports = function (t) {
              if (o(t)) throw new r("Can't call method on " + t);
              return t;
            };
          },
          7751: function (t, e, n) {
            var o = n(4576),
              r = n(4901);
            t.exports = function (t, e) {
              return arguments.length < 2
                ? ((n = o[t]), r(n) ? n : void 0)
                : o[t] && o[t][e];
              var n;
            };
          },
          7764: function (t, e, n) {
            var o = n(8183).charAt,
              r = n(655),
              i = n(1181),
              a = n(1088),
              s = n(2529),
              u = 'String Iterator',
              c = i.set,
              l = i.getterFor(u);
            a(
              String,
              'String',
              function (t) {
                c(this, { type: u, string: r(t), index: 0 });
              },
              function () {
                var t,
                  e = l(this),
                  n = e.string,
                  r = e.index;
                return r >= n.length
                  ? s(void 0, !0)
                  : ((t = o(n, r)), (e.index += t.length), s(t, !1));
              },
            );
          },
          7812: function (t, e, n) {
            var o = n(6518),
              r = n(9297),
              i = n(757),
              a = n(6823),
              s = n(5745),
              u = n(1296),
              c = s('symbol-to-string-registry');
            o(
              { target: 'Symbol', stat: !0, forced: !u },
              {
                keyFor: function (t) {
                  if (!i(t)) throw new TypeError(a(t) + ' is not a symbol');
                  if (r(c, t)) return c[t];
                },
              },
            );
          },
          7829: function (t, e, n) {
            var o = n(8183).charAt;
            t.exports = function (t, e, n) {
              return e + (n ? o(t, e).length : 1);
            };
          },
          7916: function (t, e, n) {
            var o = n(6080),
              r = n(9565),
              i = n(8981),
              a = n(6319),
              s = n(4209),
              u = n(3517),
              c = n(6198),
              l = n(4659),
              f = n(81),
              d = n(851),
              p = Array;
            t.exports = function (t) {
              var e = i(t),
                n = u(this),
                h = arguments.length,
                v = h > 1 ? arguments[1] : void 0,
                y = void 0 !== v;
              y && (v = o(v, h > 2 ? arguments[2] : void 0));
              var g,
                m,
                b,
                x,
                w,
                E,
                O = d(e),
                S = 0;
              if (!O || (this === p && s(O)))
                for (g = c(e), m = n ? new this(g) : p(g); g > S; S++)
                  ((E = y ? v(e[S], S) : e[S]), l(m, S, E));
              else
                for (
                  m = n ? new this() : [], w = (x = f(e, O)).next;
                  !(b = r(w, x)).done;
                  S++
                )
                  ((E = y ? a(x, v, [b.value, S], !0) : b.value), l(m, S, E));
              return ((m.length = S), m);
            };
          },
          7979: function (t, e, n) {
            var o = n(8551);
            t.exports = function () {
              var t = o(this),
                e = '';
              return (
                t.hasIndices && (e += 'd'),
                t.global && (e += 'g'),
                t.ignoreCase && (e += 'i'),
                t.multiline && (e += 'm'),
                t.dotAll && (e += 's'),
                t.unicode && (e += 'u'),
                t.unicodeSets && (e += 'v'),
                t.sticky && (e += 'y'),
                e
              );
            };
          },
          8014: function (t, e, n) {
            var o = n(1291),
              r = Math.min;
            t.exports = function (t) {
              var e = o(t);
              return e > 0 ? r(e, 9007199254740991) : 0;
            };
          },
          8183: function (t, e, n) {
            var o = n(9504),
              r = n(1291),
              i = n(655),
              a = n(7750),
              s = o(''.charAt),
              u = o(''.charCodeAt),
              c = o(''.slice),
              l = function (t) {
                return function (e, n) {
                  var o,
                    l,
                    f = i(a(e)),
                    d = r(n),
                    p = f.length;
                  return d < 0 || d >= p
                    ? t
                      ? ''
                      : void 0
                    : (o = u(f, d)) < 55296 ||
                        o > 56319 ||
                        d + 1 === p ||
                        (l = u(f, d + 1)) < 56320 ||
                        l > 57343
                      ? t
                        ? s(f, d)
                        : o
                      : t
                        ? c(f, d, d + 2)
                        : l - 56320 + ((o - 55296) << 10) + 65536;
                };
              };
            t.exports = { codeAt: l(!1), charAt: l(!0) };
          },
          8227: function (t, e, n) {
            var o = n(4576),
              r = n(5745),
              i = n(9297),
              a = n(3392),
              s = n(4495),
              u = n(7040),
              c = o.Symbol,
              l = r('wks'),
              f = u ? c.for || c : (c && c.withoutSetter) || a;
            t.exports = function (t) {
              return (
                i(l, t) || (l[t] = s && i(c, t) ? c[t] : f('Symbol.' + t)),
                l[t]
              );
            };
          },
          8242: function (t, e, n) {
            var o = n(9565),
              r = n(7751),
              i = n(8227),
              a = n(6840);
            t.exports = function () {
              var t = r('Symbol'),
                e = t && t.prototype,
                n = e && e.valueOf,
                s = i('toPrimitive');
              e &&
                !e[s] &&
                a(
                  e,
                  s,
                  function (t) {
                    return o(n, this);
                  },
                  { arity: 1 },
                );
            };
          },
          8344: function (t, e, n) {
            n(8543);
          },
          8429: function (t, e, n) {
            var o = n(9039),
              r = n(4576).RegExp,
              i = o(function () {
                var t = r('a', 'y');
                return ((t.lastIndex = 2), null !== t.exec('abcd'));
              }),
              a =
                i ||
                o(function () {
                  return !r('a', 'y').sticky;
                }),
              s =
                i ||
                o(function () {
                  var t = r('^r', 'gy');
                  return ((t.lastIndex = 2), null !== t.exec('str'));
                });
            t.exports = { BROKEN_CARET: s, MISSED_STICKY: a, UNSUPPORTED_Y: i };
          },
          8480: function (t, e, n) {
            var o = n(1828),
              r = n(8727).concat('length', 'prototype');
            e.f =
              Object.getOwnPropertyNames ||
              function (t) {
                return o(t, r);
              };
          },
          8543: function (t, e, n) {
            var o = n(6518),
              r = n(9565),
              i = n(7476),
              a = n(3994),
              s = n(2529),
              u = n(7750),
              c = n(8014),
              l = n(655),
              f = n(8551),
              d = n(4117),
              p = n(2195),
              h = n(788),
              v = n(1034),
              y = n(5966),
              g = n(6840),
              m = n(9039),
              b = n(8227),
              x = n(2293),
              w = n(7829),
              E = n(6682),
              O = n(1181),
              S = n(6395),
              k = b('matchAll'),
              P = 'RegExp String',
              I = P + ' Iterator',
              C = O.set,
              A = O.getterFor(I),
              M = RegExp.prototype,
              D = TypeError,
              T = i(''.indexOf),
              N = i(''.matchAll),
              j =
                !!N &&
                !m(function () {
                  N('a', /./);
                }),
              R = a(
                function (t, e, n, o) {
                  C(this, {
                    type: I,
                    regexp: t,
                    string: e,
                    global: n,
                    unicode: o,
                    done: !1,
                  });
                },
                P,
                function () {
                  var t = A(this);
                  if (t.done) return s(void 0, !0);
                  var e = t.regexp,
                    n = t.string,
                    o = E(e, n);
                  return null === o
                    ? ((t.done = !0), s(void 0, !0))
                    : t.global
                      ? ('' === l(o[0]) &&
                          (e.lastIndex = w(n, c(e.lastIndex), t.unicode)),
                        s(o, !1))
                      : ((t.done = !0), s(o, !1));
                },
              ),
              L = function (t) {
                var e,
                  n,
                  o,
                  r = f(this),
                  i = l(t),
                  a = x(r, RegExp),
                  s = l(v(r));
                return (
                  (e = new a(a === RegExp ? r.source : r, s)),
                  (n = !!~T(s, 'g')),
                  (o = !!~T(s, 'u')),
                  (e.lastIndex = c(r.lastIndex)),
                  new R(e, i, n, o)
                );
              };
            (o(
              { target: 'String', proto: !0, forced: j },
              {
                matchAll: function (t) {
                  var e,
                    n,
                    o,
                    i,
                    a = u(this);
                  if (d(t)) {
                    if (j) return N(a, t);
                  } else {
                    if (h(t) && ((e = l(u(v(t)))), !~T(e, 'g')))
                      throw new D(
                        '`.matchAll` does not allow non-global regexes',
                      );
                    if (j) return N(a, t);
                    if (
                      (void 0 === (o = y(t, k)) &&
                        S &&
                        'RegExp' === p(t) &&
                        (o = L),
                      o)
                    )
                      return r(o, t, a);
                  }
                  return (
                    (n = l(a)),
                    (i = new RegExp(t, 'g')),
                    S ? r(L, i, n) : i[k](n)
                  );
                },
              },
            ),
              S || k in M || g(M, k, L));
          },
          8551: function (t, e, n) {
            var o = n(34),
              r = String,
              i = TypeError;
            t.exports = function (t) {
              if (o(t)) return t;
              throw new i(r(t) + ' is not an object');
            };
          },
          8598: function (t, e, n) {
            var o = n(6518),
              r = n(9504),
              i = n(7055),
              a = n(5397),
              s = n(4598),
              u = r([].join);
            o(
              {
                target: 'Array',
                proto: !0,
                forced: i !== Object || !s('join', ','),
              },
              {
                join: function (t) {
                  return u(a(this), void 0 === t ? ',' : t);
                },
              },
            );
          },
          8622: function (t, e, n) {
            var o = n(4576),
              r = n(4901),
              i = o.WeakMap;
            t.exports = r(i) && /native code/.test(String(i));
          },
          8686: function (t, e, n) {
            var o = n(3724),
              r = n(9039);
            t.exports =
              o &&
              r(function () {
                return (
                  42 !==
                  Object.defineProperty(function () {}, 'prototype', {
                    value: 42,
                    writable: !1,
                  }).prototype
                );
              });
          },
          8706: function (t, e, n) {
            var o = n(6518),
              r = n(9039),
              i = n(4376),
              a = n(34),
              s = n(8981),
              u = n(6198),
              c = n(6837),
              l = n(4659),
              f = n(1469),
              d = n(597),
              p = n(8227),
              h = n(9519),
              v = p('isConcatSpreadable'),
              y =
                h >= 51 ||
                !r(function () {
                  var t = [];
                  return ((t[v] = !1), t.concat()[0] !== t);
                }),
              g = function (t) {
                if (!a(t)) return !1;
                var e = t[v];
                return void 0 !== e ? !!e : i(t);
              };
            o(
              {
                target: 'Array',
                proto: !0,
                arity: 1,
                forced: !y || !d('concat'),
              },
              {
                concat: function (t) {
                  var e,
                    n,
                    o,
                    r,
                    i,
                    a = s(this),
                    d = f(a, 0),
                    p = 0;
                  for (e = -1, o = arguments.length; e < o; e++)
                    if (g((i = -1 === e ? a : arguments[e])))
                      for (r = u(i), c(p + r), n = 0; n < r; n++, p++)
                        n in i && l(d, p, i[n]);
                    else (c(p + 1), l(d, p++, i));
                  return ((d.length = p), d);
                },
              },
            );
          },
          8727: function (t) {
            t.exports = [
              'constructor',
              'hasOwnProperty',
              'isPrototypeOf',
              'propertyIsEnumerable',
              'toLocaleString',
              'toString',
              'valueOf',
            ];
          },
          8745: function (t, e, n) {
            var o = n(616),
              r = Function.prototype,
              i = r.apply,
              a = r.call;
            t.exports =
              ('object' == typeof Reflect && Reflect.apply) ||
              (o
                ? a.bind(i)
                : function () {
                    return a.apply(i, arguments);
                  });
          },
          8773: function (t, e) {
            var n = {}.propertyIsEnumerable,
              o = Object.getOwnPropertyDescriptor,
              r = o && !n.call({ 1: 2 }, 1);
            e.f = r
              ? function (t) {
                  var e = o(this, t);
                  return !!e && e.enumerable;
                }
              : n;
          },
          8781: function (t, e, n) {
            var o = n(350).PROPER,
              r = n(6840),
              i = n(8551),
              a = n(655),
              s = n(9039),
              u = n(1034),
              c = 'toString',
              l = RegExp.prototype,
              f = l[c],
              d = s(function () {
                return '/a/b' !== f.call({ source: 'a', flags: 'b' });
              }),
              p = o && f.name !== c;
            (d || p) &&
              r(
                l,
                c,
                function () {
                  var t = i(this);
                  return '/' + a(t.source) + '/' + a(u(t));
                },
                { unsafe: !0 },
              );
          },
          8814: function (t, e, n) {
            var o = n(9039),
              r = n(4576).RegExp;
            t.exports = o(function () {
              var t = r('(?<a>b)', 'g');
              return (
                'b' !== t.exec('b').groups.a || 'bc' !== 'b'.replace(t, '$<a>c')
              );
            });
          },
          8981: function (t, e, n) {
            var o = n(7750),
              r = Object;
            t.exports = function (t) {
              return r(o(t));
            };
          },
          9039: function (t) {
            t.exports = function (t) {
              try {
                return !!t();
              } catch (t) {
                return !0;
              }
            };
          },
          9085: function (t, e, n) {
            var o = n(6518),
              r = n(4213);
            o(
              {
                target: 'Object',
                stat: !0,
                arity: 2,
                forced: Object.assign !== r,
              },
              { assign: r },
            );
          },
          9167: function (t, e, n) {
            var o = n(4576);
            t.exports = o;
          },
          9213: function (t, e, n) {
            var o = n(6080),
              r = n(9504),
              i = n(7055),
              a = n(8981),
              s = n(6198),
              u = n(1469),
              c = r([].push),
              l = function (t) {
                var e = 1 === t,
                  n = 2 === t,
                  r = 3 === t,
                  l = 4 === t,
                  f = 6 === t,
                  d = 7 === t,
                  p = 5 === t || f;
                return function (h, v, y, g) {
                  for (
                    var m,
                      b,
                      x = a(h),
                      w = i(x),
                      E = s(w),
                      O = o(v, y),
                      S = 0,
                      k = g || u,
                      P = e ? k(h, E) : n || d ? k(h, 0) : void 0;
                    E > S;
                    S++
                  )
                    if ((p || S in w) && ((b = O((m = w[S]), S, x)), t))
                      if (e) P[S] = b;
                      else if (b)
                        switch (t) {
                          case 3:
                            return !0;
                          case 5:
                            return m;
                          case 6:
                            return S;
                          case 2:
                            c(P, m);
                        }
                      else
                        switch (t) {
                          case 4:
                            return !1;
                          case 7:
                            c(P, m);
                        }
                  return f ? -1 : r || l ? l : P;
                };
              };
            t.exports = {
              forEach: l(0),
              map: l(1),
              filter: l(2),
              some: l(3),
              every: l(4),
              find: l(5),
              findIndex: l(6),
              filterReject: l(7),
            };
          },
          9228: function (t, e, n) {
            n(7495);
            var o = n(9565),
              r = n(6840),
              i = n(7323),
              a = n(9039),
              s = n(8227),
              u = n(6699),
              c = s('species'),
              l = RegExp.prototype;
            t.exports = function (t, e, n, f) {
              var d = s(t),
                p = !a(function () {
                  var e = {};
                  return (
                    (e[d] = function () {
                      return 7;
                    }),
                    7 !== ''[t](e)
                  );
                }),
                h =
                  p &&
                  !a(function () {
                    var e = !1,
                      n = /a/;
                    return (
                      'split' === t &&
                        (((n = {}).constructor = {}),
                        (n.constructor[c] = function () {
                          return n;
                        }),
                        (n.flags = ''),
                        (n[d] = /./[d])),
                      (n.exec = function () {
                        return ((e = !0), null);
                      }),
                      n[d](''),
                      !e
                    );
                  });
              if (!p || !h || n) {
                var v = /./[d],
                  y = e(d, ''[t], function (t, e, n, r, a) {
                    var s = e.exec;
                    return s === i || s === l.exec
                      ? p && !a
                        ? { done: !0, value: o(v, e, n, r) }
                        : { done: !0, value: o(t, n, e, r) }
                      : { done: !1 };
                  });
                (r(String.prototype, t, y[0]), r(l, d, y[1]));
              }
              f && u(l[d], 'sham', !0);
            };
          },
          9296: function (t, e, n) {
            var o = n(4055)('span').classList,
              r = o && o.constructor && o.constructor.prototype;
            t.exports = r === Object.prototype ? void 0 : r;
          },
          9297: function (t, e, n) {
            var o = n(9504),
              r = n(8981),
              i = o({}.hasOwnProperty);
            t.exports =
              Object.hasOwn ||
              function (t, e) {
                return i(r(t), e);
              };
          },
          9306: function (t, e, n) {
            var o = n(4901),
              r = n(6823),
              i = TypeError;
            t.exports = function (t) {
              if (o(t)) return t;
              throw new i(r(t) + ' is not a function');
            };
          },
          9432: function (t, e, n) {
            var o = n(6518),
              r = n(8981),
              i = n(1072);
            o(
              {
                target: 'Object',
                stat: !0,
                forced: n(9039)(function () {
                  i(1);
                }),
              },
              {
                keys: function (t) {
                  return i(r(t));
                },
              },
            );
          },
          9433: function (t, e, n) {
            var o = n(4576),
              r = Object.defineProperty;
            t.exports = function (t, e) {
              try {
                r(o, t, { value: e, configurable: !0, writable: !0 });
              } catch (n) {
                o[t] = e;
              }
              return e;
            };
          },
          9463: function (t, e, n) {
            var o = n(6518),
              r = n(3724),
              i = n(4576),
              a = n(9504),
              s = n(9297),
              u = n(4901),
              c = n(1625),
              l = n(655),
              f = n(2106),
              d = n(7740),
              p = i.Symbol,
              h = p && p.prototype;
            if (
              r &&
              u(p) &&
              (!('description' in h) || void 0 !== p().description)
            ) {
              var v = {},
                y = function () {
                  var t =
                      arguments.length < 1 || void 0 === arguments[0]
                        ? void 0
                        : l(arguments[0]),
                    e = c(h, this) ? new p(t) : void 0 === t ? p() : p(t);
                  return ('' === t && (v[e] = !0), e);
                };
              (d(y, p), (y.prototype = h), (h.constructor = y));
              var g =
                  'Symbol(description detection)' ===
                  String(p('description detection')),
                m = a(h.valueOf),
                b = a(h.toString),
                x = /^Symbol\((.*)\)[^)]+$/,
                w = a(''.replace),
                E = a(''.slice);
              (f(h, 'description', {
                configurable: !0,
                get: function () {
                  var t = m(this);
                  if (s(v, t)) return '';
                  var e = b(t),
                    n = g ? E(e, 7, -1) : w(e, x, '$1');
                  return '' === n ? void 0 : n;
                },
              }),
                o({ global: !0, constructor: !0, forced: !0 }, { Symbol: y }));
            }
          },
          9504: function (t, e, n) {
            var o = n(616),
              r = Function.prototype,
              i = r.call,
              a = o && r.bind.bind(i, i);
            t.exports = o
              ? a
              : function (t) {
                  return function () {
                    return i.apply(t, arguments);
                  };
                };
          },
          9519: function (t, e, n) {
            var o,
              r,
              i = n(4576),
              a = n(2839),
              s = i.process,
              u = i.Deno,
              c = (s && s.versions) || (u && u.version),
              l = c && c.v8;
            (l &&
              (r = (o = l.split('.'))[0] > 0 && o[0] < 4 ? 1 : +(o[0] + o[1])),
              !r &&
                a &&
                (!(o = a.match(/Edge\/(\d+)/)) || o[1] >= 74) &&
                (o = a.match(/Chrome\/(\d+)/)) &&
                (r = +o[1]),
              (t.exports = r));
          },
          9539: function (t, e, n) {
            var o = n(9565),
              r = n(8551),
              i = n(5966);
            t.exports = function (t, e, n) {
              var a, s;
              r(t);
              try {
                if (!(a = i(t, 'return'))) {
                  if ('throw' === e) throw n;
                  return n;
                }
                a = o(a, t);
              } catch (t) {
                ((s = !0), (a = t));
              }
              if ('throw' === e) throw n;
              if (s) throw a;
              return (r(a), n);
            };
          },
          9565: function (t, e, n) {
            var o = n(616),
              r = Function.prototype.call;
            t.exports = o
              ? r.bind(r)
              : function () {
                  return r.apply(r, arguments);
                };
          },
          9572: function (t, e, n) {
            var o = n(9297),
              r = n(6840),
              i = n(3640),
              a = n(8227)('toPrimitive'),
              s = Date.prototype;
            o(s, a) || r(s, a, i);
          },
          9617: function (t, e, n) {
            var o = n(5397),
              r = n(5610),
              i = n(6198),
              a = function (t) {
                return function (e, n, a) {
                  var s = o(e),
                    u = i(s);
                  if (0 === u) return !t && -1;
                  var c,
                    l = r(a, u);
                  if (t && n != n) {
                    for (; u > l; ) if ((c = s[l++]) != c) return !0;
                  } else
                    for (; u > l; l++)
                      if ((t || l in s) && s[l] === n) return t || l || 0;
                  return !t && -1;
                };
              };
            t.exports = { includes: a(!0), indexOf: a(!1) };
          },
          9773: function (t, e, n) {
            var o = n(6518),
              r = n(4495),
              i = n(9039),
              a = n(3717),
              s = n(8981);
            o(
              {
                target: 'Object',
                stat: !0,
                forced:
                  !r ||
                  i(function () {
                    a.f(1);
                  }),
              },
              {
                getOwnPropertySymbols: function (t) {
                  var e = a.f;
                  return e ? e(s(t)) : [];
                },
              },
            );
          },
        },
        e = {};
      function n(o) {
        var r = e[o];
        if (void 0 !== r) return r.exports;
        var i = (e[o] = { exports: {} });
        return (t[o].call(i.exports, i, i.exports, n), i.exports);
      }
      ((n.d = function (t, e) {
        for (var o in e)
          n.o(e, o) &&
            !n.o(t, o) &&
            Object.defineProperty(t, o, { enumerable: !0, get: e[o] });
      }),
        (n.g = (function () {
          if ('object' == typeof globalThis) return globalThis;
          try {
            return this || new Function('return this')();
          } catch (t) {
            if ('object' == typeof window) return window;
          }
        })()),
        (n.o = function (t, e) {
          return Object.prototype.hasOwnProperty.call(t, e);
        }),
        (n.r = function (t) {
          ('undefined' != typeof Symbol &&
            Symbol.toStringTag &&
            Object.defineProperty(t, Symbol.toStringTag, { value: 'Module' }),
            Object.defineProperty(t, '__esModule', { value: !0 }));
        }));
      var o = {};
      (n.r(o),
        n.d(o, {
          SimpleKeyboard: function () {
            return T;
          },
          default: function () {
            return N;
          },
        }));
      (n(5276),
        n(8598),
        n(4782),
        n(4554),
        n(2010),
        n(7427),
        n(6099),
        n(7495),
        n(8781),
        n(5440),
        n(744),
        n(2762));
      ('undefined' == typeof Element ||
        'remove' in Element.prototype ||
        (Element.prototype.remove = function () {
          this.parentNode && this.parentNode.removeChild(this);
        }),
        'undefined' != typeof self &&
          'document' in self &&
          ((!('classList' in document.createElement('_')) ||
            (document.createElementNS &&
              !(
                'classList' in
                document.createElementNS('http://www.w3.org/2000/svg', 'g')
              ))) &&
            (function (t) {
              if ('Element' in t) {
                var e = 'classList',
                  n = 'prototype',
                  o = t.Element[n],
                  r = Object,
                  i =
                    String[n].trim ||
                    function () {
                      return this.replace(/^\s+|\s+$/g, '');
                    },
                  a =
                    Array[n].indexOf ||
                    function (t) {
                      for (var e = 0, n = this.length; e < n; e++)
                        if (e in this && this[e] === t) return e;
                      return -1;
                    },
                  s = function (t, e) {
                    ((this.name = t),
                      (this.code = DOMException[t]),
                      (this.message = e));
                  },
                  u = function (t, e) {
                    if ('' === e)
                      throw new s('SYNTAX_ERR', 'The token must not be empty.');
                    if (/\s/.test(e))
                      throw new s(
                        'INVALID_CHARACTER_ERR',
                        'The token must not contain space characters.',
                      );
                    return a.call(t, e);
                  },
                  c = function (t) {
                    for (
                      var e = i.call(t.getAttribute('class') || ''),
                        n = e ? e.split(/\s+/) : [],
                        o = 0,
                        r = n.length;
                      o < r;
                      o++
                    )
                      this.push(n[o]);
                    this._updateClassName = function () {
                      t.setAttribute('class', this.toString());
                    };
                  },
                  l = (c[n] = []),
                  f = function () {
                    return new c(this);
                  };
                if (
                  ((s[n] = Error[n]),
                  (l.item = function (t) {
                    return this[t] || null;
                  }),
                  (l.contains = function (t) {
                    return ~u(this, t + '');
                  }),
                  (l.add = function () {
                    var t,
                      e = arguments,
                      n = 0,
                      o = e.length,
                      r = !1;
                    do {
                      ~u(this, (t = e[n] + '')) || (this.push(t), (r = !0));
                    } while (++n < o);
                    r && this._updateClassName();
                  }),
                  (l.remove = function () {
                    var t,
                      e,
                      n = arguments,
                      o = 0,
                      r = n.length,
                      i = !1;
                    do {
                      for (e = u(this, (t = n[o] + '')); ~e; )
                        (this.splice(e, 1), (i = !0), (e = u(this, t)));
                    } while (++o < r);
                    i && this._updateClassName();
                  }),
                  (l.toggle = function (t, e) {
                    var n = this.contains(t),
                      o = n ? !0 !== e && 'remove' : !1 !== e && 'add';
                    return (o && this[o](t), !0 === e || !1 === e ? e : !n);
                  }),
                  (l.replace = function (t, e) {
                    var n = u(t + '');
                    ~n && (this.splice(n, 1, e), this._updateClassName());
                  }),
                  (l.toString = function () {
                    return this.join(' ');
                  }),
                  r.defineProperty)
                ) {
                  var d = { get: f, enumerable: !0, configurable: !0 };
                  try {
                    r.defineProperty(o, e, d);
                  } catch (t) {
                    (void 0 !== t.number && -2146823252 !== t.number) ||
                      ((d.enumerable = !1), r.defineProperty(o, e, d));
                  }
                } else r[n].__defineGetter__ && o.__defineGetter__(e, f);
              }
            })(self),
          (function () {
            var t = document.createElement('_');
            if ((t.classList.add('c1', 'c2'), !t.classList.contains('c2'))) {
              var e = function (t) {
                var e = DOMTokenList.prototype[t];
                DOMTokenList.prototype[t] = function (t) {
                  var n,
                    o = arguments.length;
                  for (n = 0; n < o; n++) ((t = arguments[n]), e.call(this, t));
                };
              };
              (e('add'), e('remove'));
            }
            if ((t.classList.toggle('c3', !1), t.classList.contains('c3'))) {
              var n = DOMTokenList.prototype.toggle;
              DOMTokenList.prototype.toggle = function (t, e) {
                return 1 in arguments && !this.contains(t) == !e
                  ? e
                  : n.call(this, t);
              };
            }
            ('replace' in document.createElement('_').classList ||
              (DOMTokenList.prototype.replace = function (t, e) {
                var n = this.toString().split(' '),
                  o = n.indexOf(t + '');
                ~o &&
                  ((n = n.slice(o)),
                  this.remove.apply(this, n),
                  this.add(e),
                  this.add.apply(this, n.slice(1)));
              }),
              (t = null));
          })()));
      (n(2675),
        n(9463),
        n(2259),
        n(5700),
        n(8706),
        n(2008),
        n(3418),
        n(4423),
        n(3792),
        n(2062),
        n(6910),
        n(739),
        n(9572),
        n(2892),
        n(9085),
        n(3851),
        n(1278),
        n(9432),
        n(4864),
        n(1699),
        n(7764),
        n(8344),
        n(3500),
        n(2953),
        n(2712),
        n(2637),
        n(1480),
        n(825),
        n(1761));
      function r(t) {
        return (
          (function (t) {
            if (Array.isArray(t)) return a(t);
          })(t) ||
          (function (t) {
            if (
              ('undefined' != typeof Symbol && null != t[Symbol.iterator]) ||
              null != t['@@iterator']
            )
              return Array.from(t);
          })(t) ||
          i(t) ||
          (function () {
            throw new TypeError(
              'Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.',
            );
          })()
        );
      }
      function i(t, e) {
        if (t) {
          if ('string' == typeof t) return a(t, e);
          var n = {}.toString.call(t).slice(8, -1);
          return (
            'Object' === n && t.constructor && (n = t.constructor.name),
            'Map' === n || 'Set' === n
              ? Array.from(t)
              : 'Arguments' === n ||
                  /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
                ? a(t, e)
                : void 0
          );
        }
      }
      function a(t, e) {
        (null == e || e > t.length) && (e = t.length);
        for (var n = 0, o = Array(e); n < e; n++) o[n] = t[n];
        return o;
      }
      function s(t) {
        return (
          (s =
            'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
              ? function (t) {
                  return typeof t;
                }
              : function (t) {
                  return t &&
                    'function' == typeof Symbol &&
                    t.constructor === Symbol &&
                    t !== Symbol.prototype
                    ? 'symbol'
                    : typeof t;
                }),
          s(t)
        );
      }
      function u(t, e) {
        for (var n = 0; n < e.length; n++) {
          var o = e[n];
          ((o.enumerable = o.enumerable || !1),
            (o.configurable = !0),
            'value' in o && (o.writable = !0),
            Object.defineProperty(t, l(o.key), o));
        }
      }
      function c(t, e, n) {
        return (
          (e = l(e)) in t
            ? Object.defineProperty(t, e, {
                value: n,
                enumerable: !0,
                configurable: !0,
                writable: !0,
              })
            : (t[e] = n),
          t
        );
      }
      function l(t) {
        var e = (function (t, e) {
          if ('object' != s(t) || !t) return t;
          var n = t[Symbol.toPrimitive];
          if (void 0 !== n) {
            var o = n.call(t, e || 'default');
            if ('object' != s(o)) return o;
            throw new TypeError('@@toPrimitive must return a primitive value.');
          }
          return ('string' === e ? String : Number)(t);
        })(t, 'string');
        return 'symbol' == s(e) ? e : e + '';
      }
      var f = (function () {
        return (
          (t = function t(e) {
            var n = e.getOptions,
              o = e.getCaretPosition,
              r = e.getCaretPositionEnd,
              i = e.dispatch;
            (!(function (t, e) {
              if (!(t instanceof e))
                throw new TypeError('Cannot call a class as a function');
            })(this, t),
              c(this, 'getOptions', void 0),
              c(this, 'getCaretPosition', void 0),
              c(this, 'getCaretPositionEnd', void 0),
              c(this, 'dispatch', void 0),
              c(this, 'maxLengthReached', void 0),
              c(this, 'isStandardButton', function (t) {
                return t && !('{' === t[0] && '}' === t[t.length - 1]);
              }),
              (this.getOptions = n),
              (this.getCaretPosition = o),
              (this.getCaretPositionEnd = r),
              (this.dispatch = i),
              t.bindMethods(t, this));
          }),
          (e = [
            {
              key: 'getButtonType',
              value: function (t) {
                return t.includes('{') && t.includes('}') && '{//}' !== t
                  ? 'functionBtn'
                  : 'standardBtn';
              },
            },
            {
              key: 'getButtonClass',
              value: function (t) {
                var e = this.getButtonType(t),
                  n = t.replace('{', '').replace('}', ''),
                  o = '';
                return (
                  'standardBtn' !== e && (o = ' hg-button-'.concat(n)),
                  'hg-'.concat(e).concat(o)
                );
              },
            },
            {
              key: 'getDefaultDiplay',
              value: function () {
                return {
                  '{bksp}': 'backspace',
                  '{backspace}': 'backspace',
                  '{enter}': '< enter',
                  '{shift}': 'shift',
                  '{shiftleft}': 'shift',
                  '{shiftright}': 'shift',
                  '{alt}': 'alt',
                  '{s}': 'shift',
                  '{tab}': 'tab',
                  '{lock}': 'caps',
                  '{capslock}': 'caps',
                  '{accept}': 'Submit',
                  '{space}': ' ',
                  '{//}': ' ',
                  '{esc}': 'esc',
                  '{escape}': 'esc',
                  '{f1}': 'f1',
                  '{f2}': 'f2',
                  '{f3}': 'f3',
                  '{f4}': 'f4',
                  '{f5}': 'f5',
                  '{f6}': 'f6',
                  '{f7}': 'f7',
                  '{f8}': 'f8',
                  '{f9}': 'f9',
                  '{f10}': 'f10',
                  '{f11}': 'f11',
                  '{f12}': 'f12',
                  '{numpaddivide}': '/',
                  '{numlock}': 'lock',
                  '{arrowup}': '↑',
                  '{arrowleft}': '←',
                  '{arrowdown}': '↓',
                  '{arrowright}': '→',
                  '{prtscr}': 'print',
                  '{scrolllock}': 'scroll',
                  '{pause}': 'pause',
                  '{insert}': 'ins',
                  '{home}': 'home',
                  '{pageup}': 'up',
                  '{delete}': 'del',
                  '{forwarddelete}': 'del',
                  '{end}': 'end',
                  '{pagedown}': 'down',
                  '{numpadmultiply}': '*',
                  '{numpadsubtract}': '-',
                  '{numpadadd}': '+',
                  '{numpadenter}': 'enter',
                  '{period}': '.',
                  '{numpaddecimal}': '.',
                  '{numpad0}': '0',
                  '{numpad1}': '1',
                  '{numpad2}': '2',
                  '{numpad3}': '3',
                  '{numpad4}': '4',
                  '{numpad5}': '5',
                  '{numpad6}': '6',
                  '{numpad7}': '7',
                  '{numpad8}': '8',
                  '{numpad9}': '9',
                };
              },
            },
            {
              key: 'getButtonDisplayName',
              value: function (t, e) {
                return (
                  (e =
                    arguments.length > 2 &&
                    void 0 !== arguments[2] &&
                    arguments[2]
                      ? Object.assign({}, this.getDefaultDiplay(), e)
                      : e || this.getDefaultDiplay())[t] || t
                );
              },
            },
            {
              key: 'getUpdatedInput',
              value: function (t, e, n) {
                var o =
                    arguments.length > 3 && void 0 !== arguments[3]
                      ? arguments[3]
                      : n,
                  r =
                    arguments.length > 4 &&
                    void 0 !== arguments[4] &&
                    arguments[4],
                  i = this.getOptions(),
                  a = [n, o, r],
                  s = e;
                return (
                  ('{bksp}' === t || '{backspace}' === t) && s.length > 0
                    ? (s = this.removeAt.apply(this, [s].concat(a)))
                    : ('{delete}' === t || '{forwarddelete}' === t) &&
                        s.length > 0
                      ? (s = this.removeForwardsAt.apply(this, [s].concat(a)))
                      : '{space}' === t
                        ? (s = this.addStringAt.apply(this, [s, ' '].concat(a)))
                        : '{tab}' !== t ||
                            ('boolean' == typeof i.tabCharOnTab &&
                              !1 === i.tabCharOnTab)
                          ? ('{enter}' !== t && '{numpadenter}' !== t) ||
                            !i.newLineOnEnter
                            ? t.includes('numpad') &&
                              Number.isInteger(Number(t[t.length - 2]))
                              ? (s = this.addStringAt.apply(
                                  this,
                                  [s, t[t.length - 2]].concat(a),
                                ))
                              : '{numpaddivide}' === t
                                ? (s = this.addStringAt.apply(
                                    this,
                                    [s, '/'].concat(a),
                                  ))
                                : '{numpadmultiply}' === t
                                  ? (s = this.addStringAt.apply(
                                      this,
                                      [s, '*'].concat(a),
                                    ))
                                  : '{numpadsubtract}' === t
                                    ? (s = this.addStringAt.apply(
                                        this,
                                        [s, '-'].concat(a),
                                      ))
                                    : '{numpadadd}' === t
                                      ? (s = this.addStringAt.apply(
                                          this,
                                          [s, '+'].concat(a),
                                        ))
                                      : '{numpaddecimal}' === t
                                        ? (s = this.addStringAt.apply(
                                            this,
                                            [s, '.'].concat(a),
                                          ))
                                        : '{' === t || '}' === t
                                          ? (s = this.addStringAt.apply(
                                              this,
                                              [s, t].concat(a),
                                            ))
                                          : t.includes('{') ||
                                            t.includes('}') ||
                                            (s = this.addStringAt.apply(
                                              this,
                                              [s, t].concat(a),
                                            ))
                            : (s = this.addStringAt.apply(
                                this,
                                [s, '\n'].concat(a),
                              ))
                          : (s = this.addStringAt.apply(
                              this,
                              [s, '\t'].concat(a),
                            )),
                  i.debug && console.log('Input will be: ' + s),
                  s
                );
              },
            },
            {
              key: 'updateCaretPos',
              value: function (t) {
                var e =
                    arguments.length > 1 &&
                    void 0 !== arguments[1] &&
                    arguments[1],
                  n = this.updateCaretPosAction(t, e);
                this.dispatch(function (t) {
                  t.setCaretPosition(n);
                });
              },
            },
            {
              key: 'updateCaretPosAction',
              value: function (t) {
                var e =
                    arguments.length > 1 &&
                    void 0 !== arguments[1] &&
                    arguments[1],
                  n = this.getOptions(),
                  o = this.getCaretPosition();
                return (
                  null != o && (e ? o > 0 && (o -= t) : (o += t)),
                  n.debug && console.log('Caret at:', o),
                  o
                );
              },
            },
            {
              key: 'addStringAt',
              value: function (t, e) {
                var n,
                  o =
                    arguments.length > 2 && void 0 !== arguments[2]
                      ? arguments[2]
                      : t.length,
                  r =
                    arguments.length > 3 && void 0 !== arguments[3]
                      ? arguments[3]
                      : t.length,
                  i =
                    arguments.length > 4 &&
                    void 0 !== arguments[4] &&
                    arguments[4];
                return (
                  o || 0 === o
                    ? ((n = [t.slice(0, o), e, t.slice(r)].join('')),
                      this.isMaxLengthReached() ||
                        (i && this.updateCaretPos(e.length)))
                    : (n = t + e),
                  n
                );
              },
            },
            {
              key: 'removeAt',
              value: function (t) {
                var e,
                  n =
                    arguments.length > 1 && void 0 !== arguments[1]
                      ? arguments[1]
                      : t.length,
                  o =
                    arguments.length > 2 && void 0 !== arguments[2]
                      ? arguments[2]
                      : t.length,
                  r =
                    arguments.length > 3 &&
                    void 0 !== arguments[3] &&
                    arguments[3];
                if (0 === n && 0 === o) return t;
                if (n === o) {
                  var i = /([\uD800-\uDBFF][\uDC00-\uDFFF])/g;
                  n && n >= 0
                    ? t.substring(n - 2, n).match(i)
                      ? ((e = t.substr(0, n - 2) + t.substr(n)),
                        r && this.updateCaretPos(2, !0))
                      : ((e = t.substr(0, n - 1) + t.substr(n)),
                        r && this.updateCaretPos(1, !0))
                    : t.slice(-2).match(i)
                      ? ((e = t.slice(0, -2)), r && this.updateCaretPos(2, !0))
                      : ((e = t.slice(0, -1)), r && this.updateCaretPos(1, !0));
                } else
                  ((e = t.slice(0, n) + t.slice(o)),
                    r &&
                      this.dispatch(function (t) {
                        t.setCaretPosition(n);
                      }));
                return e;
              },
            },
            {
              key: 'removeForwardsAt',
              value: function (t) {
                var e,
                  n =
                    arguments.length > 1 && void 0 !== arguments[1]
                      ? arguments[1]
                      : t.length,
                  o =
                    arguments.length > 2 && void 0 !== arguments[2]
                      ? arguments[2]
                      : t.length,
                  r =
                    arguments.length > 3 &&
                    void 0 !== arguments[3] &&
                    arguments[3];
                return null != t && t.length && null !== n
                  ? (n === o
                      ? (e = t
                          .substring(n, n + 2)
                          .match(/([\uD800-\uDBFF][\uDC00-\uDFFF])/g)
                          ? t.substr(0, n) + t.substr(n + 2)
                          : t.substr(0, n) + t.substr(n + 1))
                      : ((e = t.slice(0, n) + t.slice(o)),
                        r &&
                          this.dispatch(function (t) {
                            t.setCaretPosition(n);
                          })),
                    e)
                  : t;
              },
            },
            {
              key: 'handleMaxLength',
              value: function (t, e) {
                var n = this.getOptions(),
                  o = n.maxLength,
                  r = t[n.inputName || 'default'],
                  i = e.length - 1 >= o;
                if (e.length <= r.length) return !1;
                if (Number.isInteger(o))
                  return (
                    n.debug && console.log('maxLength (num) reached:', i),
                    i
                      ? ((this.maxLengthReached = !0), !0)
                      : ((this.maxLengthReached = !1), !1)
                  );
                if ('object' === s(o)) {
                  var a = e.length - 1 >= o[n.inputName || 'default'];
                  return (
                    n.debug && console.log('maxLength (obj) reached:', a),
                    a
                      ? ((this.maxLengthReached = !0), !0)
                      : ((this.maxLengthReached = !1), !1)
                  );
                }
              },
            },
            {
              key: 'isMaxLengthReached',
              value: function () {
                return Boolean(this.maxLengthReached);
              },
            },
            {
              key: 'isTouchDevice',
              value: function () {
                return 'ontouchstart' in window || navigator.maxTouchPoints;
              },
            },
            {
              key: 'pointerEventsSupported',
              value: function () {
                return !!window.PointerEvent;
              },
            },
            {
              key: 'camelCase',
              value: function (t) {
                return t
                  ? t
                      .toLowerCase()
                      .trim()
                      .split(/[.\-_\s]/g)
                      .reduce(function (t, e) {
                        return e.length
                          ? t + e[0].toUpperCase() + e.slice(1)
                          : t;
                      })
                  : '';
              },
            },
            {
              key: 'chunkArray',
              value: function (t, e) {
                return r(Array(Math.ceil(t.length / e))).map(function (n, o) {
                  return t.slice(e * o, e + e * o);
                });
              },
            },
            {
              key: 'escapeRegex',
              value: function (t) {
                return t.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
              },
            },
            {
              key: 'getRtlOffset',
              value: function (t, e) {
                var n = t,
                  o = e.indexOf('‫');
                return (
                  o < t && -1 != o && n--,
                  e.indexOf('‬') < t && -1 != o && n--,
                  n < 0 ? 0 : n
                );
              },
            },
            {
              key: 'isConstructor',
              value: function (t) {
                try {
                  Reflect.construct(String, [], t);
                } catch (t) {
                  return !1;
                }
                return !0;
              },
            },
          ]),
          (n = [
            {
              key: 'bindMethods',
              value: function (t, e) {
                var n,
                  o = (function (t, e) {
                    var n =
                      ('undefined' != typeof Symbol && t[Symbol.iterator]) ||
                      t['@@iterator'];
                    if (!n) {
                      if (
                        Array.isArray(t) ||
                        (n = i(t)) ||
                        (e && t && 'number' == typeof t.length)
                      ) {
                        n && (t = n);
                        var o = 0,
                          r = function () {};
                        return {
                          s: r,
                          n: function () {
                            return o >= t.length
                              ? { done: !0 }
                              : { done: !1, value: t[o++] };
                          },
                          e: function (t) {
                            throw t;
                          },
                          f: r,
                        };
                      }
                      throw new TypeError(
                        'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.',
                      );
                    }
                    var a,
                      s = !0,
                      u = !1;
                    return {
                      s: function () {
                        n = n.call(t);
                      },
                      n: function () {
                        var t = n.next();
                        return ((s = t.done), t);
                      },
                      e: function (t) {
                        ((u = !0), (a = t));
                      },
                      f: function () {
                        try {
                          s || null == n.return || n.return();
                        } finally {
                          if (u) throw a;
                        }
                      },
                    };
                  })(Object.getOwnPropertyNames(t.prototype));
                try {
                  for (o.s(); !(n = o.n()).done; ) {
                    var r = n.value;
                    'constructor' === r ||
                      'bindMethods' === r ||
                      (e[r] = e[r].bind(e));
                  }
                } catch (t) {
                  o.e(t);
                } finally {
                  o.f();
                }
              },
            },
          ]),
          e && u(t.prototype, e),
          n && u(t, n),
          Object.defineProperty(t, 'prototype', { writable: !1 }),
          t
        );
        var t, e, n;
      })();
      c(f, 'noop', function () {});
      var d = f;
      function p(t) {
        return (
          (p =
            'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
              ? function (t) {
                  return typeof t;
                }
              : function (t) {
                  return t &&
                    'function' == typeof Symbol &&
                    t.constructor === Symbol &&
                    t !== Symbol.prototype
                    ? 'symbol'
                    : typeof t;
                }),
          p(t)
        );
      }
      function h(t, e) {
        for (var n = 0; n < e.length; n++) {
          var o = e[n];
          ((o.enumerable = o.enumerable || !1),
            (o.configurable = !0),
            'value' in o && (o.writable = !0),
            Object.defineProperty(t, y(o.key), o));
        }
      }
      function v(t, e, n) {
        return (
          (e = y(e)) in t
            ? Object.defineProperty(t, e, {
                value: n,
                enumerable: !0,
                configurable: !0,
                writable: !0,
              })
            : (t[e] = n),
          t
        );
      }
      function y(t) {
        var e = (function (t, e) {
          if ('object' != p(t) || !t) return t;
          var n = t[Symbol.toPrimitive];
          if (void 0 !== n) {
            var o = n.call(t, e || 'default');
            if ('object' != p(o)) return o;
            throw new TypeError('@@toPrimitive must return a primitive value.');
          }
          return ('string' === e ? String : Number)(t);
        })(t, 'string');
        return 'symbol' == p(e) ? e : e + '';
      }
      var g = (function () {
        return (
          (t = function t(e) {
            var n = this,
              o = e.dispatch,
              r = e.getOptions;
            (!(function (t, e) {
              if (!(t instanceof e))
                throw new TypeError('Cannot call a class as a function');
            })(this, t),
              v(this, 'getOptions', void 0),
              v(this, 'dispatch', void 0),
              v(this, 'isModifierKey', function (t) {
                return (
                  t.altKey ||
                  t.ctrlKey ||
                  t.shiftKey ||
                  [
                    'Tab',
                    'CapsLock',
                    'Esc',
                    'ArrowUp',
                    'ArrowDown',
                    'ArrowLeft',
                    'ArrowRight',
                  ].includes(
                    t.code ||
                      t.key ||
                      n.keyCodeToKey(null == t ? void 0 : t.keyCode),
                  )
                );
              }),
              (this.dispatch = o),
              (this.getOptions = r),
              d.bindMethods(t, this));
          }),
          (e = [
            {
              key: 'handleHighlightKeyDown',
              value: function (t) {
                var e = this.getOptions();
                e.physicalKeyboardHighlightPreventDefault &&
                  this.isModifierKey(t) &&
                  (t.preventDefault(), t.stopImmediatePropagation());
                var n = this.getSimpleKeyboardLayoutKey(t);
                this.dispatch(function (o) {
                  var r,
                    i,
                    a = o.getButtonElement(n),
                    s = o.getButtonElement('{'.concat(n, '}'));
                  if (a) ((r = a), (i = n));
                  else {
                    if (!s) return;
                    ((r = s), (i = '{'.concat(n, '}')));
                  }
                  var u,
                    c,
                    l,
                    f,
                    d = function (t) {
                      ((t.style.background =
                        e.physicalKeyboardHighlightBgColor || '#dadce4'),
                        (t.style.color =
                          e.physicalKeyboardHighlightTextColor || 'black'));
                    };
                  if (r)
                    if (Array.isArray(r)) {
                      if (
                        (r.forEach(function (t) {
                          return d(t);
                        }),
                        e.physicalKeyboardHighlightPress)
                      )
                        if (e.physicalKeyboardHighlightPressUsePointerEvents)
                          null === (u = r[0]) ||
                            void 0 === u ||
                            null === (c = u.onpointerdown) ||
                            void 0 === c ||
                            c.call(u, t);
                        else if (e.physicalKeyboardHighlightPressUseClick) {
                          var p;
                          null === (p = r[0]) || void 0 === p || p.click();
                        } else o.handleButtonClicked(i, t);
                    } else
                      (d(r),
                        e.physicalKeyboardHighlightPress &&
                          (e.physicalKeyboardHighlightPressUsePointerEvents
                            ? null === (l = r) ||
                              void 0 === l ||
                              null === (f = l.onpointerdown) ||
                              void 0 === f ||
                              f.call(l, t)
                            : e.physicalKeyboardHighlightPressUseClick
                              ? r.click()
                              : o.handleButtonClicked(i, t)));
                });
              },
            },
            {
              key: 'handleHighlightKeyUp',
              value: function (t) {
                var e = this.getOptions();
                e.physicalKeyboardHighlightPreventDefault &&
                  this.isModifierKey(t) &&
                  (t.preventDefault(), t.stopImmediatePropagation());
                var n = this.getSimpleKeyboardLayoutKey(t);
                this.dispatch(function (o) {
                  var r,
                    i,
                    a,
                    s =
                      o.getButtonElement(n) ||
                      o.getButtonElement('{'.concat(n, '}')),
                    u = function (t) {
                      t.removeAttribute && t.removeAttribute('style');
                    };
                  s &&
                    (Array.isArray(s)
                      ? (s.forEach(function (t) {
                          return u(t);
                        }),
                        e.physicalKeyboardHighlightPressUsePointerEvents &&
                          (null === (r = s[0]) ||
                            void 0 === r ||
                            null === (i = r.onpointerup) ||
                            void 0 === i ||
                            i.call(r, t)))
                      : (u(s),
                        e.physicalKeyboardHighlightPressUsePointerEvents &&
                          (null == s ||
                            null === (a = s.onpointerup) ||
                            void 0 === a ||
                            a.call(s, t))));
                });
              },
            },
            {
              key: 'getSimpleKeyboardLayoutKey',
              value: function (t) {
                var e,
                  n = '',
                  o =
                    t.code ||
                    t.key ||
                    this.keyCodeToKey(null == t ? void 0 : t.keyCode);
                return (n =
                  (null != o && o.includes('Numpad')) ||
                  (null != o && o.includes('Shift')) ||
                  (null != o && o.includes('Space')) ||
                  (null != o && o.includes('Backspace')) ||
                  (null != o && o.includes('Control')) ||
                  (null != o && o.includes('Alt')) ||
                  (null != o && o.includes('Meta'))
                    ? t.code || ''
                    : t.key ||
                      this.keyCodeToKey(null == t ? void 0 : t.keyCode) ||
                      '').length > 1
                  ? null === (e = n) || void 0 === e
                    ? void 0
                    : e.toLowerCase()
                  : n;
              },
            },
            {
              key: 'keyCodeToKey',
              value: function (t) {
                return (
                  {
                    8: 'Backspace',
                    9: 'Tab',
                    13: 'Enter',
                    16: 'Shift',
                    17: 'Ctrl',
                    18: 'Alt',
                    19: 'Pause',
                    20: 'CapsLock',
                    27: 'Esc',
                    32: 'Space',
                    33: 'PageUp',
                    34: 'PageDown',
                    35: 'End',
                    36: 'Home',
                    37: 'ArrowLeft',
                    38: 'ArrowUp',
                    39: 'ArrowRight',
                    40: 'ArrowDown',
                    45: 'Insert',
                    46: 'Delete',
                    48: '0',
                    49: '1',
                    50: '2',
                    51: '3',
                    52: '4',
                    53: '5',
                    54: '6',
                    55: '7',
                    56: '8',
                    57: '9',
                    65: 'A',
                    66: 'B',
                    67: 'C',
                    68: 'D',
                    69: 'E',
                    70: 'F',
                    71: 'G',
                    72: 'H',
                    73: 'I',
                    74: 'J',
                    75: 'K',
                    76: 'L',
                    77: 'M',
                    78: 'N',
                    79: 'O',
                    80: 'P',
                    81: 'Q',
                    82: 'R',
                    83: 'S',
                    84: 'T',
                    85: 'U',
                    86: 'V',
                    87: 'W',
                    88: 'X',
                    89: 'Y',
                    90: 'Z',
                    91: 'Meta',
                    96: 'Numpad0',
                    97: 'Numpad1',
                    98: 'Numpad2',
                    99: 'Numpad3',
                    100: 'Numpad4',
                    101: 'Numpad5',
                    102: 'Numpad6',
                    103: 'Numpad7',
                    104: 'Numpad8',
                    105: 'Numpad9',
                    106: 'NumpadMultiply',
                    107: 'NumpadAdd',
                    109: 'NumpadSubtract',
                    110: 'NumpadDecimal',
                    111: 'NumpadDivide',
                    112: 'F1',
                    113: 'F2',
                    114: 'F3',
                    115: 'F4',
                    116: 'F5',
                    117: 'F6',
                    118: 'F7',
                    119: 'F8',
                    120: 'F9',
                    121: 'F10',
                    122: 'F11',
                    123: 'F12',
                    144: 'NumLock',
                    145: 'ScrollLock',
                    186: ';',
                    187: '=',
                    188: ',',
                    189: '-',
                    190: '.',
                    191: '/',
                    192: '`',
                    219: '[',
                    220: '\\',
                    221: ']',
                    222: "'",
                  }[t] || ''
                );
              },
            },
          ]),
          e && h(t.prototype, e),
          n && h(t, n),
          Object.defineProperty(t, 'prototype', { writable: !1 }),
          t
        );
        var t, e, n;
      })();
      function m(t) {
        return (
          (m =
            'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
              ? function (t) {
                  return typeof t;
                }
              : function (t) {
                  return t &&
                    'function' == typeof Symbol &&
                    t.constructor === Symbol &&
                    t !== Symbol.prototype
                    ? 'symbol'
                    : typeof t;
                }),
          m(t)
        );
      }
      function b(t, e) {
        for (var n = 0; n < e.length; n++) {
          var o = e[n];
          ((o.enumerable = o.enumerable || !1),
            (o.configurable = !0),
            'value' in o && (o.writable = !0),
            Object.defineProperty(t, w(o.key), o));
        }
      }
      function x(t, e, n) {
        return (
          (e = w(e)) in t
            ? Object.defineProperty(t, e, {
                value: n,
                enumerable: !0,
                configurable: !0,
                writable: !0,
              })
            : (t[e] = n),
          t
        );
      }
      function w(t) {
        var e = (function (t, e) {
          if ('object' != m(t) || !t) return t;
          var n = t[Symbol.toPrimitive];
          if (void 0 !== n) {
            var o = n.call(t, e || 'default');
            if ('object' != m(o)) return o;
            throw new TypeError('@@toPrimitive must return a primitive value.');
          }
          return ('string' === e ? String : Number)(t);
        })(t, 'string');
        return 'symbol' == m(e) ? e : e + '';
      }
      var E = (function () {
          return (
            (t = function t(e) {
              var n = e.utilities,
                o = e.options;
              (!(function (t, e) {
                if (!(t instanceof e))
                  throw new TypeError('Cannot call a class as a function');
              })(this, t),
                x(this, 'utilities', void 0),
                x(this, 'options', void 0),
                x(this, 'candidateBoxElement', void 0),
                x(this, 'pageIndex', 0),
                x(this, 'pageSize', void 0),
                (this.utilities = n),
                (this.options = o),
                d.bindMethods(t, this),
                (this.pageSize =
                  this.utilities.getOptions().layoutCandidatesPageSize || 5));
            }),
            (e = [
              {
                key: 'destroy',
                value: function () {
                  this.candidateBoxElement &&
                    (this.candidateBoxElement.remove(), (this.pageIndex = 0));
                },
              },
              {
                key: 'show',
                value: function (t) {
                  var e = this,
                    n = t.candidateValue,
                    o = t.targetElement,
                    r = t.onSelect;
                  if (n && n.length) {
                    var i = this.utilities.chunkArray(
                      n.split(' '),
                      this.pageSize,
                    );
                    this.renderPage({
                      candidateListPages: i,
                      targetElement: o,
                      pageIndex: this.pageIndex,
                      nbPages: i.length,
                      onItemSelected: function (t, n) {
                        (r(t, n), e.destroy());
                      },
                    });
                  }
                },
              },
              {
                key: 'renderPage',
                value: function (t) {
                  var e,
                    n = this,
                    o = t.candidateListPages,
                    r = t.targetElement,
                    i = t.pageIndex,
                    a = t.nbPages,
                    s = t.onItemSelected;
                  (null === (e = this.candidateBoxElement) ||
                    void 0 === e ||
                    e.remove(),
                    (this.candidateBoxElement = document.createElement('div')),
                    (this.candidateBoxElement.className = 'hg-candidate-box'));
                  var u = document.createElement('ul');
                  ((u.className = 'hg-candidate-box-list'),
                    o[i].forEach(function (t) {
                      var e,
                        o = document.createElement('li'),
                        r = function () {
                          var t = new (
                            n.options.useTouchEvents ? TouchEvent : MouseEvent
                          )('click');
                          return (
                            Object.defineProperty(t, 'target', { value: o }),
                            t
                          );
                        };
                      ((o.className = 'hg-candidate-box-list-item'),
                        (o.innerHTML =
                          (null === (e = n.options.display) || void 0 === e
                            ? void 0
                            : e[t]) || t),
                        n.options.useTouchEvents
                          ? (o.ontouchstart = function (e) {
                              return s(t, e || r());
                            })
                          : (o.onclick = function () {
                              var e =
                                arguments.length > 0 && void 0 !== arguments[0]
                                  ? arguments[0]
                                  : r();
                              return s(t, e);
                            }),
                        u.appendChild(o));
                    }));
                  var c = i > 0,
                    l = document.createElement('div');
                  (l.classList.add('hg-candidate-box-prev'),
                    c && l.classList.add('hg-candidate-box-btn-active'));
                  var f = function () {
                    c &&
                      n.renderPage({
                        candidateListPages: o,
                        targetElement: r,
                        pageIndex: i - 1,
                        nbPages: a,
                        onItemSelected: s,
                      });
                  };
                  (this.options.useTouchEvents
                    ? (l.ontouchstart = f)
                    : (l.onclick = f),
                    this.candidateBoxElement.appendChild(l),
                    this.candidateBoxElement.appendChild(u));
                  var d = i < a - 1,
                    p = document.createElement('div');
                  (p.classList.add('hg-candidate-box-next'),
                    d && p.classList.add('hg-candidate-box-btn-active'));
                  var h = function () {
                    d &&
                      n.renderPage({
                        candidateListPages: o,
                        targetElement: r,
                        pageIndex: i + 1,
                        nbPages: a,
                        onItemSelected: s,
                      });
                  };
                  (this.options.useTouchEvents
                    ? (p.ontouchstart = h)
                    : (p.onclick = h),
                    this.candidateBoxElement.appendChild(p),
                    r.prepend(this.candidateBoxElement));
                },
              },
            ]),
            e && b(t.prototype, e),
            n && b(t, n),
            Object.defineProperty(t, 'prototype', { writable: !1 }),
            t
          );
          var t, e, n;
        })(),
        O = E;
      function S(t) {
        return (
          (function (t) {
            if (Array.isArray(t)) return k(t);
          })(t) ||
          (function (t) {
            if (
              ('undefined' != typeof Symbol && null != t[Symbol.iterator]) ||
              null != t['@@iterator']
            )
              return Array.from(t);
          })(t) ||
          (function (t, e) {
            if (t) {
              if ('string' == typeof t) return k(t, e);
              var n = {}.toString.call(t).slice(8, -1);
              return (
                'Object' === n && t.constructor && (n = t.constructor.name),
                'Map' === n || 'Set' === n
                  ? Array.from(t)
                  : 'Arguments' === n ||
                      /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
                    ? k(t, e)
                    : void 0
              );
            }
          })(t) ||
          (function () {
            throw new TypeError(
              'Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.',
            );
          })()
        );
      }
      function k(t, e) {
        (null == e || e > t.length) && (e = t.length);
        for (var n = 0, o = Array(e); n < e; n++) o[n] = t[n];
        return o;
      }
      function P(t) {
        return (
          (P =
            'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
              ? function (t) {
                  return typeof t;
                }
              : function (t) {
                  return t &&
                    'function' == typeof Symbol &&
                    t.constructor === Symbol &&
                    t !== Symbol.prototype
                    ? 'symbol'
                    : typeof t;
                }),
          P(t)
        );
      }
      function I(t, e) {
        var n = Object.keys(t);
        if (Object.getOwnPropertySymbols) {
          var o = Object.getOwnPropertySymbols(t);
          (e &&
            (o = o.filter(function (e) {
              return Object.getOwnPropertyDescriptor(t, e).enumerable;
            })),
            n.push.apply(n, o));
        }
        return n;
      }
      function C(t, e) {
        for (var n = 0; n < e.length; n++) {
          var o = e[n];
          ((o.enumerable = o.enumerable || !1),
            (o.configurable = !0),
            'value' in o && (o.writable = !0),
            Object.defineProperty(t, M(o.key), o));
        }
      }
      function A(t, e, n) {
        return (
          (e = M(e)) in t
            ? Object.defineProperty(t, e, {
                value: n,
                enumerable: !0,
                configurable: !0,
                writable: !0,
              })
            : (t[e] = n),
          t
        );
      }
      function M(t) {
        var e = (function (t, e) {
          if ('object' != P(t) || !t) return t;
          var n = t[Symbol.toPrimitive];
          if (void 0 !== n) {
            var o = n.call(t, e || 'default');
            if ('object' != P(o)) return o;
            throw new TypeError('@@toPrimitive must return a primitive value.');
          }
          return ('string' === e ? String : Number)(t);
        })(t, 'string');
        return 'symbol' == P(e) ? e : e + '';
      }
      var D = (function () {
          return (
            (t = function t(e, n) {
              var o = this;
              if (
                ((function (t, e) {
                  if (!(t instanceof e))
                    throw new TypeError('Cannot call a class as a function');
                })(this, t),
                A(this, 'input', void 0),
                A(this, 'options', void 0),
                A(this, 'utilities', void 0),
                A(this, 'caretPosition', void 0),
                A(this, 'caretPositionEnd', void 0),
                A(this, 'keyboardDOM', void 0),
                A(this, 'keyboardPluginClasses', void 0),
                A(this, 'keyboardDOMClass', void 0),
                A(this, 'buttonElements', void 0),
                A(this, 'currentInstanceName', void 0),
                A(this, 'allKeyboardInstances', void 0),
                A(this, 'keyboardInstanceNames', void 0),
                A(this, 'isFirstKeyboardInstance', void 0),
                A(this, 'physicalKeyboard', void 0),
                A(this, 'modules', void 0),
                A(this, 'activeButtonClass', void 0),
                A(this, 'holdInteractionTimeout', void 0),
                A(this, 'holdTimeout', void 0),
                A(this, 'isMouseHold', void 0),
                A(this, 'initialized', void 0),
                A(this, 'candidateBox', void 0),
                A(this, 'keyboardRowsDOM', void 0),
                A(this, 'defaultName', 'default'),
                A(this, 'activeInputElement', null),
                A(this, 'handleParams', function (t, e) {
                  var n, o, r;
                  if ('string' == typeof t)
                    ((n = t.split('.').join('')),
                      (o = document.querySelector('.'.concat(n))),
                      (r = e));
                  else if (t instanceof HTMLDivElement) {
                    if (!t.className)
                      throw (
                        console.warn(
                          'Any DOM element passed as parameter must have a class.',
                        ),
                        new Error('KEYBOARD_DOM_CLASS_ERROR')
                      );
                    ((n = t.className.split(' ')[0]), (o = t), (r = e));
                  } else
                    ((n = 'simple-keyboard'),
                      (o = document.querySelector('.'.concat(n))),
                      (r = t));
                  return { keyboardDOMClass: n, keyboardDOM: o, options: r };
                }),
                A(this, 'getOptions', function () {
                  return o.options;
                }),
                A(this, 'getCaretPosition', function () {
                  return o.caretPosition;
                }),
                A(this, 'getCaretPositionEnd', function () {
                  return o.caretPositionEnd;
                }),
                A(this, 'registerModule', function (t, e) {
                  (o.modules[t] || (o.modules[t] = {}), e(o.modules[t]));
                }),
                A(this, 'getKeyboardClassString', function () {
                  for (
                    var t = arguments.length, e = new Array(t), n = 0;
                    n < t;
                    n++
                  )
                    e[n] = arguments[n];
                  return [o.keyboardDOMClass]
                    .concat(e)
                    .filter(function (t) {
                      return !!t;
                    })
                    .join(' ');
                }),
                'undefined' != typeof window)
              ) {
                var r = this.handleParams(e, n),
                  i = r.keyboardDOMClass,
                  a = r.keyboardDOM,
                  s = r.options,
                  u = void 0 === s ? {} : s;
                ((this.utilities = new d({
                  getOptions: this.getOptions,
                  getCaretPosition: this.getCaretPosition,
                  getCaretPositionEnd: this.getCaretPositionEnd,
                  dispatch: this.dispatch,
                })),
                  (this.caretPosition = null),
                  (this.caretPositionEnd = null),
                  (this.keyboardDOM = a),
                  (this.options = (function (t) {
                    for (var e = 1; e < arguments.length; e++) {
                      var n = null != arguments[e] ? arguments[e] : {};
                      e % 2
                        ? I(Object(n), !0).forEach(function (e) {
                            A(t, e, n[e]);
                          })
                        : Object.getOwnPropertyDescriptors
                          ? Object.defineProperties(
                              t,
                              Object.getOwnPropertyDescriptors(n),
                            )
                          : I(Object(n)).forEach(function (e) {
                              Object.defineProperty(
                                t,
                                e,
                                Object.getOwnPropertyDescriptor(n, e),
                              );
                            });
                    }
                    return t;
                  })(
                    {
                      layoutName: 'default',
                      theme: 'hg-theme-default',
                      inputName: 'default',
                      preventMouseDownDefault: !1,
                      enableLayoutCandidates: !0,
                      excludeFromLayout: {},
                    },
                    u,
                  )),
                  (this.keyboardPluginClasses = ''),
                  d.bindMethods(t, this));
                var c = this.options.inputName,
                  l = void 0 === c ? this.defaultName : c;
                if (
                  ((this.input = {}),
                  (this.input[l] = ''),
                  (this.keyboardDOMClass = i),
                  (this.buttonElements = {}),
                  window.SimpleKeyboardInstances ||
                    (window.SimpleKeyboardInstances = {}),
                  (this.currentInstanceName = this.utilities.camelCase(
                    this.keyboardDOMClass,
                  )),
                  (window.SimpleKeyboardInstances[this.currentInstanceName] =
                    this),
                  (this.allKeyboardInstances = window.SimpleKeyboardInstances),
                  (this.keyboardInstanceNames = Object.keys(
                    window.SimpleKeyboardInstances,
                  )),
                  (this.isFirstKeyboardInstance =
                    this.keyboardInstanceNames[0] === this.currentInstanceName),
                  (this.physicalKeyboard = new g({
                    dispatch: this.dispatch,
                    getOptions: this.getOptions,
                  })),
                  (this.candidateBox = this.options.enableLayoutCandidates
                    ? new O({
                        utilities: this.utilities,
                        options: this.options,
                      })
                    : null),
                  !this.keyboardDOM)
                )
                  throw (
                    console.warn('".'.concat(i, '" was not found in the DOM.')),
                    new Error('KEYBOARD_DOM_ERROR')
                  );
                (this.render(), (this.modules = {}), this.loadModules());
              }
            }),
            (e = [
              {
                key: 'setCaretPosition',
                value: function (t) {
                  var e =
                    arguments.length > 1 && void 0 !== arguments[1]
                      ? arguments[1]
                      : t;
                  ((this.caretPosition = t), (this.caretPositionEnd = e));
                },
              },
              {
                key: 'getInputCandidates',
                value: function (t) {
                  var e = this,
                    n = this.options,
                    o = n.layoutCandidates,
                    r = n.layoutCandidatesCaseSensitiveMatch;
                  if (!o || 'object' !== P(o)) return {};
                  var i = Object.keys(o).filter(function (n) {
                    var o = t.substring(0, e.getCaretPositionEnd() || 0) || t,
                      i = new RegExp(
                        ''.concat(e.utilities.escapeRegex(n), '$'),
                        r ? 'g' : 'gi',
                      );
                    return !!S(o.matchAll(i)).length;
                  });
                  if (i.length > 1) {
                    var a = i.sort(function (t, e) {
                      return e.length - t.length;
                    })[0];
                    return { candidateKey: a, candidateValue: o[a] };
                  }
                  if (i.length) {
                    var s = i[0];
                    return { candidateKey: s, candidateValue: o[s] };
                  }
                  return {};
                },
              },
              {
                key: 'showCandidatesBox',
                value: function (t, e, n) {
                  var o = this;
                  this.candidateBox &&
                    this.candidateBox.show({
                      candidateValue: e,
                      targetElement: n,
                      onSelect: function (e, n) {
                        var r = o.options,
                          i = r.layoutCandidatesCaseSensitiveMatch,
                          a = r.disableCandidateNormalization,
                          s = r.enableLayoutCandidatesKeyPress,
                          u = e;
                        (a || (u = e.normalize('NFD')),
                          'function' == typeof o.options.beforeInputUpdate &&
                            o.options.beforeInputUpdate(o));
                        var c = o.getInput(o.options.inputName, !0),
                          l = o.getCaretPositionEnd() || 0,
                          f = c.substring(0, l || 0) || c,
                          d = new RegExp(
                            ''.concat(o.utilities.escapeRegex(t), '$'),
                            i ? 'g' : 'gi',
                          ),
                          p = f.replace(d, u),
                          h = c.replace(f, p),
                          v = p.length - f.length,
                          y = (l || c.length) + v;
                        (y < 0 && (y = 0),
                          o.setInput(h, o.options.inputName, !0),
                          o.setCaretPosition(y),
                          s &&
                            'function' == typeof o.options.onKeyPress &&
                            o.options.onKeyPress(e, n),
                          'function' == typeof o.options.onChange &&
                            o.options.onChange(
                              o.getInput(o.options.inputName, !0),
                              n,
                            ),
                          'function' == typeof o.options.onChangeAll &&
                            o.options.onChangeAll(o.getAllInputs(), n));
                      },
                    });
                },
              },
              {
                key: 'handleButtonClicked',
                value: function (t, e) {
                  var n = this.options,
                    o = n.inputName,
                    r = void 0 === o ? this.defaultName : o,
                    i = n.debug;
                  if ('{//}' !== t) {
                    (this.input[r] || (this.input[r] = ''),
                      'function' == typeof this.options.beforeInputUpdate &&
                        this.options.beforeInputUpdate(this));
                    var a = this.utilities.getUpdatedInput(
                      t,
                      this.input[r],
                      this.caretPosition,
                      this.caretPositionEnd,
                    );
                    if (
                      this.utilities.isStandardButton(t) &&
                      this.activeInputElement &&
                      this.input[r] &&
                      this.input[r] === a &&
                      0 === this.caretPosition &&
                      this.caretPositionEnd === a.length
                    )
                      return (
                        this.setInput('', this.options.inputName, !0),
                        this.setCaretPosition(0),
                        (this.activeInputElement.value = ''),
                        this.activeInputElement.setSelectionRange(0, 0),
                        void this.handleButtonClicked(t, e)
                      );
                    if (
                      ('function' == typeof this.options.onKeyPress &&
                        this.options.onKeyPress(t, e),
                      this.input[r] !== a &&
                        (!this.options.inputPattern ||
                          (this.options.inputPattern &&
                            this.inputPatternIsValid(a))))
                    ) {
                      if (
                        this.options.maxLength &&
                        this.utilities.handleMaxLength(this.input, a)
                      )
                        return;
                      var s = this.utilities.getUpdatedInput(
                        t,
                        this.input[r],
                        this.caretPosition,
                        this.caretPositionEnd,
                        !0,
                      );
                      if (
                        (this.setInput(s, this.options.inputName, !0),
                        i && console.log('Input changed:', this.getAllInputs()),
                        this.options.debug &&
                          console.log(
                            'Caret at: ',
                            this.getCaretPosition(),
                            this.getCaretPositionEnd(),
                            '('.concat(this.keyboardDOMClass, ')'),
                            null == e ? void 0 : e.type,
                          ),
                        this.options.syncInstanceInputs &&
                          this.syncInstanceInputs(),
                        'function' == typeof this.options.onChange &&
                          this.options.onChange(
                            this.getInput(this.options.inputName, !0),
                            e,
                          ),
                        'function' == typeof this.options.onChangeAll &&
                          this.options.onChangeAll(this.getAllInputs(), e),
                        null != e &&
                          e.target &&
                          this.options.enableLayoutCandidates)
                      ) {
                        var u,
                          c = this.getInputCandidates(a),
                          l = c.candidateKey,
                          f = c.candidateValue;
                        l && f
                          ? this.showCandidatesBox(l, f, this.keyboardDOM)
                          : null === (u = this.candidateBox) ||
                            void 0 === u ||
                            u.destroy();
                      }
                    }
                    (this.caretPositionEnd &&
                      this.caretPosition !== this.caretPositionEnd &&
                      (this.setCaretPosition(
                        this.caretPositionEnd,
                        this.caretPositionEnd,
                      ),
                      this.activeInputElement &&
                        this.activeInputElement.setSelectionRange(
                          this.caretPositionEnd,
                          this.caretPositionEnd,
                        ),
                      this.options.debug &&
                        console.log(
                          'Caret position aligned',
                          this.caretPosition,
                        )),
                      i && console.log('Key pressed:', t));
                  }
                },
              },
              {
                key: 'getMouseHold',
                value: function () {
                  return this.isMouseHold;
                },
              },
              {
                key: 'setMouseHold',
                value: function (t) {
                  this.options.syncInstanceInputs
                    ? this.dispatch(function (e) {
                        e.isMouseHold = t;
                      })
                    : (this.isMouseHold = t);
                },
              },
              {
                key: 'handleButtonMouseDown',
                value: function (t, e) {
                  var n = this;
                  (e &&
                    (this.options.preventMouseDownDefault && e.preventDefault(),
                    this.options.stopMouseDownPropagation &&
                      e.stopPropagation(),
                    e.target.classList.add(this.activeButtonClass)),
                    this.holdInteractionTimeout &&
                      clearTimeout(this.holdInteractionTimeout),
                    this.holdTimeout && clearTimeout(this.holdTimeout),
                    this.setMouseHold(!0),
                    this.options.disableButtonHold ||
                      (this.holdTimeout = window.setTimeout(function () {
                        (((n.getMouseHold() &&
                          ((!t.includes('{') && !t.includes('}')) ||
                            '{delete}' === t ||
                            '{backspace}' === t ||
                            '{bksp}' === t ||
                            '{space}' === t ||
                            '{tab}' === t)) ||
                          '{arrowright}' === t ||
                          '{arrowleft}' === t ||
                          '{arrowup}' === t ||
                          '{arrowdown}' === t) &&
                          (n.options.debug && console.log('Button held:', t),
                          n.handleButtonHold(t)),
                          clearTimeout(n.holdTimeout));
                      }, 500)));
                },
              },
              {
                key: 'handleButtonMouseUp',
                value: function (t, e) {
                  var n = this;
                  (e &&
                    (this.options.preventMouseUpDefault &&
                      e.preventDefault &&
                      e.preventDefault(),
                    this.options.stopMouseUpPropagation &&
                      e.stopPropagation &&
                      e.stopPropagation(),
                    !(
                      e.target === this.keyboardDOM ||
                      (e.target && this.keyboardDOM.contains(e.target)) ||
                      (this.candidateBox &&
                        this.candidateBox.candidateBoxElement &&
                        (e.target === this.candidateBox.candidateBoxElement ||
                          (e.target &&
                            this.candidateBox.candidateBoxElement.contains(
                              e.target,
                            ))))
                    ) &&
                      this.candidateBox &&
                      this.candidateBox.destroy()),
                    this.recurseButtons(function (t) {
                      t.classList.remove(n.activeButtonClass);
                    }),
                    this.setMouseHold(!1),
                    this.holdInteractionTimeout &&
                      clearTimeout(this.holdInteractionTimeout),
                    t &&
                      'function' == typeof this.options.onKeyReleased &&
                      this.options.onKeyReleased(t, e));
                },
              },
              {
                key: 'handleKeyboardContainerMouseDown',
                value: function (t) {
                  this.options.preventMouseDownDefault && t.preventDefault();
                },
              },
              {
                key: 'handleButtonHold',
                value: function (t) {
                  var e = this;
                  (this.holdInteractionTimeout &&
                    clearTimeout(this.holdInteractionTimeout),
                    (this.holdInteractionTimeout = window.setTimeout(
                      function () {
                        e.getMouseHold()
                          ? (e.handleButtonClicked(t), e.handleButtonHold(t))
                          : clearTimeout(e.holdInteractionTimeout);
                      },
                      100,
                    )));
                },
              },
              {
                key: 'syncInstanceInputs',
                value: function () {
                  var t = this;
                  this.dispatch(function (e) {
                    (e.replaceInput(t.input),
                      e.setCaretPosition(t.caretPosition, t.caretPositionEnd));
                  });
                },
              },
              {
                key: 'clearInput',
                value: function () {
                  var t =
                    arguments.length > 0 && void 0 !== arguments[0]
                      ? arguments[0]
                      : this.options.inputName || this.defaultName;
                  ((this.input[t] = ''),
                    this.setCaretPosition(0),
                    this.options.syncInstanceInputs &&
                      this.syncInstanceInputs());
                },
              },
              {
                key: 'getInput',
                value: function () {
                  var t =
                      arguments.length > 0 && void 0 !== arguments[0]
                        ? arguments[0]
                        : this.options.inputName || this.defaultName,
                    e =
                      arguments.length > 1 &&
                      void 0 !== arguments[1] &&
                      arguments[1];
                  return (
                    this.options.syncInstanceInputs &&
                      !e &&
                      this.syncInstanceInputs(),
                    this.options.rtl
                      ? '‫' +
                        this.input[t].replace('‫', '').replace('‬', '') +
                        '‬'
                      : this.input[t]
                  );
                },
              },
              {
                key: 'getAllInputs',
                value: function () {
                  var t = this,
                    e = {};
                  return (
                    Object.keys(this.input).forEach(function (n) {
                      e[n] = t.getInput(n, !0);
                    }),
                    e
                  );
                },
              },
              {
                key: 'setInput',
                value: function (t) {
                  var e =
                      arguments.length > 1 && void 0 !== arguments[1]
                        ? arguments[1]
                        : this.options.inputName || this.defaultName,
                    n = arguments.length > 2 ? arguments[2] : void 0;
                  ((this.input[e] = t),
                    !n &&
                      this.options.syncInstanceInputs &&
                      this.syncInstanceInputs());
                },
              },
              {
                key: 'replaceInput',
                value: function (t) {
                  this.input = t;
                },
              },
              {
                key: 'setOptions',
                value: function () {
                  var t =
                      arguments.length > 0 && void 0 !== arguments[0]
                        ? arguments[0]
                        : {},
                    e = this.changedOptions(t);
                  ((this.options = Object.assign(this.options, t)),
                    e.length &&
                      (this.options.debug && console.log('changedOptions', e),
                      this.onSetOptions(e),
                      this.render()));
                },
              },
              {
                key: 'changedOptions',
                value: function (t) {
                  var e = this;
                  return Object.keys(t).filter(function (n) {
                    return (
                      JSON.stringify(t[n]) !== JSON.stringify(e.options[n])
                    );
                  });
                },
              },
              {
                key: 'onSetOptions',
                value: function () {
                  var t =
                    arguments.length > 0 && void 0 !== arguments[0]
                      ? arguments[0]
                      : [];
                  (t.includes('layoutName') &&
                    this.candidateBox &&
                    this.candidateBox.destroy(),
                    (t.includes('layoutCandidatesPageSize') ||
                      t.includes('layoutCandidates')) &&
                      this.candidateBox &&
                      (this.candidateBox.destroy(),
                      (this.candidateBox = new O({
                        utilities: this.utilities,
                        options: this.options,
                      }))));
                },
              },
              {
                key: 'resetRows',
                value: function () {
                  (this.keyboardRowsDOM && this.keyboardRowsDOM.remove(),
                    (this.keyboardDOM.className = this.keyboardDOMClass),
                    this.keyboardDOM.setAttribute(
                      'data-skInstance',
                      this.currentInstanceName,
                    ),
                    (this.buttonElements = {}));
                },
              },
              {
                key: 'dispatch',
                value: function (t) {
                  if (!window.SimpleKeyboardInstances)
                    throw (
                      console.warn(
                        'SimpleKeyboardInstances is not defined. Dispatch cannot be called.',
                      ),
                      new Error('INSTANCES_VAR_ERROR')
                    );
                  return Object.keys(window.SimpleKeyboardInstances).forEach(
                    function (e) {
                      t(window.SimpleKeyboardInstances[e], e);
                    },
                  );
                },
              },
              {
                key: 'addButtonTheme',
                value: function (t, e) {
                  var n = this;
                  e &&
                    t &&
                    (t.split(' ').forEach(function (o) {
                      e.split(' ').forEach(function (e) {
                        n.options.buttonTheme || (n.options.buttonTheme = []);
                        var r = !1;
                        (n.options.buttonTheme.map(function (t) {
                          if (null != t && t.class.split(' ').includes(e)) {
                            r = !0;
                            var n = t.buttons.split(' ');
                            n.includes(o) ||
                              ((r = !0), n.push(o), (t.buttons = n.join(' ')));
                          }
                          return t;
                        }),
                          r ||
                            n.options.buttonTheme.push({
                              class: e,
                              buttons: t,
                            }));
                      });
                    }),
                    this.render());
                },
              },
              {
                key: 'removeButtonTheme',
                value: function (t, e) {
                  var n = this;
                  if (!t && !e)
                    return (
                      (this.options.buttonTheme = []),
                      void this.render()
                    );
                  t &&
                    Array.isArray(this.options.buttonTheme) &&
                    this.options.buttonTheme.length &&
                    (t.split(' ').forEach(function (t) {
                      var o;
                      null === (o = n.options) ||
                        void 0 === o ||
                        null === (o = o.buttonTheme) ||
                        void 0 === o ||
                        o.map(function (o, r) {
                          if ((o && e && e.includes(o.class)) || !e) {
                            var i,
                              a,
                              s =
                                null === (i = o) || void 0 === i
                                  ? void 0
                                  : i.buttons.split(' ').filter(function (e) {
                                      return e !== t;
                                    });
                            o && null != s && s.length
                              ? (o.buttons = s.join(' '))
                              : (null === (a = n.options.buttonTheme) ||
                                  void 0 === a ||
                                  a.splice(r, 1),
                                (o = null));
                          }
                          return o;
                        });
                    }),
                    this.render());
                },
              },
              {
                key: 'getButtonElement',
                value: function (t) {
                  var e,
                    n = this.buttonElements[t];
                  return (n && (e = n.length > 1 ? n : n[0]), e);
                },
              },
              {
                key: 'inputPatternIsValid',
                value: function (t) {
                  var e,
                    n = this.options.inputPattern;
                  if (
                    (e =
                      n instanceof RegExp
                        ? n
                        : n[this.options.inputName || this.defaultName]) &&
                    t
                  ) {
                    var o = e.test(t);
                    return (
                      this.options.debug &&
                        console.log(
                          'inputPattern ("'
                            .concat(e, '"): ')
                            .concat(o ? 'passed' : 'did not pass!'),
                        ),
                      o
                    );
                  }
                  return !0;
                },
              },
              {
                key: 'setEventListeners',
                value: function () {
                  if (
                    this.isFirstKeyboardInstance ||
                    !this.allKeyboardInstances
                  ) {
                    this.options.debug &&
                      console.log(
                        'Caret handling started ('.concat(
                          this.keyboardDOMClass,
                          ')',
                        ),
                      );
                    var t =
                        this.options.physicalKeyboardHighlightPreventDefault,
                      e = void 0 !== t && t;
                    (document.addEventListener('keyup', this.handleKeyUp, e),
                      document.addEventListener(
                        'keydown',
                        this.handleKeyDown,
                        e,
                      ),
                      document.addEventListener('mouseup', this.handleMouseUp),
                      document.addEventListener(
                        'touchend',
                        this.handleTouchEnd,
                      ),
                      this.options.updateCaretOnSelectionChange &&
                        document.addEventListener(
                          'selectionchange',
                          this.handleSelectionChange,
                        ),
                      document.addEventListener('select', this.handleSelect));
                  }
                },
              },
              {
                key: 'handleKeyUp',
                value: function (t) {
                  (this.caretEventHandler(t),
                    this.options.physicalKeyboardHighlight &&
                      this.physicalKeyboard.handleHighlightKeyUp(t));
                },
              },
              {
                key: 'handleKeyDown',
                value: function (t) {
                  this.options.physicalKeyboardHighlight &&
                    this.physicalKeyboard.handleHighlightKeyDown(t);
                },
              },
              {
                key: 'handleMouseUp',
                value: function (t) {
                  this.caretEventHandler(t);
                },
              },
              {
                key: 'handleTouchEnd',
                value: function (t) {
                  this.caretEventHandler(t);
                },
              },
              {
                key: 'handleSelect',
                value: function (t) {
                  this.caretEventHandler(t);
                },
              },
              {
                key: 'handleSelectionChange',
                value: function (t) {
                  navigator.userAgent.includes('Firefox') ||
                    this.caretEventHandler(t);
                },
              },
              {
                key: 'caretEventHandler',
                value: function (t) {
                  var e,
                    n = this;
                  (t.target.tagName && (e = t.target.tagName.toLowerCase()),
                    this.dispatch(function (o) {
                      var r =
                        t.target === o.keyboardDOM ||
                        (t.target && o.keyboardDOM.contains(t.target));
                      if (
                        (n.options.syncInstanceInputs &&
                          Array.isArray(t.path) &&
                          (r = t.path.some(function (t) {
                            var e;
                            return null == t ||
                              null === (e = t.hasAttribute) ||
                              void 0 === e
                              ? void 0
                              : e.call(t, 'data-skInstance');
                          })),
                        ('textarea' === e ||
                          ('input' === e &&
                            [
                              'text',
                              'search',
                              'url',
                              'tel',
                              'password',
                            ].includes(t.target.type))) &&
                          !o.options.disableCaretPositioning)
                      ) {
                        var i = t.target.selectionStart,
                          a = t.target.selectionEnd;
                        (o.options.rtl &&
                          ((i = o.utilities.getRtlOffset(i, o.getInput())),
                          (a = o.utilities.getRtlOffset(a, o.getInput()))),
                          o.setCaretPosition(i, a),
                          (o.activeInputElement = t.target),
                          o.options.debug &&
                            console.log(
                              'Caret at: ',
                              o.getCaretPosition(),
                              o.getCaretPositionEnd(),
                              t && t.target.tagName.toLowerCase(),
                              '('.concat(o.keyboardDOMClass, ')'),
                              null == t ? void 0 : t.type,
                            ));
                      } else
                        (!o.options.disableCaretPositioning && r) ||
                          'selectionchange' === (null == t ? void 0 : t.type) ||
                          (o.setCaretPosition(null),
                          (o.activeInputElement = null),
                          o.options.debug &&
                            console.log(
                              'Caret position reset due to "'.concat(
                                null == t ? void 0 : t.type,
                                '" event',
                              ),
                              t,
                            ));
                    }));
                },
              },
              {
                key: 'recurseButtons',
                value: function (t) {
                  var e = this;
                  t &&
                    Object.keys(this.buttonElements).forEach(function (n) {
                      return e.buttonElements[n].forEach(t);
                    });
                },
              },
              {
                key: 'destroy',
                value: function () {
                  this.options.debug &&
                    console.log(
                      'Destroying simple-keyboard instance: '.concat(
                        this.currentInstanceName,
                      ),
                    );
                  var t = this.options.physicalKeyboardHighlightPreventDefault,
                    e = void 0 !== t && t;
                  (document.removeEventListener('keyup', this.handleKeyUp, e),
                    document.removeEventListener(
                      'keydown',
                      this.handleKeyDown,
                      e,
                    ),
                    document.removeEventListener('mouseup', this.handleMouseUp),
                    document.removeEventListener(
                      'touchend',
                      this.handleTouchEnd,
                    ),
                    document.removeEventListener('select', this.handleSelect),
                    this.options.updateCaretOnSelectionChange &&
                      document.removeEventListener(
                        'selectionchange',
                        this.handleSelectionChange,
                      ),
                    (document.onpointerup = null),
                    (document.ontouchend = null),
                    (document.ontouchcancel = null),
                    (document.onmouseup = null),
                    this.recurseButtons(function (t) {
                      t &&
                        ((t.onpointerdown = null),
                        (t.onpointerup = null),
                        (t.onpointercancel = null),
                        (t.ontouchstart = null),
                        (t.ontouchend = null),
                        (t.ontouchcancel = null),
                        (t.onclick = null),
                        (t.onmousedown = null),
                        (t.onmouseup = null),
                        t.remove(),
                        (t = null));
                    }),
                    (this.keyboardDOM.onpointerdown = null),
                    (this.keyboardDOM.ontouchstart = null),
                    (this.keyboardDOM.onmousedown = null),
                    this.resetRows(),
                    this.candidateBox &&
                      (this.candidateBox.destroy(), (this.candidateBox = null)),
                    (this.activeInputElement = null),
                    this.keyboardDOM.removeAttribute('data-skInstance'),
                    (this.keyboardDOM.innerHTML = ''),
                    (window.SimpleKeyboardInstances[this.currentInstanceName] =
                      null),
                    delete window.SimpleKeyboardInstances[
                      this.currentInstanceName
                    ],
                    (this.initialized = !1));
                },
              },
              {
                key: 'getButtonThemeClasses',
                value: function (t) {
                  var e = this.options.buttonTheme,
                    n = [];
                  return (
                    Array.isArray(e) &&
                      e.forEach(function (e) {
                        if (
                          e &&
                          e.class &&
                          'string' == typeof e.class &&
                          e.buttons &&
                          'string' == typeof e.buttons
                        ) {
                          var o = e.class.split(' ');
                          e.buttons.split(' ').includes(t) &&
                            (n = [].concat(S(n), S(o)));
                        } else
                          console.warn(
                            'Incorrect "buttonTheme". Please check the documentation.',
                            e,
                          );
                      }),
                    n
                  );
                },
              },
              {
                key: 'setDOMButtonAttributes',
                value: function (t, e) {
                  var n = this.options.buttonAttributes;
                  Array.isArray(n) &&
                    n.forEach(function (n) {
                      n.attribute &&
                      'string' == typeof n.attribute &&
                      n.value &&
                      'string' == typeof n.value &&
                      n.buttons &&
                      'string' == typeof n.buttons
                        ? n.buttons.split(' ').includes(t) &&
                          e(n.attribute, n.value)
                        : console.warn(
                            'Incorrect "buttonAttributes". Please check the documentation.',
                            n,
                          );
                    });
                },
              },
              {
                key: 'onTouchDeviceDetected',
                value: function () {
                  (this.processAutoTouchEvents(),
                    this.disableContextualWindow());
                },
              },
              {
                key: 'disableContextualWindow',
                value: function () {
                  window.oncontextmenu = function (t) {
                    if (t.target.classList.contains('hg-button'))
                      return (t.preventDefault(), t.stopPropagation(), !1);
                  };
                },
              },
              {
                key: 'processAutoTouchEvents',
                value: function () {
                  this.options.autoUseTouchEvents &&
                    ((this.options.useTouchEvents = !0),
                    this.options.debug &&
                      console.log(
                        'autoUseTouchEvents: Touch device detected, useTouchEvents enabled.',
                      ));
                },
              },
              {
                key: 'onInit',
                value: function () {
                  (this.options.debug &&
                    console.log(
                      ''.concat(this.keyboardDOMClass, ' Initialized'),
                    ),
                    this.setEventListeners(),
                    'function' == typeof this.options.onInit &&
                      this.options.onInit(this));
                },
              },
              {
                key: 'beforeFirstRender',
                value: function () {
                  (this.utilities.isTouchDevice() &&
                    this.onTouchDeviceDetected(),
                    'function' == typeof this.options.beforeFirstRender &&
                      this.options.beforeFirstRender(this),
                    this.isFirstKeyboardInstance &&
                      this.utilities.pointerEventsSupported() &&
                      !this.options.useTouchEvents &&
                      !this.options.useMouseEvents &&
                      this.options.debug &&
                      console.log(
                        'Using PointerEvents as it is supported by this browser',
                      ),
                    this.options.useTouchEvents &&
                      this.options.debug &&
                      console.log(
                        'useTouchEvents has been enabled. Only touch events will be used.',
                      ));
                },
              },
              {
                key: 'beforeRender',
                value: function () {
                  'function' == typeof this.options.beforeRender &&
                    this.options.beforeRender(this);
                },
              },
              {
                key: 'onRender',
                value: function () {
                  'function' == typeof this.options.onRender &&
                    this.options.onRender(this);
                },
              },
              {
                key: 'onModulesLoaded',
                value: function () {
                  'function' == typeof this.options.onModulesLoaded &&
                    this.options.onModulesLoaded(this);
                },
              },
              {
                key: 'loadModules',
                value: function () {
                  var t = this;
                  Array.isArray(this.options.modules) &&
                    (this.options.modules.forEach(function (e) {
                      var n = t.utilities.isConstructor(e) ? new e(t) : e(t);
                      n.init && n.init(t);
                    }),
                    (this.keyboardPluginClasses = 'modules-loaded'),
                    this.render(),
                    this.onModulesLoaded());
                },
              },
              {
                key: 'getModuleProp',
                value: function (t, e) {
                  return !!this.modules[t] && this.modules[t][e];
                },
              },
              {
                key: 'getModulesList',
                value: function () {
                  return Object.keys(this.modules);
                },
              },
              {
                key: 'parseRowDOMContainers',
                value: function (t, e, n, o) {
                  var r = this,
                    i = Array.from(t.children),
                    a = 0;
                  return (
                    i.length &&
                      n.forEach(function (n, s) {
                        var u = o[s];
                        if (!(u && u > n)) return !1;
                        var c = n - a,
                          l = u - a,
                          f = document.createElement('div');
                        f.className += 'hg-button-container';
                        var d = ''
                          .concat(r.options.layoutName, '-r')
                          .concat(e, 'c')
                          .concat(s);
                        f.setAttribute('data-skUID', d);
                        var p = i.splice(c, l - c + 1);
                        ((a += l - c),
                          p.forEach(function (t) {
                            return f.appendChild(t);
                          }),
                          i.splice(c, 0, f),
                          (t.innerHTML = ''),
                          i.forEach(function (e) {
                            return t.appendChild(e);
                          }),
                          r.options.debug &&
                            console.log('rowDOMContainer', p, c, l, a + 1));
                      }),
                    t
                  );
                },
              },
              {
                key: 'render',
                value: function () {
                  var t = this;
                  (this.resetRows(),
                    this.initialized || this.beforeFirstRender(),
                    this.beforeRender());
                  var e = 'hg-layout-'.concat(this.options.layoutName),
                    n = this.options.layout || {
                      default: [
                        '` 1 2 3 4 5 6 7 8 9 0 - = {bksp}',
                        '{tab} q w e r t y u i o p [ ] \\',
                        "{lock} a s d f g h j k l ; ' {enter}",
                        '{shift} z x c v b n m , . / {shift}',
                        '.com @ {space}',
                      ],
                      shift: [
                        '~ ! @ # $ % ^ & * ( ) _ + {bksp}',
                        '{tab} Q W E R T Y U I O P { } |',
                        '{lock} A S D F G H J K L : " {enter}',
                        '{shift} Z X C V B N M < > ? {shift}',
                        '.com @ {space}',
                      ],
                    },
                    o = this.options.useTouchEvents || !1,
                    r = o ? 'hg-touch-events' : '',
                    i = this.options.useMouseEvents || !1,
                    a = this.options.disableRowButtonContainers;
                  ((this.keyboardDOM.className = this.getKeyboardClassString(
                    this.options.theme,
                    e,
                    this.keyboardPluginClasses,
                    r,
                  )),
                    this.keyboardDOM.setAttribute(
                      'data-skInstance',
                      this.currentInstanceName,
                    ),
                    (this.keyboardRowsDOM = document.createElement('div')),
                    (this.keyboardRowsDOM.className = 'hg-rows'),
                    n[this.options.layoutName || this.defaultName].forEach(
                      function (e, n) {
                        var r = e.split(' ');
                        t.options.excludeFromLayout &&
                          t.options.excludeFromLayout[
                            t.options.layoutName || t.defaultName
                          ] &&
                          (r = r.filter(function (e) {
                            return (
                              t.options.excludeFromLayout &&
                              !t.options.excludeFromLayout[
                                t.options.layoutName || t.defaultName
                              ].includes(e)
                            );
                          }));
                        var s = document.createElement('div');
                        s.className += 'hg-row';
                        var u = [],
                          c = [];
                        (r.forEach(function (e, r) {
                          var l,
                            f =
                              !a &&
                              'string' == typeof e &&
                              e.length > 1 &&
                              0 === e.indexOf('['),
                            d =
                              !a &&
                              'string' == typeof e &&
                              e.length > 1 &&
                              e.indexOf(']') === e.length - 1;
                          (f && (u.push(r), (e = e.replace(/\[/g, ''))),
                            d && (c.push(r), (e = e.replace(/\]/g, ''))));
                          var p = t.utilities.getButtonClass(e),
                            h = t.utilities.getButtonDisplayName(
                              e,
                              t.options.display,
                              t.options.mergeDisplay,
                            ),
                            v = t.options.useButtonTag ? 'button' : 'div',
                            y = document.createElement(v);
                          ((y.className += 'hg-button '.concat(p)),
                            (l = y.classList).add.apply(
                              l,
                              S(t.getButtonThemeClasses(e)),
                            ),
                            t.setDOMButtonAttributes(e, function (t, e) {
                              y.setAttribute(t, e);
                            }),
                            (t.activeButtonClass = 'hg-activeButton'),
                            !t.utilities.pointerEventsSupported() || o || i
                              ? o
                                ? ((y.ontouchstart = function (n) {
                                    (t.handleButtonClicked(e, n),
                                      t.handleButtonMouseDown(e, n));
                                  }),
                                  (y.ontouchend = function (n) {
                                    t.handleButtonMouseUp(e, n);
                                  }),
                                  (y.ontouchcancel = function (n) {
                                    t.handleButtonMouseUp(e, n);
                                  }))
                                : ((y.onclick = function (n) {
                                    (t.setMouseHold(!1),
                                      'function' ==
                                        typeof t.options.onKeyReleased ||
                                        (t.options.useMouseEvents &&
                                          t.options.clickOnMouseDown) ||
                                        t.handleButtonClicked(e, n));
                                  }),
                                  (y.onmousedown = function (n) {
                                    (('function' ==
                                      typeof t.options.onKeyReleased ||
                                      (t.options.useMouseEvents &&
                                        t.options.clickOnMouseDown)) &&
                                      !t.isMouseHold &&
                                      t.handleButtonClicked(e, n),
                                      t.handleButtonMouseDown(e, n));
                                  }),
                                  (y.onmouseup = function (n) {
                                    t.handleButtonMouseUp(e, n);
                                  }))
                              : ((y.onpointerdown = function (n) {
                                  (t.handleButtonClicked(e, n),
                                    t.handleButtonMouseDown(e, n));
                                }),
                                (y.onpointerup = function (n) {
                                  t.handleButtonMouseUp(e, n);
                                }),
                                (y.onpointercancel = function (n) {
                                  t.handleButtonMouseUp(e, n);
                                })),
                            y.setAttribute('data-skBtn', e));
                          var g = ''
                            .concat(t.options.layoutName, '-r')
                            .concat(n, 'b')
                            .concat(r);
                          y.setAttribute('data-skBtnUID', g);
                          var m = document.createElement('span');
                          ((m.innerHTML = h),
                            y.appendChild(m),
                            t.buttonElements[e] || (t.buttonElements[e] = []),
                            t.buttonElements[e].push(y),
                            s.appendChild(y));
                        }),
                          (s = t.parseRowDOMContainers(s, n, u, c)),
                          t.keyboardRowsDOM.appendChild(s));
                      },
                    ),
                    this.keyboardDOM.appendChild(this.keyboardRowsDOM),
                    this.onRender(),
                    this.initialized ||
                      ((this.initialized = !0),
                      !this.utilities.pointerEventsSupported() || o || i
                        ? o
                          ? ((document.ontouchend = function (e) {
                              return t.handleButtonMouseUp(void 0, e);
                            }),
                            (document.ontouchcancel = function (e) {
                              return t.handleButtonMouseUp(void 0, e);
                            }),
                            (this.keyboardDOM.ontouchstart = function (e) {
                              return t.handleKeyboardContainerMouseDown(e);
                            }))
                          : o ||
                            ((document.onmouseup = function (e) {
                              return t.handleButtonMouseUp(void 0, e);
                            }),
                            (this.keyboardDOM.onmousedown = function (e) {
                              return t.handleKeyboardContainerMouseDown(e);
                            }))
                        : ((document.onpointerup = function (e) {
                            return t.handleButtonMouseUp(void 0, e);
                          }),
                          (this.keyboardDOM.onpointerdown = function (e) {
                            return t.handleKeyboardContainerMouseDown(e);
                          })),
                      this.onInit()));
                },
              },
            ]),
            e && C(t.prototype, e),
            n && C(t, n),
            Object.defineProperty(t, 'prototype', { writable: !1 }),
            t
          );
          var t, e, n;
        })(),
        T = D,
        N = T;
      return o;
    })();
  });

  const css = `/*! simple-keyboard v3.8.48 https://github.com/hodgef/simple-keyboard Copyright (c) Francisco Hodge (https://github.com/hodgef) and project contributors. This source code is licensed under the MIT license found in the LICENSE file in the root directory of this source tree.*/.hg-theme-default,.hg-theme-default .hg-button{box-sizing:border-box;padding:5px;border-radius:5px}.hg-theme-default{background-color:#ececec;font-family:HelveticaNeue-Light,Helvetica Neue Light,Helvetica Neue,Helvetica,Arial,Lucida Grande,sans-serif;overflow:hidden;touch-action:manipulation;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:100%}.hg-theme-default .hg-button span,.hg-theme-default .hg-button span svg{pointer-events:none}.hg-theme-default button.hg-button{border-width:0;font-size:inherit;outline:0}.hg-theme-default .hg-button{display:inline-block;flex-grow:1;align-items:center;background:#fff;border-bottom:1px solid #b5b5b5;box-shadow:0 0 3px -1px rgba(0,0,0,.3);cursor:pointer;display:flex;height:40px;justify-content:center;-webkit-tap-highlight-color:transparent}.hg-theme-default .hg-row,.hg-theme-default .hg-row .hg-button-container{display:flex}.hg-theme-default .hg-row:not(:last-child){margin-bottom:5px}.hg-theme-default .hg-row .hg-button-container,.hg-theme-default .hg-row .hg-button:not(:last-child){margin-right:5px}.hg-theme-default .hg-row>div:last-child{margin-right:0}.hg-theme-default .hg-button.hg-standardBtn{width:20px}.hg-theme-default .hg-button.hg-activeButton{background:#efefef}.hg-theme-default.hg-layout-numeric .hg-button{align-items:center;display:flex;height:60px;justify-content:center;width:33.3%}.hg-theme-default .hg-button.hg-button-numpadadd,.hg-theme-default .hg-button.hg-button-numpadenter{height:85px}.hg-theme-default .hg-button.hg-button-numpad0{width:105px}.hg-theme-default .hg-button.hg-button-com{max-width:85px}.hg-theme-default .hg-button.hg-standardBtn.hg-button-at{max-width:45px}.hg-theme-default .hg-button.hg-selectedButton{background:rgba(5,25,70,.53);color:#fff}.hg-theme-default .hg-button.hg-standardBtn[data-skbtn=".com"]{max-width:82px}.hg-theme-default .hg-button.hg-standardBtn[data-skbtn="@"]{max-width:60px}.hg-candidate-box{background:#ececec;border-bottom:2px solid #b5b5b5;border-radius:5px;display:inline-flex;margin-top:-10px;position:absolute;transform:translateY(-100%);-webkit-user-select:none;-moz-user-select:none;user-select:none}ul.hg-candidate-box-list{display:flex;flex:1;list-style:none;margin:0;padding:0}li.hg-candidate-box-list-item{align-items:center;display:flex;height:40px;justify-content:center;width:40px}li.hg-candidate-box-list-item:hover{background:rgba(0,0,0,.03);cursor:pointer}li.hg-candidate-box-list-item:active{background:rgba(0,0,0,.1)}.hg-candidate-box-prev:before{content:"◄"}.hg-candidate-box-next:before{content:"►"}.hg-candidate-box-next,.hg-candidate-box-prev{align-items:center;color:#969696;cursor:pointer;display:flex;padding:0 10px}.hg-candidate-box-next{border-bottom-right-radius:5px;border-top-right-radius:5px}.hg-candidate-box-prev{border-bottom-left-radius:5px;border-top-left-radius:5px}.hg-candidate-box-btn-active{color:#444}.simple-keyboard{position:fixed;top:calc(100% - 185px);left:0;zoom:1.2;z-index:9999;color:#444}`;

  // The main logic of the keyboard is to set a globally accessible __$vkFocus and __$vkKeyboardObj
  // The __$vkKeyboardObj is created using the SimpleKeyboard library
  // When an element is clicked, if not a keyboard button, the __$vkFocus is set to said element
  // and the __$vkKeyboardObj is set to the input value of the element. Then the keyboard is shown.
  // On each virtual keyboard button press, the input value of the __$vkFocus is updated.
  //
  // If the element is not an input or textarea, the keyboard is hidden and the input value is cleared.
  //
  // The internal global variables are prefixed __vk and __$vk, so __$vkFocus means Virtual Keyboard Focus

  function installKeyboard() {
    var style = document.createElement('style');
    style.innerHTML = css;
    document.head.appendChild(style);

    // Handle when to show the keyboard
    document.addEventListener(
      'click',
      (event) => {
        const keyBoardEnabled = window.__vkEnabled;
        if (!keyBoardEnabled) return;

        const isKeyboardButton = event.target.className.includes('hg-button');
        const isInput =
          event.target instanceof HTMLInputElement ||
          event.target instanceof HTMLTextAreaElement;

        const keyboard = window.__$vkKeyboardElement;
        if (!isKeyboardButton) {
          // i.e Virtual Keyboard Focus, Virtual Keyboard Obj and so on
          window.__$vkFocus = event.target;
          window.__$vkKeyboardObj.setInput(event.target.value);

          keyboard.style.display = isInput ? 'block' : 'none';
          !isInput && window.__$vkKeyboardObj.clearInput();
        }
      },
      true,
    );

    // Create the DOM element for the keyboard
    var __vkDiv = document.createElement('div');
    __vkDiv.className = 'simple-keyboard';
    __vkDiv.style.display = 'none';
    window.__$vkKeyboardElement = __vkDiv;
    document.body.appendChild(__vkDiv);

    // using var to enable redeclaration if the page is reloaded
    var __VKKeyboard = window.SimpleKeyboard.default;

    var __vkKeyboard = new __VKKeyboard({
      onChange: (input) => __vkOnChange(input),
      onKeyPress: (button) => __vkOnKeyPress(button),
      theme: 'hg-theme-default hg-theme-ios',
      layout: {
        default: [
          'q w e r t y u i o p {bksp}',
          'a s d f g h j k l {enter}',
          '{shift} z x c v b n m , . {shift}',
          '{alt} {space} {altright}',
        ],
        shift: [
          'Q W E R T Y U I O P {bksp}',
          'A S D F G H J K L {enter}',
          '{shiftactivated} Z X C V B N M , . {shiftactivated}',
          '{alt} {space} {altright}',
        ],
        alt: [
          '1 2 3 4 5 6 7 8 9 0 {bksp}',
          `@ # $ & * ( ) ' " {enter}`,
          '{shift} % - + = / ; : ! ? {shift}',
          '{default} {space} {back}',
        ],
      },
      display: {
        '{alt}': '123',
        '{shift}': '⇧',
        '{shiftactivated}': '⇧',
        '{enter}': '⏎',
        '{bksp}': '⌫',
        '{altright}': '123',
        '{downkeyboard}': '🞃',
        '{space}': ' ',
        '{default}': 'ABC',
        '{back}': '⇦',
      },
    });
    window.__$vkKeyboardObj = __vkKeyboard;

    // Handle when to change the input value
    function __vkOnChange(input) {
      window.__$vkFocus.value = input;
      window.__$vkFocus.dispatchEvent(new Event('change', { bubbles: true }));
      window.__$vkFocus.focus();
    }

    // Handle when to change the keyboard layout
    function __vkOnKeyPress(button) {
      if (button.includes('{') && button.includes('}')) {
        __vkHandleLayoutChange(button);
      }
    }

    function __vkHandleLayoutChange(button) {
      let currentLayout = __vkKeyboard.options.layoutName;
      let layoutName;

      switch (button) {
        case '{shift}':
        case '{shiftactivated}':
        case '{default}':
          layoutName = currentLayout === 'default' ? 'shift' : 'default';
          break;

        case '{alt}':
        case '{altright}':
          layoutName = currentLayout === 'alt' ? 'default' : 'alt';
          break;

        case '{enter}':
          window.__$vkFocus.focus();
          const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            bubbles: true,
            cancelable: true,
          });

          window.__$vkFocus.dispatchEvent(enterEvent);
          break;

        default:
          break;
      }

      if (layoutName) {
        __vkKeyboard.setOptions({
          layoutName: layoutName,
        });
      }
    }
  }

  var state = document.readyState;

  if (state === 'complete' || state === 'interactive') {
    installKeyboard();
  } else {
    window.addEventListener('load', installKeyboard);
  }
}

import {
  BadRequest,
  Browser<PERSON><PERSON><PERSON>oute,
  BrowserInstance,
  BrowserlessSession,
  BrowserManager,
  BrowserServerOptions,
  BrowserWebsocketRoute,
  CDPLaunchOptions,
  convertIfBase64,
  getTokenFromRequest,
  Logger,
  noop,
  NotFound,
  pwVersionRegex,
  Unauthorized,
} from '@browserless.io/browserless';
import path from 'path';
import { EnterpriseChromeCDP, EnterpriseChromiumCDP } from './browsers/core.js';
import { EnterpriseRoutes } from './paths.js';
import { AugmentedRequest, SessionMetadata } from './types.js';
import fileSystem from './file-system.js';
import { ChromiumStealthBrowser } from './browsers/chromium.stealth.js';
import { ChromeStealthBrowser } from './browsers/chrome.stealth.js';
import { SharedConfig } from './config.js';
import { getURLLastSegment } from './utils.js';

type EnterpriseBrowsers = EnterpriseChromeCDP | EnterpriseChromiumCDP;
export default class EnterpriseBrowserManager extends BrowserManager {
  public getBrowserById(id: string): EnterpriseBrowsers | null {
    const found = [...this.browsers].find(([b]) =>
      b.wsEndpoint()?.endsWith(id),
    );

    if (found) {
      return found[0] as unknown as EnterpriseBrowsers;
    }

    return null;
  }

  public async getBrowserForSession(
    req: AugmentedRequest,
    session: SessionMetadata,
  ) {
    const Browser = session.stealth
      ? session.browser === 'chromium'
        ? ChromiumStealthBrowser
        : ChromeStealthBrowser
      : session.browser === 'chromium'
        ? EnterpriseChromiumCDP
        : EnterpriseChromeCDP;

    const browser = new Browser({
      blockAds: session.blockAds,
      record: false,
      config: this.config as SharedConfig,
      logger: this.log,
      userDataDir: session.userDataDir,
    });

    const match = (req.headers['user-agent'] || '').match(pwVersionRegex);
    const pwVersion = match ? match[1] : 'default';
    const decodedLaunchOptions = convertIfBase64(
      req.parsed.searchParams.get('launch') || '{}',
    );
    let parsedLaunchOptions: BrowserServerOptions | CDPLaunchOptions;

    try {
      parsedLaunchOptions = JSON.parse(decodedLaunchOptions);
    } catch (err) {
      throw new BadRequest(
        `Error parsing launch-options: ${err}. Launch options must be a JSON or base64-encoded JSON object`,
      );
    }

    const launchOptions = {
      headless: session.headless ?? parsedLaunchOptions.headless,
      args: [...session.args, ...(parsedLaunchOptions.args ?? [])],
    };

    await browser.launch({
      options: launchOptions,
      pwVersion,
    });

    const browserlessSession: BrowserlessSession = {
      id: browser.wsEndpoint()?.split('/').pop() as string,
      initialConnectURL:
        path.join(req.parsed.pathname, req.parsed.search) || '',
      isTempDataDir: false,
      launchOptions,
      numbConnected: 1,
      resolver: noop,
      routePath: req.parsed.pathname,
      startedOn: Date.now(),
      ttl: 0,
      userDataDir: session.userDataDir,
    };

    this.browsers.set(browser, browserlessSession);

    await this.hooks.browser({ browser, req });

    return browser;
  }

  public getBrowserForRequest(
    req: AugmentedRequest,
    router: BrowserHTTPRoute | BrowserWebsocketRoute,
    logger: Logger,
  ): Promise<BrowserInstance> {
    const bqlReconnect =
      (router.path.includes(EnterpriseRoutes.chromiumBQL) ||
        router.path.includes(EnterpriseRoutes.chromeBQL)) &&
      getURLLastSegment(req.parsed) !== 'bql';

    if (router.path.includes(EnterpriseRoutes.reconnect) || bqlReconnect) {
      logger.trace('Inbound reconnect request', req.url);
      const id = getURLLastSegment(req.parsed)!;
      const reconnect = fileSystem.getId(id);
      const found = Array.from(this.browsers).find(([b]) =>
        b.wsEndpoint()?.includes(reconnect?.value ?? id),
      );

      if (!found) {
        throw new NotFound(
          `Couldn't locate browser/session "${id}" for request "${req.parsed.pathname}"`,
        );
      }

      if (reconnect?.meta?.auth) {
        const requestToken = getTokenFromRequest(req);
        if (requestToken !== reconnect.meta.auth) {
          throw new Unauthorized('Request was not properly authorized');
        }
      }

      const [browser, session] = found;

      ++session.numbConnected;
      this.log.debug(`Located browser with ID ${id}`);
      return Promise.resolve(browser);
    }

    return BrowserManager.prototype.getBrowserForRequest.call(
      this,
      req,
      router,
      logger,
    );
  }
}

import fs from 'fs';
import path from 'path';

const dlURL =
  'https://raw.githubusercontent.com/wanhose/cookie-dialog-monster/refs/heads/main/database.json';
const dataDir = path.join('.', 'build', 'data');

const res = await fetch(dlURL)
  .then((r) => {
    if (!r.ok) {
      throw new Error(`Non OK response: [${r.status}]: ${r.statusText}`);
    }
    return r;
  })
  .catch((err) => {
    console.error('Failed to fetch selectors: ' + err);
    process.exit(1);
  });

/** @constant
    @type {{ classes: string[]; selectors: string[] }}
    @default
*/
const json = await res.json().catch((err) => {
  console.error('Failed to deserialize selectors: ' + err);
  process.exit(1);
});

if (!fs.existsSync(dataDir)) {
  await fs.promises.mkdir(dataDir, { recursive: true });
}

await fs.promises
  .writeFile(
    path.join(dataDir, `selectors.json`),
    JSON.stringify(json.tokens.selectors),
  )
  .catch((err) => {
    console.error('Failed to write selectors: ' + err);
    process.exit(1);
  });

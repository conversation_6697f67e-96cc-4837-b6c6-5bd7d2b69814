import debug from 'debug';
import { Protocol } from 'puppeteer-core';

import { Context } from '../context.js';
import { BadRequest, sleep } from '@browserless.io/browserless';
import { serial } from '../utils.js';
import {
  operators,
  PDFOptions,
  ScreenshotOpts,
  solveTypes,
  verifyTypes,
  waitUntil,
} from '../types.js';

const log = debug(`bql:resolvers`);

const resolvers = () => {
  const api = {
    preferences: async (args: { timeout?: number }, context: Context) => {
      log('preferences', args);
      const browser = await context.getBrowser();
      return await browser.preferences(args);
    },

    proxy: async (
      args: {
        country?: string;
        city?: string;
        state?: string;
        sticky?: boolean;
        server: string;
        method?: string[];
        operator: operators;
        type?: string[];
        url?: string[];
      },
      context: Context,
    ) => {
      log('proxy', args);

      if (
        args.server &&
        (args.country || args.city || args.state || args.sticky)
      ) {
        throw new BadRequest(
          `Cannot use "server" with "country", "state", "city" or "sticky"`,
        );
      }

      const browser = await context.getBrowser();
      return await browser.proxy({
        country: args.country,
        city: args.city,
        state: args.state,
        sticky: args.sticky,
        server: args.server,
        method: args.method,
        operator: args.operator,
        type: args.type,
        url: args.url,
      });
    },

    solve: async (
      args: { type?: solveTypes; timeout?: number; wait: boolean },
      context: Context,
    ) => {
      log('solve', args);
      const browser = await context.getBrowser();
      return await browser.solveCaptchaAutoDetect({
        type: args.type,
        wait: args.wait,
        timeout: args.timeout,
      });
    },

    verify: async (
      args: { type: verifyTypes; timeout?: number; wait: boolean },
      context: Context,
    ) => {
      log('verify', args);
      const browser = await context.getBrowser();
      return await browser.verify({
        type: args.type,
        wait: args.wait,
        timeout: args.timeout,
      });
    },

    hover: async (
      args: {
        selector?: string;
        x?: number;
        y?: number;
        scroll: boolean;
        wait: boolean;
        visible: boolean;
        timeout?: number;
      },
      context: Context,
    ) => {
      log('hover', args);
      const browser = await context.getBrowser();
      return await browser.hover({
        selector: args.selector,
        x: args.x,
        y: args.y,
        wait: args.wait,
        scroll: args.scroll,
        visible: args.visible,
        timeout: args.timeout,
      });
    },

    click: async (
      args: {
        selector: string;
        scroll: boolean;
        wait: boolean;
        visible: boolean;
        timeout?: number;
      },
      context: Context,
    ) => {
      log('click', args);
      const browser = await context.getBrowser();
      return await browser.click({
        selector: args.selector,
        wait: args.wait,
        scroll: args.scroll,
        visible: args.visible,
        timeout: args.timeout,
      });
    },

    cookies: async (
      args: { cookies: Protocol.Network.CookieParam[] },
      context: Context,
    ) => {
      log('cookies', args);
      const browser = await context.getBrowser();
      return await browser.cookies({ cookies: args.cookies });
    },

    setExtraHTTPHeaders: async (
      args: { headers: Record<string, string> },
      context: Context,
    ) => {
      log('setExtraHTTPHeaders', args);
      const browser = await context.getBrowser();
      return await browser.setExtraHTTPHeaders({ headers: args.headers });
    },

    userAgent: async (args: { userAgent: string }, context: Context) => {
      log('userAgent', args);
      const browser = await context.getBrowser();
      return await browser.userAgent({ userAgent: args.userAgent });
    },

    screenshot: async (args: ScreenshotOpts, context: Context) => {
      log('screenshot', args);
      const browser = await context.getBrowser();

      return await browser.screenshot({
        selector: args.selector,
        options: args,
      });
    },

    pdf: async (args: PDFOptions, context: Context) => {
      log('pdf', args);

      // ensure that these params are either strings or numbers
      const scalarParams = [
        'width',
        'height',
        'marginTop',
        'marginBottom',
        'marginLeft',
        'marginRight',
      ];

      for (const param of scalarParams) {
        if (
          (args as any)[param] &&
          typeof (args as any)[param] !== 'string' &&
          typeof (args as any)[param] !== 'number'
        ) {
          throw new BadRequest(`"${param}" must be a string or a number`);
        }
      }

      const browser = await context.getBrowser();
      return await browser.pdf({
        options: args,
      });
    },

    javaScriptEnabled: async (args: { enabled: boolean }, context: Context) => {
      log('javaScriptEnabled', args);
      const browser = await context.getBrowser();
      return await browser.javaScriptEnabled(args);
    },

    content: async (
      args: { html: string; waitUntil: waitUntil },
      context: Context,
    ) => {
      log('content', args);
      const browser = await context.getBrowser();
      return await browser.content({
        html: args.html,
        waitUntil: args.waitUntil,
      });
    },

    liveURL: async (
      args: {
        timeout?: number;
        interactable: boolean;
        type: string;
        quality: number;
        resizable: boolean;
      },
      context: Context,
    ) => {
      if (args.type !== 'jpeg' && args.quality < 100) {
        throw new BadRequest(`"type" must be "jpeg" when setting a quality`);
      }

      if (args.quality < 1 || args.quality > 100) {
        throw new BadRequest(`"quality" must be between "1" or "100"`);
      }
      log('liveURL', args);

      const browser = await context.getBrowser();
      return browser.liveURL({
        timeout: args.timeout,
        interactable: args.interactable,
        type: args.type,
        quality: args.quality,
        resizable: args.resizable,
      });
    },

    if: async (
      args: {
        selector: any;
        visible: boolean;
        request?: { url: string; method: string };
        response?: { codes: number[]; statuses: number[]; url: string };
      },
      context: Context,
    ): Promise<unknown | null> => {
      log('if', args);
      const browser = await context.getBrowser();

      if (args.selector) {
        const res = await browser.getBoundingClientRect({
          selector: args.selector,
          visible: args.visible,
        });
        if (res) {
          return serialized;
        }
      }

      if (args.request) {
        if (
          browser.hasMadeRequest({
            urls: args.request.url ? [args.request.url] : undefined,
            methods: args.request.method ? [args.request.method] : undefined,
            operator: 'or',
          })
        ) {
          return serialized;
        }
      }

      if (args.response) {
        const statuses = args.response?.statuses.length
          ? args.response.statuses
          : args.response?.codes.length
            ? args.response.codes
            : undefined;
        if (
          browser.hasReceivedResponse({
            urls: args.response.url ? [args.response.url] : undefined,
            statuses,
            operator: 'or',
          })
        ) {
          return serialized;
        }
      }

      return null;
    },

    ifnot: async (args: any, context: Context) => {
      log('ifnot', args);
      if (!(await api.if(args, context))) {
        return serialized;
      }
      return null;
    },

    forward: async (
      args: { waitUntil: waitUntil; timeout?: number },
      context: Context,
    ) => {
      log('forward', args);
      const browser = await context.getBrowser();
      return await browser.forward({
        waitUntil: args.waitUntil,
        timeout: args.timeout,
      });
    },

    back: async (
      args: { waitUntil: waitUntil; timeout?: number },
      context: Context,
    ) => {
      log('back', args);
      const browser = await context.getBrowser();
      return await browser.back({
        waitUntil: args.waitUntil,
        timeout: args.timeout,
      });
    },

    goto: async (
      args: { url: string; waitUntil?: waitUntil; timeout?: number },
      context: Context,
    ) => {
      log('goto', args);
      const browser = await context.getBrowser();
      return await browser.goto(args);
    },

    evaluate: async (
      args: { content?: string; url?: string; timeout?: number },
      context: Context,
    ) => {
      log('evaluate', args);
      if (!args.content && !args.url) {
        throw new BadRequest(`One of "content" or "url" is required`);
      }
      const browser = await context.getBrowser();
      return await browser.evaluate({
        content: args.content,
        url: args.url,
        timeout: args.timeout,
      });
    },

    checkbox: async (
      args: {
        selector: string;
        checked: boolean;
        scroll: boolean;
        wait: boolean;
        visible: boolean;
        timeout?: number;
      },
      context: Context,
    ) => {
      log('checkbox', args);
      const browser = await context.getBrowser();
      return await browser.checkbox({
        selector: args.selector,
        checked: args.checked,
        wait: args.wait,
        scroll: args.scroll,
        visible: args.visible,
        timeout: args.timeout,
      });
    },

    scroll: async (
      args: {
        selector?: string;
        x?: number;
        y?: number;
        visible: boolean;
        timeout?: number;
        wait: boolean;
      },
      context: Context,
    ) => {
      log('scroll', args);
      const browser = await context.getBrowser();
      return await browser.scroll({
        selector: args.selector,
        x: args.x,
        y: args.y,
        visible: args.visible,
        timeout: args.timeout,
        wait: args.wait,
      });
    },

    text: async (
      args: {
        clean?: {
          removeNonTextNodes: boolean;
          removeAttributes: boolean;
          removeRegex: boolean;
          regexes: string[];
          selectors: string[];
          attributes?: string[];
        };
        selector?: string;
        visible: boolean;
        timeout?: number;
      },
      context: Context,
    ) => {
      log('text', args);
      const browser = await context.getBrowser();
      return await browser.text({
        clean: args.clean,
        selector: args.selector,
        visible: args.visible,
        timeout: args.timeout,
      });
    },

    mapSelector: async (
      args: { selector: string; timeout?: number; wait: boolean },
      context: Context,
    ) => {
      log('mapSelector', args);
      const browser = await context.getBrowser();
      return await browser.mapSelector({
        selector: args.selector,
        wait: args.wait,
        timeout: args.timeout,
      });
    },

    html: async (
      args: {
        clean?: {
          removeNonTextNodes: boolean;
          removeAttributes: boolean;
          removeRegex: boolean;
          regexes: string[];
          selectors: string[];
          attributes?: string[];
        };
        selector?: string;
        visible: boolean;
        timeout?: number;
      },
      context: Context,
    ) => {
      log('html', args);
      const browser = await context.getBrowser();
      return await browser.html({
        clean: args.clean,
        selector: args.selector,
        visible: args.visible,
        timeout: args.timeout,
      });
    },

    type: async (
      args: {
        text: string;
        selector: string;
        delay: [number, number];
        wait: boolean;
        scroll: boolean;
        visible: boolean;
        timeout?: number;
        interactable?: boolean;
      },
      context: Context,
    ) => {
      log('type', args);
      const browser = await context.getBrowser();
      return await browser.type({
        text: args.text,
        selector: args.selector,
        delay: args.delay,
        wait: args.wait,
        scroll: args.scroll,
        visible: args.visible,
        timeout: args.timeout,
        interactable: args.interactable,
      });
    },

    querySelector: async (
      args: { selector: string; timeout?: number; visible: boolean },
      context: Context,
    ) => {
      log('querySelector', args);
      const browser = await context.getBrowser();
      return browser.querySelector(args);
    },

    querySelectorAll: async (
      args: { selector: string; timeout?: number; visible: boolean },
      context: Context,
    ) => {
      log('querySelector', { selector: args.selector });
      const browser = await context.getBrowser();
      return browser.querySelectorAll(args);
    },

    waitForTimeout: async (args: { time: number }) => {
      log('waitForTimeout', args);
      const start = Date.now();
      await sleep(args.time);
      return { time: Date.now() - start };
    },

    waitForNavigation: async (
      args: { waitUntil: waitUntil; timeout?: number },
      context: Context,
    ) => {
      log('waitForNavigation', args);
      const browser = await context.getBrowser();
      return await browser.waitForNavigation(args.waitUntil, args.timeout);
    },

    waitForRequest: async (
      args: { url: string; method?: string; timeout?: number },
      context: Context,
    ) => {
      const browser = await context.getBrowser();
      const start = Date.now();
      const [{ url }] = await browser.waitForRequest({
        urls: args.url ? [args.url] : undefined,
        methods: args.method ? [args.method] : undefined,
        operator: 'or',
        timeout: args.timeout,
      });

      return {
        time: Date.now() - start,
        url,
      };
    },

    waitForResponse: async (
      args: {
        url: string;
        codes?: number[];
        statuses?: number[];
        timeout?: number;
      },
      context: Context,
    ) => {
      const browser = await context.getBrowser();
      const start = Date.now();
      const statuses = args.statuses?.length
        ? args.statuses
        : args.codes?.length
          ? args.codes
          : undefined;

      const [{ url, status }] = await browser.waitForResponse({
        urls: args.url ? [args.url] : undefined,
        statuses,
        operator: 'or',
        timeout: args.timeout,
      });

      return {
        time: Date.now() - start,
        url,
        status,
      };
    },

    waitForSelector: async (
      args: {
        selector: string;
        visible: boolean;
        timeout?: number;
      },
      context: Context,
    ) => {
      log('waitForSelector', args);
      const start = Date.now();

      const browser = await context.getBrowser();
      const result = await browser.waitForSelector({
        selector: args.selector,
        timeout: args.timeout,
        visible: args.visible,
      });

      return {
        time: Date.now() - start,
        selector: args.selector,
        x: result.x,
        y: result.y,
        width: result.width,
        height: result.height,
      };
    },

    request: async (
      args: {
        method?: string[];
        type?: string[];
        operator: operators;
        timeout?: number;
        url?: string[];
        wait: boolean;
      },
      context: Context,
    ) => {
      log('request', args);
      const browser = await context.getBrowser();
      return await browser.request({
        method: args.method,
        operator: args.operator,
        timeout: args.timeout,
        type: args.type,
        url: args.url,
        wait: args.wait,
      });
    },

    response: async (
      args: {
        status?: number[];
        method?: string[];
        operator: operators;
        timeout?: number;
        type?: string[];
        url?: string[];
        wait: boolean;
      },
      context: Context,
    ) => {
      log('response', args);
      const browser = await context.getBrowser();
      return await browser.response({
        status: args.status,
        method: args.method,
        operator: args.operator,
        timeout: args.timeout,
        type: args.type,
        url: args.url,
        wait: args.wait,
      });
    },

    title: async (_args: any, context: Context) => {
      log('title');
      const browser = await context.getBrowser();
      const { value: title } = await browser.title();

      return {
        title,
      };
    },

    url: async (args: {}, context: Context) => {
      log('url', args);
      const browser = await context.getBrowser();
      const { value: url } = await browser.url();

      return {
        url,
      };
    },

    reconnect: async (args: { timeout: number }, context: Context) => {
      log('reconnect', args);
      const browser = await context.getBrowser();
      return await browser.reconnect(args.timeout);
    },

    reject: async (
      args: {
        enabled: boolean;
        url?: string[];
        method?: string[];
        type?: string[];
        operator: operators;
      },
      context: Context,
    ) => {
      log('reject', args);
      const browser = await context.getBrowser();
      return await browser.reject({
        enabled: args.enabled,
        method: args.method,
        operator: args.operator,
        type: args.type,
        url: args.url,
      });
    },

    select: async (
      args: {
        selector: string;
        value: string | string[];
        wait: boolean;
        scroll: boolean;
        visible: boolean;
        timeout?: number;
      },
      context: Context,
    ) => {
      log('querySelector', { selector: args.selector });

      if (typeof args.value !== 'string' && !Array.isArray(args.value)) {
        throw new BadRequest('value must be a string or an array of strings');
      }

      const browser = await context.getBrowser();
      return browser.select(args);
    },

    viewport: async (
      args: {
        width: number;
        height: number;
        deviceScaleFactor?: number;
        mobile?: boolean;
      },
      context: Context,
    ) => {
      log('viewport', args);
      const browser = await context.getBrowser();
      return await browser.viewport(args);
    },
  };

  const serialized = Object.entries(api).reduce((allApis, [api, handler]) => {
    const s = serial();
    allApis[api] = s(handler);
    return allApis;
  }, {} as any) as typeof api;

  return serialized;
};

export default resolvers;

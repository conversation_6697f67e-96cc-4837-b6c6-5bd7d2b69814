import fs from 'fs/promises';
import path from 'path';

const root = process.cwd();

export const loadSchema = async () => {
  const files = await fs.readdir(root, {
    recursive: true,
  });
  const gqlFilesPath = files.filter((f) => f.endsWith('.graphql'));
  const filesContents = await Promise.all(
    gqlFilesPath.map((filePath) =>
      fs.readFile(path.join(root, filePath), 'utf-8'),
    ),
  );

  return filesContents.join('\n');
};

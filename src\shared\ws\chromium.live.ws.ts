import {
  WebSocketRoute,
  Request,
  APITags,
  BadRequest,
  dedent,
  Logger,
} from '@browserless.io/browserless';
import { Duplex } from 'stream';
import { WebSocket, WebSocketServer } from 'ws';

import { LiveServer } from '../utils/live.server.js';
import { EnterpriseRoutes } from '../../paths.js';
import EnterpriseBrowserManager from '../../browser-manager.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';
import fileSystem from '../../file-system.js';
import { logLiveUrlVisited } from '../browserql/utils/url-logging.js';
import { getURLLastSegment, isMobile } from '../../utils.js';

export default class ChromiumLiveWebsocketRoute extends WebSocketRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromiumLiveWebsocketRoute;
  description = dedent(`
    > This API is only available for Enterprise plans and self-hosted Enterprise plans. [Contact us for more information here.](https://www.browserless.io/contact/), or [sign-up here](https://www.browserless.io/pricing/).

    Websocket back-end that powers the live session experience.
  `);
  path = [EnterpriseRoutes.live, EnterpriseRoutes.chromiumLive];
  tags = [APITags.browserWS];
  auth = false;
  handler = async (
    req: Request,
    socket: Duplex,
    head: Buffer,
    logger: Logger,
  ): Promise<void> =>
    new Promise(async (resolve, reject) => {
      const id = getURLLastSegment(req.parsed);

      if (!id) {
        return reject(new BadRequest(`An ID path-parameter is required.`));
      }

      const browserManager = this.browserManager() as EnterpriseBrowserManager;
      const liveLocation = fileSystem.getId(id);

      if (!liveLocation) {
        return reject(new BadRequest(`Bad Request.`));
      }

      const [browserId, pageId] = liveLocation.value.split('.');
      const { type, quality, interactable, resizable } = liveLocation.meta as {
        type: 'jpeg' | 'png';
        quality: number;
        interactable: boolean;
        resizable: boolean;
      };
      const browser = browserManager.getBrowserById(browserId);

      if (!browser) {
        return reject(new BadRequest(`Bad Request.`));
      }

      const wsEndpoint = browser.wsEndpoint();

      if (!wsEndpoint) {
        return reject(new BadRequest(`Bad Request.`));
      }

      const client = (await new Promise((r) =>
        new WebSocketServer({
          noServer: true,
          perMessageDeflate: true,
        }).handleUpgrade(req, socket, head, (ws) => r(ws)),
      )) as WebSocket;

      const server = new LiveServer({
        client,
        browser,
        pageId,
        interactable,
        type,
        quality,
        resizable,
      }).once('close', resolve);

      const apiKey = browser.getApiKey();

      if (apiKey) {
        const config = browser.getConfig();
        const mobile = isMobile(req.headers['user-agent']);
        logLiveUrlVisited({
          token: apiKey,
          isMobile: mobile,
          type,
          quality,
          interactable,
          liveURLId: id,
          config,
          logger,
        });
      }

      fileSystem.on('deleteId', (deletedId: string) => {
        if (deletedId === id) {
          logger.debug(`Closing down live URL session ${id}`);
          server.close();
          resolve();
        }
      });
    });
}

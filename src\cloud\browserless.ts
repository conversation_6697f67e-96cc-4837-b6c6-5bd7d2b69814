import {
  Browserless,
  createLogger,
  getSourceFiles,
} from '@browserless.io/browserless';
import EventSource from 'eventsource';
import path from 'path';

import BrowserManager from '../browser-manager.js';
import fileSystem from '../file-system.js';
import disabledSharedRoutes from './disabled-routes.js';

import { CloudHooks } from './hooks.js';
import { CloudUnitConfig } from './config.js';
import { CloudCredits } from './credit.js';
import { CloudAPI } from './api.js';
import { ServerEvents } from './server-events.js';
import { CloudLogger } from './logger.js';
import { SessionEndedEventsPublisher } from '../shared/utils/session-ended-publisher.js';

export const IP_ROYAL_PW = process.env.IP_ROYAL_PW;
export const API_TOKEN = process.env.API_TOKEN;
export const API_URL = process.env.API_URL;
const EXTERNAL_BASE_URL = process.env.EXTERNAL_BASE_URL;
const EXTERNAL_ENCRYPTION_KEY = process.env.EXTERNAL_ENCRYPTION_KEY;

export default class BrowserlessCloud {
  private browserless: Browserless;
  public cwd = process.cwd();

  constructor({
    config: configOverride,
    credits: creditsOverride,
    api: apiOverride,
    hooks: hooksOverride,
    serverEvents: serverEventsOverride,
    cloudLogger: loggerOverride,
    sessionEndedPublisher: sessionEndedPublisherOverride,
  }: {
    config?: CloudUnitConfig;
    credits?: CloudCredits;
    api?: CloudAPI;
    hooks?: CloudHooks;
    serverEvents?: EventSource;
    cloudLogger?: typeof CloudLogger;
    sessionEndedPublisher?: SessionEndedEventsPublisher;
  } = {}) {
    if (!apiOverride) {
      if (!API_TOKEN || !API_URL) {
        throw new Error(
          `Missing one of API_TOKEN=${API_TOKEN} or API_URL=${API_URL} variables.`,
        );
      }
    }

    if (!hooksOverride) {
      if (!IP_ROYAL_PW) {
        throw new Error(
          `Missing IP_ROYAL_PW=${IP_ROYAL_PW}, which needs to be defined`,
        );
      }
    }

    if (!sessionEndedPublisherOverride) {
      if (
        !process.env.AWS_ACCESS_KEY_ID ||
        !process.env.AWS_SECRET_ACCESS_KEY
      ) {
        throw new Error(
          'AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY env vars are required to publish session ended events',
        );
      }
    }

    const baseURL = API_URL as string;
    const token = API_TOKEN as string;

    const api = apiOverride ?? new CloudAPI(baseURL, token);
    const serverEvents =
      serverEventsOverride ?? new ServerEvents(`${baseURL}/events`, token);
    const credits = creditsOverride ?? new CloudCredits(api, serverEvents);
    const config = configOverride ?? new CloudUnitConfig(credits);
    const sessionEndedPublisher =
      sessionEndedPublisherOverride ??
      new SessionEndedEventsPublisher(
        config.getSessionEndedEventsQueueUrl(),
        config.getSessionEndedEventsQueueRegion(),
      );
    const hooks =
      hooksOverride ??
      new CloudHooks(
        config,
        credits,
        api,
        config.getProxyPassword()!,
        sessionEndedPublisher,
      );
    const browserManager = new BrowserManager(config, hooks, fileSystem);
    const Logger = loggerOverride ?? CloudLogger;

    if (EXTERNAL_BASE_URL && EXTERNAL_ENCRYPTION_KEY) {
      config.setExternalEncryptedAddress(
        EXTERNAL_BASE_URL,
        EXTERNAL_ENCRYPTION_KEY,
      );
    }

    this.browserless = new Browserless({
      browserManager,
      config,
      fileSystem,
      hooks,
      Logger,
    });

    this.browserless.setStaticSDKDir(path.join(this.cwd, 'static'));
    this.browserless.disableRoutes(...disabledSharedRoutes);
    fileSystem.setupExpiredSessionsPoll();
  }

  public start = async () => {
    const log = createLogger('enterprise');

    log(`Starting Browserless`);

    const { httpRoutes, webSocketRoutes } = await getSourceFiles(this.cwd);

    httpRoutes.forEach((r) => this.browserless.addHTTPRoute(r));
    webSocketRoutes.forEach((r) => this.browserless.addWebSocketRoute(r));

    log(`Starting Browserless HTTP Service`);
    await this.browserless.start();
  };

  public stop = async () => {
    return this.browserless.stop();
  };
}

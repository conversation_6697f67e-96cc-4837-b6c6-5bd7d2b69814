import {
  Hooks,
  Request,
  convertIfBase64,
  getTokenFromRequest,
  safeParse,
  writeR<PERSON>ponse,
  sleep,
  AfterR<PERSON>ponse,
  PageHook,
  createLogger,
  BrowserHook,
} from '@browserless.io/browserless';
import { ConnectionStats, Server } from 'proxy-chain';
import micromatch from 'micromatch';
import { Socket } from 'net';

import {
  AugmentedRequest,
  BeforeHook,
  ISharedFleetPutPayload,
  SessionEndedEvent,
  SharedFleetStat,
} from '../types.js';
import { CloudCredits } from './credit.js';
import {
  allowedCountries,
  buildBlessObject,
  CALLED_BASED_SECOND_LIMIT,
  convertMsToSeconds,
  FIVE_MINUTES_IN_MS,
  getProxyUnitsUsed,
  isIntrospection,
} from '../utils.js';
import { CloudAPI } from './api.js';
import { CloudUnitConfig } from './config.js';
import { SessionEndedEventsPublisher } from '../shared/utils/session-ended-publisher.js';
import { detectProgrammingLanguage } from '../shared/utils/parse-language.js';
import { ProxyingEngine } from '../shared/3rd-party/proxies/ProxyingEngine.js';

export class CloudHooks extends Hooks {
  protected log = createLogger('cloud-hooks');
  protected callBasedSecondLimit = CALLED_BASED_SECOND_LIMIT;
  protected currentUsageStats: ISharedFleetPutPayload = {};
  protected sessionsEventsOnHold: { [key: string]: SessionEndedEvent } = {};
  protected allowedProxyTypes = ['residential'];
  protected saveInterval: number = process.env.SAVE_INTERVAL
    ? +process.env.SAVE_INTERVAL
    : FIVE_MINUTES_IN_MS; // 5-Minutes in MS
  protected intervalId = setInterval(
    this.persistMetrics.bind(this),
    this.saveInterval,
  );
  protected MAX_SESSION_TIME = 30 * 60 * 1000;
  protected DEFAULT_SESSION_TIME = FIVE_MINUTES_IN_MS * 3; // 15-Minutes in MS
  protected statusToIndexMap = {
    successful: 1,
    timedout: 2,
    error: 3,
  };
  protected ALLOWED_FLAGS = [
    '--disable-features',
    '--disable-setuid-sandbox',
    '--disable-site-isolation-trials',
    '--disable-web-security',
    '--disable-web-security',
    '--enable-features',
    '--font-render-hinting',
    '--force-color-profile',
    '--lang',
    '--proxy-bypass-list',
    '--proxy-server',
    '--window-size',
    'blockAds',
    'humanlike',
    'blockConsentModals',
    'headless',
    'ignoreDefaultArgs',
    'ignoreHTTPSErrors',
    'launch',
    'record',
    'slowMo',
    'stealth',
    'timeout',
    'token',

    // Custom proxy requests
    'proxy',
    'proxyCountry',
    'proxySticky',
    'proxyCity',
    'proxyState',
  ];

  // These are not understood by downstream browserless core
  // and need to be remove prior to allowing the request to continue
  protected CLOUD_ONLY_QUERY_PARAMS = [
    'proxy',
    'proxyCountry',
    'proxySticky',
    'proxyCity',
    'proxyState',
  ];

  protected nonTokenURLs = [
    // live URLs on any protocol. ws://live/1234 and https://live/index.html match.
    /^[a-zA-Z]+:\/\/[^\/]+\/live\/.*/,

    // http(s)://host/client.js
    /^https?:\/\/[^\/]+\/client\.js$/,

    // http(s) Devtools resources
    /^https?:\/\/[^\/]+\/devtools\/.*/,
  ];

  // https://en.wikipedia.org/wiki/Private_network
  // https://serverfault.com/questions/427018/what-is-this-ip-address-169-254-169-254
  protected blockedIPPatterns = [
    // Localhost/loopback/etc.
    'localhost',
    '127.',

    // https://www.iana.org/help/abuse-answers
    // Special-Use IPs
    '172.16.',
    '172.17.',
    '172.18.',
    '172.19.',
    '172.20.',
    '172.21.',
    '172.22.',
    '172.23.',
    '172.24.',
    '172.25.',
    '172.26.',
    '172.27.',
    '172.28.',
    '172.29.',
    '172.30.',
    '172.31.',

    // AWS and cloud-hosted IPs
    '169.254',
  ];

  protected blockedProtocols = ['file://', 'smtp://', 'ftp://'];

  protected urlDenyList = [
    ...this.blockedProtocols,
    ...this.blockedIPPatterns.flatMap((ip) => [
      `http://${ip}`,
      `https://${ip}`,
      `ws://${ip}`,
      `wss://${ip}`,
    ]),
  ];

  protected urlAllowList = [
    '/favicon.ico',
    '/function/index.html',
    '/function/client.js',
    '/function/favicon.',
    '/function/browserless-function-*.js',
  ];

  protected proxyingEngine: ProxyingEngine;

  constructor(
    protected config: CloudUnitConfig,
    protected credit: CloudCredits,
    protected api: CloudAPI,
    protected ipRoyalPW: string,
    protected sessionEndedPublisher: SessionEndedEventsPublisher,
  ) {
    super();
    this.proxyingEngine = new ProxyingEngine(this.config);
  }

  protected async persistMetrics() {
    const start = Date.now();
    const hasRanRecentTraffic = Object.keys(this.currentUsageStats).length;

    try {
      // If no new sessions have ran then just update credits
      if (!hasRanRecentTraffic) {
        const newCredits = await this.api.getCloudCredits();
        this.log(`Successfully loaded new credits in ${Date.now() - start}ms`);
        this.credit.set(newCredits);
      } else {
        const newCredits = await this.api.saveCloudMetrics(
          this.currentUsageStats,
        );
        this.log(`Successfully saved usage in ${Date.now() - start}ms`);
        this.credit.set(newCredits);
        this.currentUsageStats = {};
      }
      await this.sessionEndedPublisher.publishFailedEntries();
    } catch (error: any) {
      this.log('Metrics failed to save', error.message);
      return;
    }
  }

  protected recordMetrics({
    token,
    endpoint,
    userAgent,
    clientIp,
    sessionId,
    status,
    seconds,
    captchas,
    proxy,
    eventType,
    errorType,
    errorDescription,
    eventWaitForProxyUnits = false,
    requestType,
  }: {
    token: string;
    endpoint?: string;
    sessionId: string;
    userAgent?: string;
    clientIp?: string;
    status?: AfterResponse['status'];
    seconds?: number;
    captchas?: number;
    proxy?: number;
    eventType: 'Browser Session Ended' | 'Proxy Session Ended';
    errorType?: string;
    errorDescription?: string;
    eventWaitForProxyUnits?: boolean;
    requestType?: string;
  }) {
    const now = Date.now();
    const secondsUsed = seconds ?? 0;
    const captchaUnits = typeof captchas === 'number' ? captchas * 10 : 0;
    const proxyUnits = proxy ?? 0;
    const timeUnits = Math.ceil(secondsUsed / this.callBasedSecondLimit);

    // Initialize API-token if not present in stats payload
    this.currentUsageStats[token] =
      this.currentUsageStats[token] ??
      ([0, 0, 0, 0, 0, 0, 0, 0] as SharedFleetStat);

    // Update status (indices 1 - 3)
    if (status) {
      const statusIndex = this.statusToIndexMap[status];
      this.currentUsageStats[token][statusIndex]++;
    }

    // Update seconds and time units
    this.currentUsageStats[token][0] += secondsUsed;

    // Set date
    this.currentUsageStats[token][4] = now;

    // Track time-units used
    this.currentUsageStats[token][5] += timeUnits;

    // Update proxy units consumed
    this.currentUsageStats[token][6] += proxyUnits;

    // Calculate units consumed for captchas a 10x multiple
    this.currentUsageStats[token][7] += captchaUnits;

    const totalUnitsUsed = [timeUnits, captchaUnits, proxyUnits].reduce(
      (a, b) => a + b,
      0,
    );

    this.log(`${token} current stats: ${this.currentUsageStats[token]}`);
    this.credit.decrby(token, totalUnitsUsed);
    const sessionEvent: SessionEndedEvent = {
      session_id: eventType === 'Browser Session Ended' ? now : undefined,
      time: now,
      platform: userAgent,
      ip: clientIp,
      event_type: eventType,
      event_properties: {
        seconds: seconds,
        time_units: seconds
          ? Math.ceil(seconds / this.callBasedSecondLimit)
          : undefined,
        proxy_units: proxy,
        captcha_units: captchas ? captchas * 10 : undefined,
        total_units: totalUnitsUsed,
        status,
        endpoint,
        user_agent: userAgent,
        programming_language: detectProgrammingLanguage(userAgent),
        request_id: sessionId,
        token: token,
        worker_id: this.config.getWorkerId(),
        error_type: errorType,
        error_description: errorDescription,
        request_type: requestType,
        browserless_version: 'v2',
      },
    };

    if (eventType === 'Browser Session Ended' && eventWaitForProxyUnits) {
      this.sessionsEventsOnHold[sessionId] = sessionEvent;
      // send session if proxy units are not received after 15 seconds
      setTimeout(() => {
        const session = this.sessionsEventsOnHold[sessionId];
        if (session) {
          delete this.sessionsEventsOnHold[sessionId];
          this.sessionEndedPublisher.publishEvents([session]);
        }
      }, 15 * 1000);
    } else if (eventType === 'Proxy Session Ended') {
      const browserSession = this.sessionsEventsOnHold[sessionId];
      if (browserSession) {
        delete this.sessionsEventsOnHold[sessionId];
        browserSession.event_properties.proxy_units = proxy;
        browserSession.event_properties.total_units =
          (browserSession.event_properties.total_units || 0) + (proxy || 0);
        this.sessionEndedPublisher.publishEvents([browserSession]);
      } else {
        this.sessionEndedPublisher.publishEvents([sessionEvent]);
      }
    } else {
      this.sessionEndedPublisher.publishEvents([sessionEvent]);
    }
  }

  protected isURLAllowed = (path: string) =>
    this.urlAllowList.some((pattern) => micromatch.isMatch(path, pattern));

  protected logConnectionFailure(
    request: AugmentedRequest,
    writeable: any,
    statusCode: 400 | 401 | 404 | 408 | 429 | 500 | 200 | 204,
    errorMessage: string,
    errorType: string,
    errorDescription: string,
    _token?: string, // Unused parameter, prefixed with underscore
  ): boolean {
    writeResponse(writeable, statusCode, errorMessage);

    const url = `http://0.0.0.0${request.url || request.__bless__.originalUrl}`;
    const endpoint = new URL(url).pathname;
    const userAgent = request.headers['user-agent'];
    const clientIp = (request.headers['x-real-ip'] ||
      request.socket.remoteAddress) as string;
    const token = getTokenFromRequest(request);

    this.sessionEndedPublisher.publishEvents([
      {
        session_id: +request.__bless__.requestId,
        time: Date.now(),
        platform: userAgent,
        ip: clientIp,
        event_type: 'Connection Failed',
        event_properties: {
          error_type: errorType,
          error_description: errorDescription,
          endpoint,
          user_agent: userAgent,
          request_id: request.__bless__.requestId,
          worker_id: this.config.getWorkerId(),
          token: token || '',
        },
      },
    ]);

    return false;
  }

  async before({ req, res, socket }: BeforeHook): Promise<boolean> {
    const writeable = res || socket;
    const parsedURL = new URL(req.url || '', `http://${req.headers.host}`);
    let request = req as unknown as Request;
    request.parsed = parsedURL;
    request = await buildBlessObject(request);

    const token = getTokenFromRequest(request);
    const tokenIsRequired =
      !this.nonTokenURLs.some((regex) => regex.test(parsedURL.toString())) ||
      req.method?.toLowerCase() === 'options';

    if (!writeable) {
      throw new Error(`Internal error: couldn't locate a writeable socket`);
    }

    if (tokenIsRequired) {
      if (!token) {
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          401,
          'Please sign-up for an account at https://www.browserless.io/pricing/',
          'MissingToken',
          'No token provided',
        );
      }

      if (this.credit.getPlanNumber(token) === -1) {
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          401,
          'Invalid API key. Please check your API key and try again.',
          'InvalidToken',
          'Invalid API key, it must be a valid cloud-unit API key',
          token,
        );
      }

      if (!this.credit.hasCredits(token)) {
        let errorMessage =
          'Your account is run out of credits, please upgrade your plan https://account.browserless.io/';

        if (this.config.isFreePlan(token)) {
          errorMessage =
            "You've reached the units usage limit allowed under our free plan, please upgrade to a paid plan at https://account.browserless.io/";
        } else if (this.credit.isCloudUnitAccount(token)) {
          errorMessage =
            'Your account has exceeded its cloud unit allocation and is now in overage, please upgrade your plan at https://account.browserless.io/';
        }

        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          401,
          errorMessage,
          'NoCredits',
          'Account out of credits',
          token,
        );
      }

      if (!this.credit.isCloudUnitAccount(token)) {
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          401,
          'This request requires a cloud-unit account. <NAME_EMAIL> to help convert your account.',
          'WrongAccountType',
          'Not a cloud-unit account',
          token,
        );
      }
    }

    const maxTimeAllowed = this.config.getMaxConnectionTime(
      token,
      this.DEFAULT_SESSION_TIME,
    );

    const specifiedTimeout =
      parsedURL.searchParams.get('timeout') ?? maxTimeAllowed.toString();
    const parsedTimeout = +specifiedTimeout;

    if (
      !Number.isInteger(parsedTimeout) ||
      parsedTimeout <= 0 ||
      parsedTimeout > maxTimeAllowed
    ) {
      return this.logConnectionFailure(
        request as unknown as AugmentedRequest,
        writeable,
        400,
        `Timeout must be a integer between 1 and ${maxTimeAllowed.toLocaleString()} seconds`,
        'InvalidTimeout',
        `Invalid timeout "${specifiedTimeout}" (must be between 1 and ${maxTimeAllowed.toLocaleString()} seconds)`,
      );
    }

    parsedURL.searchParams.set('timeout', parsedTimeout.toString());

    // Delete all the non-whitelisted keys
    parsedURL.searchParams.forEach((_value, param) => {
      if (!this.ALLOWED_FLAGS.includes(param)) {
        parsedURL.searchParams.delete(param);
      }
    });

    // Handle stripping out launch param args as well
    if (parsedURL.searchParams.has('launch')) {
      const launchParams = convertIfBase64(
        parsedURL.searchParams.get('launch') || '{}',
      );
      const launch = safeParse(launchParams) as { args: string[] };
      if (!launch) {
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          400,
          'Bad or invalid "launch" argument. Must be a JSON-encoded or a base64 JSON encoded value.',
          'InvalidLaunch',
          'Invalid launch argument',
        );
      }

      launch.args = (launch.args ?? []).filter((a) =>
        this.ALLOWED_FLAGS.includes(a),
      );
      parsedURL.searchParams.set(
        'launch',
        Buffer.from(JSON.stringify(launch)).toString('base64'),
      );
    }

    // Handle Proxy stuff here
    if (
      parsedURL.searchParams.has('--proxy-server') &&
      !this.config.allowThirdPartyProxy(token!)
    ) {
      return this.logConnectionFailure(
        request as unknown as AugmentedRequest,
        writeable,
        401,
        'Only paid cloud-unit plans can utilize a third-party proxy.',
        'InvalidProxy',
        '3rd party proxy not allowed',
      );
    }
    if (tokenIsRequired && parsedURL.searchParams.has('proxy')) {
      // Add comprehensive logging for proxy setup
      this.log(`PROXY SETUP DETECTED:`, {
        proxyType: req.__bless__.proxyType,
        proxyCountry: req.__bless__.proxyCountry,
        proxyState: req.__bless__.proxyState,
        proxyCity: req.__bless__.proxyCity,
        proxySticky: req.__bless__.proxySticky,
        originalUrl: req.__bless__.originalUrl,
        token: `${token?.substring(0, 8)}...`,
      });

      if (parsedURL.searchParams.has('--proxy-server')) {
        this.log(`PROXY CONFLICT: Both 'proxy' and '--proxy-server' detected`);
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          400,
          'Only one of "proxy" or "--proxy-server" are allowed.',
          'InvalidProxy',
          'Only one of "proxy" or "--proxy-server" are allowed.',
        );
      }

      if (!this.allowedProxyTypes.includes(req.__bless__.proxyType)) {
        this.log(`PROXY TYPE INVALID: ${req.__bless__.proxyType}`);
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          400,
          `Only "${this.allowedProxyTypes.join(', ')}" are allowed.`,
          'InvalidProxy',
          `Invalid proxy type: ${req.__bless__.proxyType}`,
        );
      }

      if (req.__bless__.proxyCountry) {
        if (req.__bless__.proxyType !== 'residential') {
          this.log(
            `PROXY COUNTRY ERROR: proxyCountry only allowed for residential`,
          );
          return this.logConnectionFailure(
            request as unknown as AugmentedRequest,
            writeable,
            400,
            `'proxyCountry' is only allowed for residential proxying.`,
            'InvalidProxy',
            `'proxyCountry' is only allowed for residential proxying.`,
          );
        }

        if (
          !allowedCountries.includes(req.__bless__.proxyCountry.toLowerCase())
        ) {
          this.log(`PROXY COUNTRY DISALLOWED: ${req.__bless__.proxyCountry}`);
          return this.logConnectionFailure(
            request as unknown as AugmentedRequest,
            writeable,
            400,
            `'proxyCountry' of "${req.__bless__.proxyCountry}" is not allowed.`,
            'InvalidProxy',
            `Proxy Country ${req.__bless__.proxyCountry} not allowed`,
          );
        }
      }

      if (req.__bless__.proxyCity && !this.config.allowCityProxying(token!)) {
        this.log(`CITY PROXYING NOT ALLOWED for plan`);
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          401,
          `Your plan doesn't support city-level proxying.`,
          'InvalidProxy',
          'City level proxying not allowed',
        );
      }

      if (req.__bless__.proxyState && !this.config.allowStateProxying(token!)) {
        this.log(`STATE PROXYING NOT ALLOWED for plan`);
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          401,
          `Your plan doesn't support state-level proxying.`,
          'InvalidProxy',
          'State level proxying not allowed',
        );
      }

      if (
        (req.__bless__.proxyCity || req.__bless__.proxyState) &&
        !parsedURL.searchParams.get('proxyCountry')
      ) {
        this.log(`MISSING COUNTRY for city/state proxying`);
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          400,
          `In order to proxy by city or state you must specify a country with the "proxyCountry" query-parameter.`,
          'InvalidProxy',
          `In order to proxy by city or state you must specify a country with the "proxyCountry" query-parameter.`,
        );
      }

      try {
        const proxyServerArg = `http://0.0.0.0:${req.__bless__.proxyPort}`;
        parsedURL.searchParams.set('--proxy-server', proxyServerArg);
        this.log(`PROXY CONFIGURED SUCCESSFULLY:`, {
          proxyPort: req.__bless__.proxyPort,
          proxyServerArg,
          finalUrl: parsedURL.href,
        });
      } catch (error) {
        this.log(`PROXY PORT ALLOCATION FAILED:`, error);
        return this.logConnectionFailure(
          request as unknown as AugmentedRequest,
          writeable,
          500,
          'Failed to allocate proxy port',
          'ProxyPortError',
          `Failed to allocate proxy port: ${error}`,
        );
      }
    }

    this.CLOUD_ONLY_QUERY_PARAMS.forEach((param) => {
      parsedURL.searchParams.delete(param);
    });

    // Finalize any query-parameters
    req.url = parsedURL.href;
    return true;
  }

  async after({
    start,
    req,
    status,
    error,
  }: AfterResponse & { error?: Error }) {
    const now = Date.now();
    const seconds = convertMsToSeconds(now - start);
    const request = req as unknown as AugmentedRequest;
    const token = getTokenFromRequest(request);
    const captchas = request.__bless__.captchasSolved;
    const isApiRequest = request.url?.startsWith('http');
    const isBqlRequest = request.url?.includes('/bql');
    const url = request.url || `http://0.0.0.0${request.__bless__.originalUrl}`;
    const endpoint = new URL(url).pathname;
    const userAgent = request.headers['user-agent'];
    const clientIp = (request.headers['x-real-ip'] ||
      request.socket.remoteAddress) as string;
    const sessionId = request.__bless__.requestId;
    const isIntrospectionQuery = isIntrospection(request);

    let errorType, errorDescription;
    if (status === 'error' && error) {
      errorType = error.name || 'UnknownError';
      errorDescription = error.message || 'Unknown error occurred';
    }

    if (!token) {
      return true;
    }

    if (isIntrospectionQuery) {
      this.log(`Ignoring introspection query. Not recording as metrics.`);
      return true;
    }

    const eventWaitForProxyUnits = (request.__bless__.proxyPort || 0) > 0;
    this.recordMetrics({
      token,
      status,
      seconds,
      captchas,
      proxy: request.__bless__.bqlProxyBytes
        ? getProxyUnitsUsed(request.__bless__.bqlProxyBytes)
        : undefined,
      endpoint,
      userAgent,
      clientIp,
      sessionId,
      errorType,
      errorDescription,
      eventType: 'Browser Session Ended',
      eventWaitForProxyUnits,
      requestType: isBqlRequest
        ? 'BQL'
        : isApiRequest
          ? 'Rest API v2'
          : 'BaaS v2',
    });

    return true;
  }

  public async browser({
    browser,
    req,
  }: {
    browser: BrowserHook['browser'];
    req: AugmentedRequest;
  }) {
    if (req.__bless__.proxyPort) {
      try {
        this.log(
          `STARTING PROXY SERVER SETUP for port ${req.__bless__.proxyPort}`,
        );
        let bytesUsed = 0;
        const upstreamProxyUrl = this.proxyingEngine
          .chooseVendor(req.__bless__)
          .getProxyURL(req.__bless__);

        this.log(`UPSTREAM PROXY URL GENERATED:`, {
          upstreamProxyUrl: upstreamProxyUrl.replace(/\/\/.*@/, '//***:***@'), // Hide credentials
          country: req.__bless__.proxyCountry,
          city: req.__bless__.proxyCity,
          state: req.__bless__.proxyState,
          sticky: req.__bless__.proxySticky,
        });

        const proxyServer = new Server({
          port: req.__bless__.proxyPort,
          prepareRequestFunction: () => {
            this.log(`PROXY REQUEST INTERCEPTED - routing through upstream`);
            return {
              requestAuthentication: false,
              upstreamProxyUrl,
            };
          },
        })
          .on('connectionClosed', ({ stats }: { stats: ConnectionStats }) => {
            const sent = Math.max(stats.srcRxBytes ?? 0, stats.trgTxBytes ?? 0);
            const received = Math.max(
              stats.srcTxBytes ?? 0,
              stats.trgRxBytes ?? 0,
            );
            bytesUsed += sent + received;
            this.log(
              `PROXY CONNECTION CLOSED - bytes used: ${sent + received}`,
            );
          })
          .on('error', (error) => {
            this.log(`PROXY SERVER ERROR:`, error);
          });

        this.log(`STARTING PROXY SERVER on port ${req.__bless__.proxyPort}...`);
        await proxyServer.listen();
        this.log(`PROXY SERVER LISTENING on port ${req.__bless__.proxyPort}`);

        const waitForTunnelReady = new Promise((resolve) => {
          this.log(`WAITING FOR TUNNEL CONNECTION...`);
          const onTunnelConnectResponded = ({ socket }: { socket: Socket }) => {
            if (proxyServer.stats.connectRequestCount > 1 && bytesUsed > 0) {
              this.log(
                `TUNNEL CONNECTION ESTABLISHED - continuing with ${proxyServer.stats.connectRequestCount} connections`,
              );
              proxyServer.off(
                'tunnelConnectResponded',
                onTunnelConnectResponded,
              );
              resolve(true);
            } else {
              this.log(`TESTING TUNNEL CONNECTION - sending test request`);
              // Force a test request to ensure the tunnel is ready
              socket.write('HEAD / HTTP/1.1\r\nHost: google.com\r\n\r\n');
            }
          };

          proxyServer.on('tunnelConnectResponded', onTunnelConnectResponded);
        });

        browser.once('close', async () => {
          this.log(
            `BROWSER CLOSED - cleaning up proxy server. Total bytes: ${bytesUsed}`,
          );
          await sleep(1000);
          const token = getTokenFromRequest(req) as string;
          const proxy = getProxyUnitsUsed(bytesUsed);
          const sessionId = req.__bless__.requestId;

          proxyServer.close(false);
          proxyServer.removeAllListeners();
          this.recordMetrics({
            token,
            proxy,
            sessionId,
            eventType: 'Proxy Session Ended',
          });
        });

        await waitForTunnelReady;
        this.log(`PROXY SETUP COMPLETE AND READY`);
      } catch (error) {
        this.log(`CRITICAL PROXY SETUP FAILURE:`, error);
        throw error; // Re-throw to fail the request if proxy setup fails
      }
    } else {
      this.log(`NO PROXY REQUESTED - proceeding with direct connection`);
    }
  }

  public async page({ page }: PageHook) {
    if (page) {
      page.on('request', (request) => {
        if (request.isInterceptResolutionHandled()) return;
        const { pathname } = new URL(request.url());
        if (this.isURLAllowed(pathname)) {
          this.log(`Allowing request: ${request.url()} due to whitelist`);
          return;
        }

        if (this.urlDenyList.some((url) => request.url().startsWith(url))) {
          this.log(`Blocking request: ${request.url()} due to blacklist`);
          page.browser().close();
        }
      });

      page.on('response', (response) => {
        const responseUrl = response.url();
        const remoteAddressIP = response.remoteAddress().ip;
        const { pathname } = new URL(responseUrl);
        if (this.isURLAllowed(pathname)) {
          return;
        }

        if (
          responseUrl &&
          this.urlDenyList.some((url) => responseUrl.startsWith(url))
        ) {
          this.log(`Blocking response: ${responseUrl} due to denyList`);
          page.browser().close();
        }

        if (
          remoteAddressIP &&
          this.blockedIPPatterns.some((ip) => remoteAddressIP.startsWith(ip))
        ) {
          this.log(`Blocking response: ${remoteAddressIP} due to blacklist`);
          page.browser().close();
        }
      });
    }

    return true;
  }

  stop = async () => {
    this.log('Stop called. Shutting down.');
    clearInterval(this.intervalId);
    this.credit.stop();
    await this.persistMetrics();
  };
}

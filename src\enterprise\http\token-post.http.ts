import { ServerResponse } from 'http';

import {
  contentTypes,
  Request,
  Methods,
  APITags,
  HTTPRoute,
  Unauthorized,
  getTokenFromRequest,
  jsonResponse,
  ServerError,
  dedent,
  Logger,
} from '@browserless.io/browserless';

import {
  BrowserlessEnterpriseRoutes,
  BrowserlessToken,
  Permissions,
} from '../../types.js';
import tokens from '../token.js';
import config from '../../config.js';
import { EnterpriseRoutes } from '../../paths.js';

export interface BodySchema {
  /**
   * An optional label for the token to aide in tracking it
   */
  label?: string;

  /**
   * The permission you wish to grant to this token
   */
  permission: Permissions;

  /**
   * An optional string for the token itself, otherwise
   * browserless will generate a UUID (Version 4) string
   */
  token?: string;
}

export type ResponseSchema = BrowserlessToken;

export default class TokenPostRoute extends HTTPRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseTokenPostRoute;
  accepts = [contentTypes.json];
  auth = false;
  browser = null;
  concurrency = false;
  contentTypes = [contentTypes.json];
  description = dedent(`
    > This API is only available for Enterprise hosted and self-hosted plans. [Contact us for more information here.](https://www.browserless.io/contact/)

    Allows for creation of a new token with a given permission set. Responds with the newly created token. In order for this route to =work you must start browserless with a TOKEN parameter.
  `);
  method = Methods.post;
  path = EnterpriseRoutes.token;
  tags = [APITags.management];
  async handler(
    req: Request,
    res: ServerResponse,
    _logger: Logger,
  ): Promise<void> {
    if (!config.getToken()) {
      throw new ServerError(
        `Browserless must be started with a root token, or TOKEN property, in order to create sub-tokens.`,
      );
    }

    const { permission, token, label } = req.body as BodySchema;
    const authorizedBy = getTokenFromRequest(req);
    if (!authorizedBy) {
      throw new Unauthorized(`Bad or invalid token`);
    }
    const response: ResponseSchema = await tokens.createNewToken({
      authorizedBy,
      label,
      permission,
      token,
    });

    return jsonResponse(res, 200, response);
  }
}

import { WebsocketRoutes } from '@browserless.io/browserless';
import { EnterpriseChromeCDP } from '../../browsers/core.js';
import { BrowserlessEnterpriseRoutes } from '../../types.js';

import ChromiumWebsocketRoute from './chromium.cdp.ws.js';

export { QuerySchema } from './chromium.cdp.ws.js';

export default class ChromeWebsocketRoute extends ChromiumWebsocketRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromeCDPWebSocketRoute;
  browser = EnterpriseChromeCDP;
  path = [WebsocketRoutes.chrome];
}

// Initialize storage with empty recording state on startup.
// Since the extension loads from a clean state, we are using onInstalled.
chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.local.set({ recordings: {}, tabIds: {} });
});

class Recorder {
  async start(opts) {
    await chrome.tabs.query(
      { active: true, lastFocusedWindow: true, currentWindow: true },
      async function (tabs) {
        // Get current tab to focus on it after start recording on vhs extension tab
        const currentTab = tabs[0];

        // Create vhs extension tab
        const tab = await chrome.tabs.create({
          url: chrome.runtime.getURL('recorder.html'),
          pinned: true,
          active: true,
        });

        // Save tab ID to storage. We need to persist the ID because the service worker is ephemeral.
        const storageData = await chrome.storage.local.get(['tabIds']);
        const tabIds = storageData.tabIds || {};
        tabIds[opts.id] = tab.id;
        await chrome.storage.local.set({ tabIds });

        // Wait for vhs extension tab to be loaded and request startRecording message
        chrome.tabs.onUpdated.addListener(async function listener(tabId, info) {
          if (tabId === tab.id && info.status === 'complete') {
            chrome.tabs.onUpdated.removeListener(listener);

            await chrome.tabs.sendMessage(tabId, {
              name: 'startRecording',
              body: {
                tabId,
                currentTab: currentTab,
                recordingId: opts.id,
              },
            });
          }
        });
      },
    );
  }

  async stop({ id }) {
    return new Promise(async (resolve, reject) => {
      // Get tab ID from storage
      const storageData = await chrome.storage.local.get(['tabIds']);
      const tabIds = storageData.tabIds || {};
      const tabId = tabIds[id];

      if (!tabId) {
        return reject(
          new Error(`No recorder has started, did you forget to start?`),
        );
      }

      await chrome.tabs.query(
        { active: true, lastFocusedWindow: true, currentWindow: true },
        async function (tabs) {
          // Get current tab to focus on it after starting recording on recording screen tab
          const currentTab = tabs[0];

          await chrome.tabs.sendMessage(tabId, {
            name: 'stopRecording',
            body: {
              tabId,
              currentTab: currentTab,
              recordingId: id,
            },
          });
        },
      );

      // We have to keep track of the pending stop because the service worker is ephemeral,
      // and a started recording may not be stopped before the service worker is restarted.
      const pendingStops = (await chrome.storage.local.get([
        'pendingStops',
      ])) || { pendingStops: {} };
      if (pendingStops.pendingStops) {
        pendingStops.pendingStops[id] = { resolve, timestamp: Date.now() };
        await chrome.storage.local.set(pendingStops);
      }
    });
  }
}

// Handle short-lived messages from service workers
chrome.runtime.onMessage.addListener((msg, _sender, sendResponse) => {
  const { type } = msg;

  if (type === 'REC_START') {
    handleRecordingStart(msg, sendResponse);
    return true;
  }

  if (type === 'REC_STOP') {
    handleRecordingStop(msg, sendResponse);
    return true;
  }
});

async function handleRecordingStart(msg, sendResponse) {
  const storageData = await chrome.storage.local.get(['recordings']);
  const recordings = storageData.recordings || {};

  if (recordings[msg.id]) {
    sendResponse({ id: msg.id, message: 'REC_ALREADY_STARTED' });
    return;
  }

  // Mark this recording as in progress
  recordings[msg.id] = { startTime: Date.now() };
  await chrome.storage.local.set({ recordings });

  try {
    const recorder = new Recorder();
    await recorder.start(msg);
    sendResponse({ id: msg.id, message: 'REC_STARTED' });
  } catch (err) {
    // Clean up on error
    delete recordings[msg.id];
    await chrome.storage.local.set({ recordings });

    sendResponse({
      error: err.message,
      id: msg.id,
      message: 'REC_START_FAIL',
    });
  }
}

async function handleRecordingStop(msg, sendResponse) {
  const { id } = msg;
  const storageData = await chrome.storage.local.get(['recordings']);
  const recordings = storageData.recordings || {};

  if (!recordings[id]) {
    sendResponse({
      id,
      error: 'Recording did not start',
      message: 'REC_NOT_STARTED',
    });
    return;
  }

  try {
    const recorder = new Recorder();
    recorder.stop(msg);
  } catch (err) {
    // Clean up on error, so we don't leave any dangling recordings
    delete recordings[id];
    await chrome.storage.local.set({ recordings });

    sendResponse({
      error: err.message,
      id,
      message: 'REC_STOP_FAIL',
    });
  }
}

import {
  <PERSON>rowser<PERSON>,
  create<PERSON>ogger,
  getSourceFiles,
} from '@browserless.io/browserless';
import path from 'path';

import BrowserManager from '../browser-manager.js';
import defaultConfig, { EnterpriseConfig } from './config.js';
import fileSystem from '../file-system.js';
import token from './token.js';
import EnterpriseHooks from './hooks.js';
import { EnterpriseAPI } from './api.js';

export const API_TOKEN = process.env.ENTERPRISE_API_TOKEN;
export const API_URL = process.env.API_URL;

export default class BrowserlessEnterprise {
  private browserless: Browserless;
  public cwd = process.cwd();

  constructor({
    config: configOverride,
    api: apiOverride,
    hooks: hooksOverride,
  }: {
    config?: EnterpriseConfig;
    api?: EnterpriseAPI;
    hooks?: EnterpriseHooks;
  } = {}) {
    const config = configOverride ?? defaultConfig;

    if (!apiOverride) {
      if (!API_TOKEN || !API_URL) {
        console.warn(
          `Missing one of ENTERPRISE_API_TOKEN="${API_TOKEN}" or API_URL="${API_URL}" variables. No usage will persist.`,
        );
      }
    }

    if (!hooksOverride) {
      if (!config.getProxyPassword()) {
        console.warn(
          `No PROXY_PASSWORD was defined, residential proxying won't work as expected.`,
        );
      }
    }

    const baseURL = API_URL;
    const apiToken = API_TOKEN;
    const proxyPassword = config.getProxyPassword();
    const proxyUsername = config.getProxyUsername();

    const api = apiOverride ?? new EnterpriseAPI(baseURL, apiToken);
    const hooks = new EnterpriseHooks(
      config,
      api,
      proxyUsername,
      proxyPassword,
    );
    const browserManager = new BrowserManager(config, hooks, fileSystem);

    this.browserless = new Browserless({
      browserManager,
      config,
      fileSystem,
      token,
      hooks,
    });

    this.browserless.setStaticSDKDir(path.join(this.cwd, 'static'));
    fileSystem.setupExpiredSessionsPoll();
  }

  public start = async () => {
    const log = createLogger('enterprise');

    log(`Starting Browserless`);

    const { httpRoutes, webSocketRoutes } = await getSourceFiles(this.cwd);

    httpRoutes.forEach((r) => this.browserless.addHTTPRoute(r));
    webSocketRoutes.forEach((r) => this.browserless.addWebSocketRoute(r));

    log(`Starting Browserless HTTP Service`);
    await this.browserless.start();
  };

  public stop = async () => {
    return this.browserless.stop();
  };
}

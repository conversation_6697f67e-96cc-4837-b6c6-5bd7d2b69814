import fs from 'fs/promises';
import path from 'path';

(async () => {
  const cwd = process.cwd();
  const blessPackageJSONLocation = path.join(
    cwd,
    'node_modules',
    '@browserless.io',
    'browserless',
    'package.json',
  );
  const packageJSONLocation = path.join(cwd, 'package.json');
  const browserJSONLocation = path.join(
    cwd,
    'node_modules/playwright-core/browsers.json',
  );

  const [{ browsers }, packageJSON, blessPackageJSON] = await Promise.all([
    JSON.parse(await fs.readFile(browserJSONLocation, 'utf-8')),
    JSON.parse(await fs.readFile(packageJSONLocation, 'utf-8')),
    JSON.parse(await fs.readFile(blessPackageJSONLocation, 'utf-8')),
  ]);

  const chromium = browsers.find((b) => b.name === 'chromium').browserVersion;
  const firefox = browsers.find((b) => b.name === 'firefox').browserVersion;
  const webkit = browsers.find((b) => b.name === 'webkit').browserVersion;
  const playwrightCore = blessPackageJSON.dependencies[
    'playwright-core'
  ].replace(/(\*|\^|\>|\=|\<|~)/gi, '');

  const puppeteer = blessPackageJSON.dependencies['puppeteer-core'].replace(
    /(\*|\^|\>|\=|\<|~)/gi,
    '',
  );
  const playwright =
    playwrightCore +
    '-' +
    Object.keys(blessPackageJSON.playwrightVersions)
      .filter((v) => v !== 'default')
      .join('-');

  const browserVersions = Object.entries({
    puppeteer,
    playwright,
    chromium,
    firefox,
    webkit,
  })
    .reduce(
      (versions, [name, version]) => versions.concat(`${name}-${version}`),
      [],
    )
    .join('_');

  console.log(`browserless="${packageJSON.version}"`);
  console.log(`puppeteer="${puppeteer}"`);
  console.log(`playwright="${playwright}"`);
  console.log(`chromium="Chromium=${chromium}"`);
  console.log(`firefox="Firefox=${firefox}"`);
  console.log(`webkit="WebKit=${webkit}"`);
  console.log(`tag=${packageJSON.version}_${browserVersions}`);
  console.log(`version=${packageJSON.version}`);
})();

#!/usr/bin/env node
'use strict';

import fs from 'fs/promises';
import path from 'path';
import { build } from 'esbuild';
import { exists, createLogger } from '@browserless.io/browserless';

(async () => {
  const debug = createLogger('build-client');
  const entryPoint = 'src/shared/utils/live.client.ts';
  const fullEntry = path.join(process.cwd(), entryPoint);
  const keyboardJS = path.join(
    process.cwd(),
    'src/shared/3rd-party/simple-keyboard/main.js',
  );
  const liveExists = await exists(fullEntry);
  const keyboardJSExists = await exists(keyboardJS);

  if (!liveExists) {
    debug(`Couldn't locate client entrypoint: "${fullEntry}"`);
    debug(`Exiting...`);
    return;
  }

  if (!keyboardJSExists) {
    debug(`Couldn't locate virtual keyboard module: "${keyboardJS}"`);
    debug(`Building without it...`);
  } else {
    debug(`Copying virtual keyboard module to build...`);
    await fs.mkdir('build/shared/3rd-party/simple-keyboard', {
      recursive: true,
    });
    await fs.copyFile(
      keyboardJS,
      'build/shared/3rd-party/simple-keyboard/main.js',
    );
  }

  debug('Building Live Client');
  await build({
    bundle: true,
    entryPoints: [entryPoint],
    outfile: 'static/live/client.js',
  });
  debug(`Live client built!`);
})();

name: GraphQL & OpenAPI

on:
  push:
    branches:
      - main

jobs:
  build-and-pr:
    runs-on: ubuntu-latest
    env:
      GH_USER: ${{ secrets.GHCR_USERNAME }}
      GH_TOKEN: ${{ secrets.GHCR_PASSWORD }}
    steps:
      - uses: actions/checkout@v2

      - name: Install Modules
        run: npm i

      - name: Build Package
        run: npm run build && npx browserless build

      - name: Configure Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Schemas Bot"
          git config --global --add --bool push.autoSetupRemote true

      - name: Clone Docs Repo
        run: |
          git clone https://${{ env.GH_USER }}:${{ env.GH_TOKEN }}@github.com/browserless/browserless-docs.git ./docs-repo

      - name: Copy File to Docs Repo
        run: |
          cp ./node_modules/@browserless.io/browserless/static/docs/swagger.json ./docs-repo/schemas

      - name: Copy GQL Schema to Docs Repo
        run: |
          cp ./mutations.graphql ./docs-repo/schemas

      - name: Commit and Push
        run: |
          cd ./docs-repo
          git status
          ls
          git checkout -b docs/openapi
          git add ./schemas/swagger.json ./schemas/mutations.graphql
          if git diff --cached --quiet; then
            echo "No changes to the output on this push; exiting."
            exit 0
          fi

          git commit -m "chore: update openapi"
          SHA=$(git rev-parse --short HEAD)
          git branch -m docs/openapi-$SHA
          git push -u origin HEAD
          gh pr create -B main -H docs/openapi-$SHA --title "🤖 Update Schemas [$SHA]" --body "🤖 Created by Github action 🤖"

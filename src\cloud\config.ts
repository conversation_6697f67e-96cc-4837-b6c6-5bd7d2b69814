import { SharedConfig } from '../config.js';
import { cloudUnitPlanNumbers } from '../types.js';
import { CloudCredits } from './credit.js';

export class CloudUnitConfig extends SharedConfig {
  constructor(private credits: CloudCredits) {
    super();
  }

  protected twoHundredTierAndAbove(apiKey: string) {
    const planNum = this.credits.getPlanNumber(apiKey);
    return planNum >= cloudUnitPlanNumbers.oneHundredEightyThousand;
  }

  protected fiveHundredTierAndAbove(apiKey: string) {
    const planNum = this.credits.getPlanNumber(apiKey);
    return planNum >= cloudUnitPlanNumbers.fiveHundredThousand;
  }

  public allowLiveURLs(apiKey: string) {
    return this.twoHundredTierAndAbove(apiKey);
  }

  public allowReconnect(_apiKey: string) {
    return true;
  }

  public allowCaptchaSolving() {
    return true;
  }

  public allowCityProxying(apiKey: string) {
    return this.fiveHundredTierAndAbove(apiKey);
  }

  public allowStateProxying(apiKey: string) {
    return this.fiveHundredTierAndAbove(apiKey);
  }

  public allowRecord(apiKey: string) {
    return this.twoHundredTierAndAbove(apiKey);
  }

  public allowThirdPartyProxy(apiKey: string) {
    return this.credits.isPaidAccount(apiKey);
  }

  public isFreePlan(apiKey: string | null) {
    if (!apiKey) {
      return true;
    }
    const planNum = this.credits.getPlanNumber(apiKey);
    return planNum === cloudUnitPlanNumbers.free;
  }

  public getMaxConnectionTime(apiKey: string | null, defaultValue: number) {
    if (!apiKey) {
      return defaultValue;
    }
    const planNum = this.credits.getPlanNumber(apiKey);

    if (planNum >= cloudUnitPlanNumbers.oneMillion) {
      return 60_000 * 60 * 6;
    }

    if (planNum >= cloudUnitPlanNumbers.fiveHundredThousand) {
      return 60_000 * 60;
    }

    if (planNum >= cloudUnitPlanNumbers.oneHundredEightyThousand) {
      return 60_000 * 30;
    }

    if (planNum === cloudUnitPlanNumbers.free) {
      return 60_000;
    }

    return defaultValue;
  }

  public getMaxReconnectTime(apiKey: string | null) {
    if (!apiKey) {
      return 0;
    }
    const planNum = this.credits.getPlanNumber(apiKey);

    if (planNum === cloudUnitPlanNumbers.free) {
      return 10_000;
    }

    if (
      planNum > cloudUnitPlanNumbers.free &&
      planNum < cloudUnitPlanNumbers.oneHundredEightyThousand
    ) {
      return 30_000; // 30 seconds
    }

    if (planNum === cloudUnitPlanNumbers.oneHundredEightyThousand) {
      return 60_000;
    }

    if (planNum >= cloudUnitPlanNumbers.fiveHundredThousand) {
      return 60_000 * 5;
    }

    return 0;
  }

  public disableBlocklist() {
    return false;
  }

  public getMaxRetention(apiKey: string | null) {
    if (!apiKey) {
      return 0;
    }
    const planNum = this.credits.getPlanNumber(apiKey);

    if (planNum === cloudUnitPlanNumbers.free) {
      return 24 * 60 * 60 * 1000;
    }

    if (
      planNum > cloudUnitPlanNumbers.free &&
      planNum < cloudUnitPlanNumbers.oneHundredEightyThousand
    ) {
      return 7 * 24 * 60 * 60 * 1000;
    }

    if (planNum === cloudUnitPlanNumbers.oneHundredEightyThousand) {
      return 30 * 24 * 60 * 60 * 1000;
    }

    if (planNum >= cloudUnitPlanNumbers.fiveHundredThousand) {
      return 90 * 24 * 60 * 60 * 1000;
    }

    return 0;
  }
}

<head>
  <title>Browserless.io Live</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta charset="UTF-8" />
  <link rel="icon" href="./favicon-32x32.png" type="image/png" />
  <style>
    * {
      margin: 0;
      padding: 0;
    }
    body {
      background-color: #161415;
    }

    html,
    body {
      overscroll-behavior: none;
      overflow: hidden;
      height: 100%;
    }
    canvas {
      height: 100%;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch; /* for smooth scrolling */
    }
    .container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }
    .dialog {
      padding: 20px;
      position: absolute;
      top: 50%;
      left: 50%;
      border: 1px solid #454545;
      color: white;
      transform: translate(-50%, -50%);
      font-family: monospace;
      min-width: 300px;

      border-radius: 1em;
      box-shadow:
        rgba(0, 0, 0, 0) 0px 0px 0px 0px,
        rgba(0, 0, 0, 0) 0px 0px 0px 0px,
        rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
        rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
      background-color: rgb(34 34 34 / 90%);
      cursor: default;

      border-radius: 1em;
      box-shadow:
        rgba(0, 0, 0, 0) 0px 0px 0px 0px,
        rgba(0, 0, 0, 0) 0px 0px 0px 0px,
        rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
        rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
      background-color: rgb(34 34 34 / 90%);
      cursor: default;
    }
    .dialog h2 {
      padding-bottom: 10px;
      border-bottom: 1px solid rgb(69, 69, 69);
    }
    .dialog p {
      padding: 10px 0;
    }
  </style>
</head>
<body>
  <main class="container">
    <canvas id="browserless-screen" />
  </main>
  <script src="./client.js" type="module"></script>
</body>

import crypto from 'crypto';
import fs from 'fs/promises';
import { join } from 'path';

import graphql from 'graphql';
import getPort from 'get-port';

import {
  BrowserLauncherOptions,
  buildDir,
  id,
  Request,
  WebsocketRoutes,
} from '@browserless.io/browserless';
import micromatch from 'micromatch';
import { AugmentedRequest, RecaptchaWidgetInfo } from './types.js';
import { EnterpriseRoutes } from './paths.js';
import { recaptcha } from './shared/browserql/constants.js';

const dataDir = join(buildDir, 'data');
export const MS_TO_SECONDS = 1000;
export const BYTES_MULTIPLE = 1.15;
export const BYTES_IN_A_MB = 1000 * 1000;
export const UNITS_PER_MB = 6;
export const CALLED_BASED_SECOND_LIMIT = 30;
export const THREE_MINUTES_IN_MS = 3 * 60 * 1000;
export const FIVE_MINUTES_IN_MS = 5 * 60 * 1000;

/**
 * Generates a unique, hexadecimal string that's cryptographically
 * secure and URL-friendly.
 *
 * @param length The number of random bytes to generate
 * @returns {string} The random ID.
 */
export const randomID = (length: number) =>
  crypto.randomBytes(length).toString('hex');

export const convertMsToSeconds = (time: number) =>
  Math.ceil(time / MS_TO_SECONDS);

export const allowedCountries = [
  'ad',
  'ae',
  'af',
  'ag',
  'al',
  'am',
  'ao',
  'ar',
  'at',
  'au',
  'aw',
  'ax',
  'az',
  'ba',
  'bb',
  'bd',
  'be',
  'bf',
  'bg',
  'bh',
  'bi',
  'bj',
  'bm',
  'bn',
  'bo',
  'bq',
  'br',
  'bs',
  'bt',
  'bw',
  'by',
  'bz',
  'ca',
  'cd',
  'cg',
  'ch',
  'ci',
  'cl',
  'cm',
  'cn',
  'co',
  'cr',
  'cu',
  'cv',
  'cw',
  'cy',
  'cz',
  'de',
  'dj',
  'dk',
  'dm',
  'do',
  'dz',
  'ec',
  'ee',
  'eg',
  'es',
  'et',
  'fi',
  'fj',
  'fo',
  'fr',
  'ga',
  'gb',
  'gd',
  'ge',
  'gf',
  'gg',
  'gh',
  'gi',
  'gm',
  'gn',
  'gp',
  'gr',
  'gt',
  'gu',
  'gy',
  'hk',
  'hn',
  'hr',
  'ht',
  'hu',
  'id',
  'ie',
  'il',
  'im',
  'in',
  'iq',
  'ir',
  'is',
  'it',
  'je',
  'jm',
  'jo',
  'jp',
  'ke',
  'kg',
  'kh',
  'kn',
  'kr',
  'kw',
  'ky',
  'kz',
  'la',
  'lb',
  'lc',
  'li',
  'lk',
  'lr',
  'ls',
  'lt',
  'lu',
  'lv',
  'ly',
  'ma',
  'mc',
  'md',
  'me',
  'mg',
  'mk',
  'ml',
  'mm',
  'mn',
  'mo',
  'mq',
  'mr',
  'ms',
  'mt',
  'mu',
  'mv',
  'mw',
  'mx',
  'my',
  'mz',
  'na',
  'nc',
  'ng',
  'ni',
  'nl',
  'no',
  'np',
  'nz',
  'om',
  'pa',
  'pe',
  'pf',
  'pg',
  'ph',
  'pk',
  'pl',
  'pr',
  'ps',
  'pt',
  'py',
  'qa',
  're',
  'ro',
  'rs',
  'ru',
  'rw',
  'sc',
  'se',
  'sg',
  'si',
  'sk',
  'sl',
  'sm',
  'sn',
  'so',
  'sr',
  'ss',
  'st',
  'sv',
  'sx',
  'sy',
  'sz',
  'tc',
  'tg',
  'th',
  'tj',
  'tn',
  'tr',
  'tt',
  'tw',
  'tz',
  'ua',
  'ug',
  'us',
  'uy',
  'uz',
  'vc',
  've',
  'vg',
  'vi',
  'vn',
  'ws',
  'yt',
  'za',
  'zm',
  'zw',
];

export const getRandomArbitrary = (min: number, max: number) =>
  Math.round(Math.random() * (max - min) + min);

export const blockSelectors = async (): Promise<string[]> =>
  JSON.parse((await fs.readFile(`${dataDir}/selectors.json`)).toString());

export const modalBlocker = async (selectors: string[]): Promise<void> => {
  const target = document.body || document.documentElement;
  const options = { childList: true, subtree: true };

  const isValid = (node: unknown) =>
    node instanceof HTMLElement &&
    node.parentElement &&
    !['BODY', 'HTML'].includes(node.tagName);

  const buildObserver = () => {
    selectors.forEach((selector) => {
      const node = document.querySelector(selector);
      if (node) {
        node.outerHTML = '';
      }
    });

    return new MutationObserver((mutations, instance) => {
      instance.disconnect();
      allowOverflow();
      const nodes = mutations
        .map((mutation) => Array.from(mutation.addedNodes))
        .flat()
        .filter((node) => isValid(node));

      for (const node of nodes) {
        for (const selector of selectors) {
          if (node instanceof HTMLElement && node.matches(selector)) {
            node.outerHTML = '';
            break;
          }
        }
      }

      instance.observe(target, options);
    });
  };

  const allowOverflow = () => {
    const body = target;
    const html = document.documentElement;

    if (body) {
      body.style.setProperty('overflow-y', 'unset', 'important');
    }

    if (html) {
      html.style.setProperty('overflow-y', 'unset', 'important');
    }
  };
  const observer = buildObserver();
  observer.observe(target, options);
};

// Original identifyCaptcha function - keep unchanged for BaaS compatibility
export const identifyCaptcha = (): any => {
  // @ts-ignore
  if (window.___grecaptcha_cfg && window.___grecaptcha_cfg.clients) {
    // @ts-ignore
    const [cid] = Object.entries(window.___grecaptcha_cfg.clients).at(0);
    if (cid && cid >= 10000) {
      return 'recaptcha-v3';
    }

    if (
      document.querySelector('[data-sitekey]') &&
      !document.querySelector(
        'iframe[src^="https://www.google.com/recaptcha/api2/"]',
      )
    ) {
      return 'recaptcha-invisible';
    }

    return 'recaptcha';
  }

  if (
    [...document.querySelectorAll('script')].filter((script) =>
      script.src.includes('/puzzle/get_js/?k=PUZZLE_'),
    ).length > 0
  ) {
    return 'capy';
  }

  if (
    [...document.querySelectorAll('iframe')].filter(
      (iframe) =>
        iframe.getAttribute('data-e2e') === 'enforcement-frame' &&
        iframe.src.includes('https://client-api.arkoselabs.com/'),
    ).length > 0
  ) {
    return 'funcaptcha';
  }

  if (
    document.querySelector('[data-sitekey]') &&
    [...document.querySelectorAll('iframe')].filter((iframe) =>
      iframe.src.includes('frcapi.com/api/v2/captcha/widget'),
    ).length > 0
  ) {
    return 'friendlycaptcha';
  }

  if (document.querySelector('textarea[name=h-captcha-response]')) {
    return 'hcaptcha';
  }

  if (document.querySelector('input[name="cf-turnstile-response"]')) {
    return 'cloudflare';
  }

  // More robust geetest detection - check for actual geetest implementation
  if (
    // @ts-ignore
    (window.initGeetest4 && typeof window.initGeetest4 === 'function') ||
    // @ts-ignore
    (window.initGeetest && typeof window.initGeetest === 'function')
  ) {
    // Check for actual geetest elements (most reliable)
    const hasGeetestElements =
      document.querySelector('.geetest_radar_tip') ||
      document.querySelector('.geetest_holder') ||
      document.querySelector('.geetest_widget') ||
      document.querySelector('[data-geetest]') ||
      document.querySelector('.geetest_canvas_img') ||
      document.querySelector('.geetest_slider_button');

    // Check for legitimate geetest scripts (from geetest.com domain)
    // @ts-ignore
    const legitimateGeetestScripts = [
      ...document.querySelectorAll('script'),
    ].filter((script) => script.src && script.src.includes('geetest.com'));

    // Check for geetest API requests in network (look for gt and challenge parameters)
    // @ts-ignore
    const hasGeetestApiRequests =
      window.performance &&
      window.performance.getEntriesByType('resource').some((entry) => {
        try {
          if (
            entry.name &&
            (entry.name.includes('geetest.com') ||
              entry.name.includes('/gettype.php'))
          ) {
            const url = new URL(entry.name);
            const gt = url.searchParams.get('gt');
            const challenge = url.searchParams.get('challenge');
            return gt && challenge;
          }
        } catch (e) {
          // Invalid URL, skip
        }
        return false;
      });

    const hasGeetestConfig =
      // @ts-ignore
      window.gtConfig || window.geetestConfig || window.gt;

    // Only consider it geetest if we have strong evidence
    if (
      hasGeetestElements ||
      legitimateGeetestScripts.length > 0 ||
      hasGeetestApiRequests ||
      hasGeetestConfig
    ) {
      return 'geetest';
    }
  }

  if (
    [...document.querySelectorAll('script')].filter(
      (s) =>
        s.textContent?.indexOf('s_s_c_user_id') !== -1 &&
        s.textContent?.indexOf('s_s_c_web_server_sign') !== -1,
    ).length > 0
  ) {
    return 'keycaptcha';
  }

  if (
    // @ts-ignore
    window.leminCroppedCaptcha &&
    document.querySelector('div[id*=lemin-captcha-input-box]') &&
    [...document.querySelectorAll('script')].filter((s) =>
      s.src.includes('/captcha/v1/cropped/CROPPED_'),
    ).length > 0
  ) {
    return 'lemin';
  }

  if (
    document.querySelector('input[name=mtcaptcha-verifiedtoken]') &&
    // @ts-ignore
    window.mtcaptchaConfig
  ) {
    return 'mtcaptcha';
  }

  if (
    document.querySelector("input[name='smart-token']") &&
    [...document.querySelectorAll('script')].filter((s) =>
      s.src.includes('captcha-api.yandex.ru'),
    ).length > 0
  ) {
    return 'yandex';
  }

  if (
    // @ts-ignore
    window.CaptchaScript &&
    // @ts-ignore
    window.gokuProps &&
    document.querySelector('script[src*="awswaf.com"][src*="captcha.js"]')
  ) {
    return 'amazonWaf';
  }

  // Normal captcha detection - look for common patterns
  const normalCaptchaSelectors = [
    'img[class*="captcha"]',
    'img[alt*="captcha"]',
  ];

  for (const selector of normalCaptchaSelectors) {
    const captchaImg = document.querySelector(selector);

    if (captchaImg) {
      // Check if there's an associated input field
      const inputSelectors = [
        'input[name*="captcha"]',
        'input[id*="captcha"]',
        '.captcha input[type="text"]',
        '#captcha input[type="text"]',
        'input[name*="challenge"]',
        'input[id*="challenge"]',
        'input[name*="verification"]',
        'input[id*="verification"]',
        // 2captcha.com specific patterns
        'input[name="simpleCaptcha"]',
        'input[id*="simple-captcha"]',
      ];

      for (const inputSelector of inputSelectors) {
        const inputField = document.querySelector(inputSelector);

        if (inputField) {
          return 'normal';
        }
      }
    }
  }

  // Text captcha detection - look for text-based questions with input fields
  const textCaptchaQuestionSelectors = [
    '.captcha-question',
    '[class*="captcha-question"]',
    '[id*="captcha-question"]',
    '.text-captcha-question',
    '[class*="text-captcha"]',
    '[aria-label*="captcha question"]',
    '[data-captcha-question]',
    'label[for*="captcha"]',
    '.math-captcha',
    '[class*="math-captcha"]',
  ];

  // Check for text-based captcha questions
  for (const selector of textCaptchaQuestionSelectors) {
    const questionElement = document.querySelector(selector);
    if (questionElement && questionElement.textContent) {
      const questionText = questionElement.textContent.toLowerCase();
      // Look for common text captcha patterns
      const textCaptchaPatterns = [
        'what',
        'solve',
        'calculate',
        'answer',
        'math',
        'arithmetic',
        'add',
        'subtract',
        'multiply',
        'divide',
        '+',
        '-',
        '*',
        '/',
        '=',
        'equals',
        'sum',
        'difference',
        'product',
        'quotient',
      ];

      const hasTextCaptchaPattern = textCaptchaPatterns.some((pattern) =>
        questionText.includes(pattern),
      );

      if (hasTextCaptchaPattern) {
        // Verify there's an associated input field
        const textCaptchaInputSelectors = [
          'input[name*="captcha"]',
          'input[id*="captcha"]',
          'input[name*="answer"]',
          'input[id*="answer"]',
          'input[name*="question"]',
          'input[id*="question"]',
          'input[name*="challenge"]',
          'input[id*="challenge"]',
          'input[name*="verification"]',
          'input[id*="verification"]',
          'input[name*="text-captcha"]',
          'input[id*="text-captcha"]',
          'input[name*="math"]',
          'input[id*="math"]',
          '.captcha input[type="text"]',
          '.text-captcha input[type="text"]',
          '.math-captcha input[type="text"]',
        ];

        for (const inputSelector of textCaptchaInputSelectors) {
          const inputField = document.querySelector(inputSelector);
          if (inputField) {
            return 'textcaptcha';
          }
        }
      }
    }
  }

  // Alternative text captcha detection by scanning for question patterns in text
  const allTextElements = Array.from(document.querySelectorAll('*')).filter(
    (el) =>
      el.children.length === 0 &&
      el.textContent &&
      el.textContent.trim().length > 0,
  );

  for (const element of allTextElements) {
    const text = element.textContent?.toLowerCase() || '';

    // Look for mathematical expressions or questions
    const mathPatterns = [
      /what\s+is\s+\d+\s*[\+\-\*\/]\s*\d+/,
      /solve\s*:\s*\d+\s*[\+\-\*\/]\s*\d+/,
      /calculate\s*:\s*\d+\s*[\+\-\*\/]\s*\d+/,
      /\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\?/,
      /answer\s*:\s*\d+\s*[\+\-\*\/]\s*\d+/,
    ];

    const hasMatchingPattern = mathPatterns.some((pattern) =>
      pattern.test(text),
    );

    if (hasMatchingPattern) {
      // Check if there's a nearby input field
      const parent = element.closest(
        'form, div, section, .captcha, .text-captcha',
      );
      if (parent) {
        const nearbyInput = parent.querySelector(
          'input[type="text"], input:not([type])',
        );
        if (nearbyInput) {
          return 'textcaptcha';
        }
      }
    }
  }

  return 'unknown';
};

// New enhanced function for BQL with comprehensive reCAPTCHA + other CAPTCHA detection
// This will be tested and refined before potentially replacing identifyCaptcha
export const identifyCaptchaBQL = () => {
  // Include getRecaptchaWidgetInfo implementation directly to ensure it works in browser context
  // This copies the exact same logic from browser.utils.ts to avoid duplication while ensuring functionality
  const getRecaptchaWidgetInfo = () => {
    try {
      // Access the internal reCAPTCHA widget data
      // @ts-ignore
      const widget = window.___grecaptcha_cfg?.clients?.[0];
      if (!widget) return null;

      const info: {
        sitekey: string | null;
        version: 'v2' | 'v2_invisible' | 'v3';
        action: string | null;
        s: string | null; // data-s parameter for Google Service Captcha
        enterprise: boolean;
      } = {
        sitekey: null,
        version: 'v2',
        action: null,
        s: null,
        enterprise: false,
      };

      // Check if it's enterprise (following 2captcha extension approach)
      info.enterprise =
        (window as any).grecaptcha && (window as any).grecaptcha.enterprise
          ? true
          : false;

      // Determine if it's a badge (v3 or invisible v2)
      let isBadge = false;
      for (const k1 in widget) {
        if (typeof widget[k1] !== 'object') continue;
        for (const k2 in widget[k1]) {
          if (widget[k1][k2]?.classList?.contains('grecaptcha-badge')) {
            isBadge = true;
            break;
          }
        }
        if (isBadge) break;
      }

      // Determine version
      if (isBadge) {
        info.version = 'v3';
        // Check if it's invisible v2
        for (const k1 in widget) {
          const obj = widget[k1];
          if (typeof obj !== 'object') continue;
          for (const k2 in obj) {
            if (typeof obj[k2] === 'string' && obj[k2] === 'fullscreen') {
              info.version = 'v2_invisible';
              break;
            }
          }
          if (info.version === 'v2_invisible') break;
        }
      }

      // Extract sitekey, action, and data-s parameter
      for (const k1 in widget) {
        const obj = widget[k1];
        if (typeof obj !== 'object') continue;

        for (const k2 in obj) {
          if (obj[k2] === null || typeof obj[k2] !== 'object') continue;
          if (obj[k2].sitekey === undefined && obj[k2].action === undefined)
            continue;

          for (const k3 in obj[k2]) {
            if (k3 === 'sitekey') info.sitekey = obj[k2][k3];
            if (k3 === 'action') info.action = obj[k2][k3];
            if (k3 === 's') info.s = obj[k2][k3]; // Critical: data-s parameter
          }
        }
      }

      return info.sitekey ? info : null;
    } catch (error) {
      console.error('Error extracting reCAPTCHA widget info:', error);
      return null;
    }
  };

  // Step 1: Check for reCAPTCHA first using the robust detection
  const recaptchaInfo = getRecaptchaWidgetInfo();
  if (recaptchaInfo && recaptchaInfo.sitekey) {
    // Return specific reCAPTCHA type based on version and enterprise status
    if (recaptchaInfo.version === 'v3') {
      return recaptchaInfo.enterprise
        ? 'recaptcha-v3-enterprise'
        : 'recaptcha-v3';
    } else if (recaptchaInfo.version === 'v2_invisible') {
      return recaptchaInfo.enterprise
        ? 'recaptcha-invisible-enterprise'
        : 'recaptcha-invisible';
    } else {
      return recaptchaInfo.enterprise ? 'recaptcha-enterprise' : 'recaptcha';
    }
  }

  // Step 2: Check for other SUPPORTED CAPTCHA types only

  // hCaptcha detection
  if (document.querySelector('textarea[name=h-captcha-response]')) {
    return 'hcaptcha';
  }

  // Cloudflare Turnstile detection
  if (document.querySelector('input[name="cf-turnstile-response"]')) {
    return 'cloudflare';
  }

  // Friendly Captcha detection
  if (
    document.querySelector('[data-sitekey]') &&
    [...document.querySelectorAll('iframe')].filter((iframe) =>
      iframe.src.includes('frcapi.com/api/v2/captcha/widget'),
    ).length > 0
  ) {
    return 'friendlycaptcha';
  }

  // GeeTest detection (robust implementation)
  if (
    // @ts-ignore
    (window.initGeetest4 && typeof window.initGeetest4 === 'function') ||
    // @ts-ignore
    (window.initGeetest && typeof window.initGeetest === 'function')
  ) {
    // Check for actual geetest elements (most reliable)
    const hasGeetestElements =
      document.querySelector('.geetest_radar_tip') ||
      document.querySelector('.geetest_holder') ||
      document.querySelector('.geetest_widget') ||
      document.querySelector('[data-geetest]') ||
      document.querySelector('.geetest_canvas_img') ||
      document.querySelector('.geetest_slider_button');

    // Check for legitimate geetest scripts (from geetest.com domain)
    // @ts-ignore
    const legitimateGeetestScripts = [
      ...document.querySelectorAll('script'),
    ].filter((script) => script.src && script.src.includes('geetest.com'));

    // Check for geetest API requests in network (look for gt and challenge parameters)
    // @ts-ignore
    const hasGeetestApiRequests =
      window.performance &&
      window.performance.getEntriesByType('resource').some((entry) => {
        try {
          if (
            entry.name &&
            (entry.name.includes('geetest.com') ||
              entry.name.includes('/gettype.php'))
          ) {
            const url = new URL(entry.name);
            const gt = url.searchParams.get('gt');
            const challenge = url.searchParams.get('challenge');
            return gt && challenge;
          }
        } catch (e) {
          // Invalid URL, skip
        }
        return false;
      });

    const hasGeetestConfig =
      // @ts-ignore
      window.gtConfig || window.geetestConfig || window.gt;

    // Only consider it geetest if we have strong evidence
    if (
      hasGeetestElements ||
      legitimateGeetestScripts.length > 0 ||
      hasGeetestApiRequests ||
      hasGeetestConfig
    ) {
      return 'geetest';
    }
  }

  // Normal captcha detection - look for common patterns
  const normalCaptchaSelectors = [
    'img[class*="captcha"]',
    'img[alt*="captcha"]',
  ];

  for (const selector of normalCaptchaSelectors) {
    const captchaImg = document.querySelector(selector);

    if (captchaImg) {
      // Check if there's an associated input field
      const inputSelectors = [
        'input[name*="captcha"]',
        'input[id*="captcha"]',
        '.captcha input[type="text"]',
        '#captcha input[type="text"]',
        'input[name*="challenge"]',
        'input[id*="challenge"]',
        'input[name*="verification"]',
        'input[id*="verification"]',
        // 2captcha.com specific patterns
        'input[name="simpleCaptcha"]',
        'input[id*="simple-captcha"]',
      ];

      for (const inputSelector of inputSelectors) {
        const inputField = document.querySelector(inputSelector);

        if (inputField) {
          return 'normal';
        }
      }
    }
  }

  // Text captcha detection - look for text-based questions with input fields
  const textCaptchaQuestionSelectors = [
    '.captcha-question',
    '[class*="captcha-question"]',
    '[id*="captcha-question"]',
    '.text-captcha-question',
    '[class*="text-captcha"]',
    '[aria-label*="captcha question"]',
    '[data-captcha-question]',
    'label[for*="captcha"]',
    '.math-captcha',
    '[class*="math-captcha"]',
  ];

  // Check for text-based captcha questions
  for (const selector of textCaptchaQuestionSelectors) {
    const questionElement = document.querySelector(selector);
    if (questionElement && questionElement.textContent) {
      const questionText = questionElement.textContent.toLowerCase();
      // Look for common text captcha patterns
      const textCaptchaPatterns = [
        'what',
        'solve',
        'calculate',
        'answer',
        'math',
        'arithmetic',
        'add',
        'subtract',
        'multiply',
        'divide',
        '+',
        '-',
        '*',
        '/',
        '=',
        'equals',
        'sum',
        'difference',
        'product',
        'quotient',
      ];

      const hasTextCaptchaPattern = textCaptchaPatterns.some((pattern) =>
        questionText.includes(pattern),
      );

      if (hasTextCaptchaPattern) {
        // Verify there's an associated input field
        const textCaptchaInputSelectors = [
          'input[name*="captcha"]',
          'input[id*="captcha"]',
          'input[name*="answer"]',
          'input[id*="answer"]',
          'input[name*="question"]',
          'input[id*="question"]',
          'input[name*="challenge"]',
          'input[id*="challenge"]',
          'input[name*="verification"]',
          'input[id*="verification"]',
          'input[name*="text-captcha"]',
          'input[id*="text-captcha"]',
          'input[name*="math"]',
          'input[id*="math"]',
          '.captcha input[type="text"]',
          '.text-captcha input[type="text"]',
          '.math-captcha input[type="text"]',
        ];

        for (const inputSelector of textCaptchaInputSelectors) {
          const inputField = document.querySelector(inputSelector);
          if (inputField) {
            return 'textcaptcha';
          }
        }
      }
    }
  }

  // Alternative text captcha detection by scanning for question patterns in text
  const allTextElements = Array.from(document.querySelectorAll('*')).filter(
    (el) =>
      el.children.length === 0 &&
      el.textContent &&
      el.textContent.trim().length > 0,
  );

  for (const element of allTextElements) {
    const text = element.textContent?.toLowerCase() || '';

    // Look for mathematical expressions or questions
    const mathPatterns = [
      /what\s+is\s+\d+\s*[\+\-\*\/]\s*\d+/,
      /solve\s*:\s*\d+\s*[\+\-\*\/]\s*\d+/,
      /calculate\s*:\s*\d+\s*[\+\-\*\/]\s*\d+/,
      /\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\?/,
      /answer\s*:\s*\d+\s*[\+\-\*\/]\s*\d+/,
    ];

    const hasMatchingPattern = mathPatterns.some((pattern) =>
      pattern.test(text),
    );

    if (hasMatchingPattern) {
      // Check if there's a nearby input field
      const parent = element.closest(
        'form, div, section, .captcha, .text-captcha',
      );
      if (parent) {
        const nearbyInput = parent.querySelector(
          'input[type="text"], input:not([type])',
        );
        if (nearbyInput) {
          return 'textcaptcha';
        }
      }
    }
  }

  return 'unknown';
};

export const captchaNetworkCalls = [
  '*google.com/recaptcha/api.js*',
  '*capy.me/puzzle/get_js/*',
  '*client-api.arkoselabs.com/*/api.js*',
  '*frcapi.com/api/v2/captcha/widget*',
  '*hcaptcha.com/*/api.js*',
  '*challenges.cloudflare.com/turnstile*',
  '*api.geetest.com/gettype.php*',
  '*keycaptcha.com/swfs/cap.js*',
  '*api.leminnow.com/captcha/v1/*/js*',
  '*mtcaptcha.com/mtcv1/client/iframe.html*',
  '*yandex.ru/captchapgrd*',
];

export interface URLAndUA {
  website: string;
  userAgent: string;
}

export const getURLAndUA = (): URLAndUA => ({
  website: location.href,
  userAgent: navigator.userAgent,
});

export const startRecording = (pageId: string) => {
  return new Promise((resolve, reject) => {
    const start = () =>
      window.postMessage({ id: pageId, type: 'REC_START' }, '*');
    const onStart = (event: MessageEvent) => {
      if (event.data.message === 'REC_STARTED') {
        window.removeEventListener('message', onStart);
        return resolve(undefined);
      }
      if (event.data.message === 'REC_START_FAIL') {
        window.removeEventListener('message', onStart);
        return reject(new Error(event.data.error));
      }
    };

    window.addEventListener('message', onStart);

    return document.readyState == 'complete'
      ? start()
      : window.addEventListener('load', start);
  });
};

export const stopRecording = (pageId: string) => {
  return new Promise((resolve, reject) => {
    const onStop = async (event: MessageEvent) => {
      if (event.data.type === 'REC_STOP') {
        return resolve(true);
      }

      if (event.data.type === 'REC_STOP_FAIL') {
        window.removeEventListener('message', onStop);
        return reject(new Error(event.data.error));
      }

      if (event.data.type === 'REC_NOT_STARTED') {
        window.removeEventListener('message', onStop);
        return reject(
          new Error(`Recording hasn't started, did you forget to start it?`),
        );
      }
    };

    window.addEventListener('message', onStop);
    return window.postMessage({ id: pageId, type: 'REC_STOP' }, '*');
  });
};

export const screencastExtensionPath = join(
  process.cwd(),
  'extensions',
  'screencast',
);

export const ublockExtensionPath = join(
  process.cwd(),
  'extensions',
  'ublocklite',
);

export const parseLaunchOptions = async ({
  options,
  req,
  stealth,
}: BrowserLauncherOptions): Promise<BrowserLauncherOptions> => {
  options.args = options.args || [];
  const record = req?.parsed.searchParams.has('record');
  const blockAds = req?.parsed.searchParams.has('blockAds');

  // Add WebRTC IP leak prevention to ALL browsers to prevent regional IP leaking
  // This ensures consistent IP behavior across all load balancer endpoints
  options.args.push(
    // COMPREHENSIVE LEAK PROTECTION FLAGS FOR ALL BROWSERS
    // WebRTC IP Leak Prevention
    '--webrtc-ip-handling-policy=disable_non_proxied_udp',
    '--force-webrtc-ip-handling-policy',
    '--disable-features=WebRtcHideLocalIpsWithMdns',
    '--enforce-webrtc-ip-permission-check',
  );

  // Don't load captcha solving for stealth routes as they're detectable by
  // cloudflare and others
  const extensionLaunchArgs = options.args?.find((a) =>
    a.startsWith('--load-extension'),
  );

  // Remove specified extensions as we inject others below,
  // including user-specified extensions
  options.args = options.args?.filter(
    (a) =>
      !a.startsWith('--load-extension') &&
      !a.startsWith('--disable-extensions-except'),
  );

  const extensions = [
    record ? screencastExtensionPath : null,
    blockAds ? ublockExtensionPath : null,
    extensionLaunchArgs ? extensionLaunchArgs.split('=')[1] : null,
  ].filter((_) => !!_);

  if (record) {
    options.args.push(
      '--enable-usermedia-screen-capturing',
      '--enable-blink-features=GetUserMedia',
      '--allow-http-screen-capture',
      '--auto-select-desktop-capture-source=browserless-screencast',
      '--disable-infobars',
    );
    options.ignoreDefaultArgs = ['--enable-automation'];
    options.headless = false;
  }

  if (extensions.length) {
    options.args.push(
      '--load-extension=' + extensions.join(','),
      '--disable-extensions-except=' + extensions.join(','),
    );
  }

  return {
    options,
    req,
    stealth,
  };
};

export function getBooleanRequestParameter(
  req: AugmentedRequest | boolean | string | undefined,
  param?: string,
  fallback?: boolean,
): boolean {
  if (typeof req === 'undefined') return fallback ?? false;
  if (typeof req === 'boolean') return req;
  if (typeof req === 'string') return req === 'true';

  const value = req.parsed.searchParams.get(param!);

  if (value === null) {
    return fallback ?? false;
  }

  if (value === 'false' || value === '0') {
    return false;
  }

  return true;
}

export const MouseElement = (position: { x: number; y: number }) => {
  if (document.querySelector('puppeteer-mouse-pointer')) return;
  const box = document.createElement('puppeteer-mouse-pointer');
  const styleElement = document.createElement('style');
  styleElement.innerHTML = `
  puppeteer-mouse-pointer {
    pointer-events: none;
    position: absolute;
    top: ${position.y}px;
    z-index: 10000;
    left: ${position.x}px;
    width: 20px;
    height: 20px;
    background: rgba(0,0,0,.4);
    border: 1px solid white;
    border-radius: 10px;
    margin: -10px 0 0 -10px;
    padding: 0;
    transition: background .2s, border-radius .2s, border-color .2s;
  }
  puppeteer-mouse-pointer.button-1 {
    transition: none;
    background: rgba(0,0,0,0.9);
  }
  puppeteer-mouse-pointer.button-2 {
    transition: none;
    border-color: rgba(0,0,255,0.9);
  }
  puppeteer-mouse-pointer.button-3 {
    transition: none;
    border-radius: 4px;
  }
  puppeteer-mouse-pointer.button-4 {
    transition: none;
    border-color: rgba(255,0,0,0.9);
  }
  puppeteer-mouse-pointer.button-5 {
    transition: none;
    border-color: rgba(0,255,0,0.9);
  }
`;
  document.head.appendChild(styleElement);
  document.body.appendChild(box);
  document.addEventListener(
    'mousemove',
    (event) => {
      box.style.left = event.pageX + 'px';
      box.style.top = event.pageY + 'px';
      updateButtons(event.buttons);
    },
    true,
  );
  document.addEventListener(
    'mousedown',
    (event) => {
      updateButtons(event.buttons);
      box.classList.add('button-' + event.which);
    },
    true,
  );
  document.addEventListener(
    'mouseup',
    (event) => {
      updateButtons(event.buttons);
      box.classList.remove('button-' + event.which);
    },
    true,
  );
  function updateButtons(buttons: number) {
    for (let i = 0; i < 5; i++)
      box.classList.toggle('button-' + i, Boolean(buttons & (1 << i)));
  }
};

export const isMatch = (text: string, pattern: string) => {
  return micromatch.isMatch(text, pattern, { bash: true });
};

export const truncate = (
  str: string,
  maxLength: number = 30,
  ending: string = '...',
): string => {
  if (str.length > maxLength) {
    return str.slice(0, maxLength - ending.length) + ending;
  }
  return str;
};

export const getProxyUnitsUsed = (bytes: number) => {
  const mbUsed = (bytes * BYTES_MULTIPLE) / BYTES_IN_A_MB;
  const unitsUsed = Math.ceil(mbUsed * UNITS_PER_MB);

  return unitsUsed;
};

export const getProxyURL = ({
  username,
  password,
  country,
  city,
  state,
  sessionId,
}: {
  username: string;
  password: string;
  country?: string | null;
  city?: string | null;
  state?: string | null;
  sessionId?: string | null;
}) => {
  const parts = [password, 'streaming-1', 'skipispstatic-1'];

  if (country) {
    parts.push(`country-${country}`);
  }

  if (city) {
    parts.push(`city-${city}`);
  }

  if (state) {
    parts.push(`state-${state}`);
  }

  if (sessionId) {
    parts.push(`session-${sessionId}_lifetime-30m`);
  }

  return `http://${username}:${parts.join('_')}@geo.iproyal.com:12321`;
};

export const isIntrospection = (req: AugmentedRequest) => {
  const bqlPatterns = [
    EnterpriseRoutes.chromiumBQL,
    EnterpriseRoutes.chromeBQL,
  ];

  const isBQL = bqlPatterns.some((p) => isMatch(req.parsed.pathname, p));
  if (!isBQL) {
    return false;
  }

  const body = req.body as any;

  // The GraphQL Spec indicates that __type and __schema are the only reserved keywords
  // for introspection, and must only be used inside a query, regardless of the operation name.
  // So if it's not a query, it's not an instrospection.
  // If it's a query, and matches __type or __schema, we can assume it's an instrospection.
  // https://spec.graphql.org/October2021/#sec-Schema-Introspection

  if (!body?.query) {
    return false;
  }

  const query = body.query.toLowerCase();

  return (
    /\b__type\b/.test(query) || // Match __type but not __typename
    /\b__schema\b/.test(query)
  );
};

const mobileOSRegex =
  /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i;

const mobileVendorOS =
  /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i;

export const isMobile = (userAgent: string | null | undefined) => {
  if (!userAgent) {
    return false;
  }

  const userAgentPart = userAgent.substring(0, 4);
  return mobileOSRegex.test(userAgent) || mobileVendorOS.test(userAgentPart);
};

export const recaptchaSiteKeyReducer = (
  result: null | RecaptchaWidgetInfo,
  uri: string,
) => {
  // // Import recaptcha constants locally to avoid circular dependency
  // const recaptcha = {
  //   url: `*google.com/recaptcha*k=*`,
  //   param: 'k',
  // };

  if (result) return result;
  if (!isMatch(uri, recaptcha.url)) {
    return null;
  }
  let widgetInfo: RecaptchaWidgetInfo | null = null;
  const url = new URL(uri);
  const siteKey = url.searchParams.get(recaptcha.param);

  // Extract additional parameters from network request
  const dataS = url.searchParams.get('s');
  const isEnterprise = uri.includes('/enterprise/');

  // Detect reCAPTCHA V2 version from URL parameters
  let version = 'v2'; // Default
  const size = url.searchParams.get('size');

  // Skip V3 Enterprise - let solveRecaptchaV3 handle it
  if (isEnterprise && size === 'invisible') {
    return null; // Skip this URL, let V3 solver handle it
  }

  if (size === 'invisible') {
    version = 'v2_invisible'; // reCAPTCHA v2 invisible (non-enterprise)
  }

  if (siteKey) {
    const action = url.searchParams.get('action');
    const isV3Pattern =
      action || (isEnterprise && size === 'invisible') || uri.includes('v3');

    // This can be abstracted further, just as an example:
    if (isV3Pattern) {
      widgetInfo = {
        sitekey: siteKey,
        version: 'v3',
        s: dataS || null,
        action: action || 'submit', // Default V3 action
        enterprise: isEnterprise,
      };
    } else {
      widgetInfo = {
        sitekey: siteKey,
        version: version,
        s: dataS || null,
        action: null, // V2 doesn't use action parameter
        enterprise: isEnterprise,
      };
    }
  }

  return widgetInfo;
};

export const getURLLastSegment = (url: URL) =>
  url.pathname
    .split('/')
    .filter((_) => !!_)
    .pop();

export const setGeetestResponse = (
  challenge: string,
  validate: string,
  seccode: string,
) => {
  // Set hidden form inputs
  const inputs = [
    { selector: 'input[name="geetest_challenge"]', value: challenge },
    { selector: 'input[name="geetest_validate"]', value: validate },
    { selector: 'input[name="geetest_seccode"]', value: seccode },
  ];

  try {
    inputs.forEach(({ selector, value }) => {
      let input = document.querySelector(selector) as HTMLInputElement;
      input.value = value;
    });
  } catch (error) {
    return false;
  }

  return true;
};

// prettier-ignore
export const getWAFData = () => {
  // @ts-ignore
  if (window.CaptchaScript) {
    return {
      // @ts-ignore
      ...window.gokuProps,
      // @ts-ignore
      captchaScript: document.querySelector('script[src*="captcha.js"]')?.src,
      // @ts-ignore
      challengeScript: document.querySelector('script[src*="challenge.js"]')?.src
    };
  }
  return null;
};

export const buildBlessObject = async (req: Request) => {
  const bqlPatterns = [
    EnterpriseRoutes.chromiumBQL,
    EnterpriseRoutes.chromeBQL,
  ];
  let requiresCaptchaSolving = false;
  const pathname = req.parsed.pathname;
  const wsPatterns = Object.values(WebsocketRoutes);
  const isBQL = bqlPatterns.some((p) =>
    micromatch.isMatch(pathname, p, { bash: true }),
  );
  const isWS = wsPatterns.some((p) =>
    micromatch.isMatch(pathname, p, { bash: true }),
  );
  const requestType = isBQL ? 'bql' : isWS ? 'baas' : 'rest';

  if (isBQL) {
    const waitForBody = new Promise((resolve, reject) => {
      let data = '';

      req.on('data', (chunk) => {
        data += chunk;
      });

      req.on('end', () => {
        req.body = data;
        resolve(data);
      });

      req.on('error', (err) => {
        console.log('error', err);
        reject(err);
      });
    });

    await waitForBody;
    if (req.body) {
      const operation = graphql.parse(JSON.parse(req.body as string).query);

      /**
       * Extracts the names of top-level mutations from the parsed GraphQL operation.
       */
      const getTopLevelMutations = (doc: graphql.DocumentNode) => {
        const mutations = [];
        for (const def of doc.definitions) {
          if (
            def.kind === 'OperationDefinition' &&
            def.operation === 'mutation'
          ) {
            for (const selection of def.selectionSet.selections) {
              if (selection.kind === 'Field' && selection.name.value) {
                mutations.push(selection.name.value);
              }
            }
          }
        }
        return mutations;
      };
      const mutationNames = getTopLevelMutations(operation);
      if (mutationNames.includes('solve') || mutationNames.includes('verify')) {
        requiresCaptchaSolving = true;
      }
    }
  }

  // REST
  if (!isBQL && !isWS) {
    if (pathname.includes('/scrape')) {
      requiresCaptchaSolving = true;
    }
  }

  const useProxy = req.parsed.searchParams.has('proxy') ?? false;
  const proxyType = req.parsed.searchParams.get('proxy') ?? 'residential';
  const proxyCountry = req.parsed.searchParams.get('proxyCountry') ?? 'us';
  const proxyState = req.parsed.searchParams.get('proxyState') ?? null;
  const proxyCity = req.parsed.searchParams.get('proxyCity') ?? null;
  const proxySticky = req.parsed.searchParams.has('proxySticky') ?? false;
  const proxyConfig = {
    proxyPort: await getPort(),
    proxyType,
    proxyCountry,
    proxyState,
    proxyCity,
    proxySticky,
  };

  Object.defineProperty(req, '__bless__', {
    configurable: false,
    enumerable: false,
    writable: true,
    value: {
      requestId: id(),
      originalUrl: req.url as string,
      captchasSolved: 0,
      bqlProxyBytes: 0,
      type: requestType,
      requiresCaptchaSolving: requiresCaptchaSolving,
      ...(useProxy ? proxyConfig : {}),
    },
  });

  return req;
};

import {
  AfterResponse,
  BeforeRequest,
  Config,
  Request,
} from '@browserless.io/browserless';

export interface BrowserlessToken {
  createdAt: number;
  createdBy: string;
  permissions: Permissions;
  token: string;
}

export enum Permissions {
  /**
   * Can do any action inside the browserless system. The TOKEN parameter
   * is granted this permission at startup and can create sub-tokens as well.
   */
  admin = 'admin',

  /**
   * Can view metrics, config as well as run traffic through the platform and
   * create/connect to sessions. Cannot create or view other tokens.
   */
  developer = 'developer',

  /**
   * Public: anyone with the URL can view or access the resources with this permission.
   */
  public = 'public',

  /**
   * Can view metrics, load configuration, and see some actively running sessions
   * but cannot connect or run REST API calls.
   */
  viewer = 'viewer',
}

export abstract class Database {
  constructor(public config: Config) {}
  abstract get(key: string): Promise<string | null>;
  abstract set(key: string, value: string): Promise<void>;
}

export const BrowserlessEnterpriseRoutes = {
  // Chrome
  EnterpriseChromeLiveWebsocketRoute: 'EnterpriseChromeLiveWebsocketRoute',
  EnterpriseChromeStealthWebsocketRoute:
    'EnterpriseChromeStealthWebsocketRoute',
  EnterpriseChromeUnblockPostRoute: 'EnterpriseChromeUnblockPostRoute',
  EnterpriseChromeCDPWebSocketRoute: 'EnterpriseChromeCDPWebSocketRoute',
  EnterpriseChromeExportPostRoute: 'EnterpriseChromeExportPostRoute',

  // Chromium
  EnterpriseChromiumLiveWebsocketRoute: 'EnterpriseChromiumLiveWebsocketRoute',
  EnterpriseChromiumStealthWebsocketRoute:
    'EnterpriseChromiumStealthWebsocketRoute',
  EnterpriseChromiumUnblockPostRoute: 'EnterpriseChromiumUnblockPostRoute',
  EnterpriseChromiumCDPWebSocketRoute: 'EnterpriseChromiumCDPWebSocketRoute',
  EnterpriseChromiumExportPostRoute: 'EnterpriseChromiumExportPostRoute',

  // Management
  EnterpriseTokenGetRoute: 'EnterpriseTokenGetRoute',
  EnterpriseTokenDeleteRoute: 'EnterpriseTokenDeleteRoute',
  EnterpriseTokenPostRoute: 'EnterpriseTokenPostRoute',

  // BQL Routes
  EnterpriseChromiumBQLPostRoute: 'EnterpriseChromiumBQLPostRoute',
  EnterpriseChromeBQLPostRoute: 'EnterpriseChromeBQLPostRoute',

  // Other
  EnterpriseReconnectRoute: 'EnterpriseReconnectRoute',
  EnterpriseChromiumStealthPostRoute: 'EnterpriseChromiumStealthPostRoute',

  // Session Management
  EnterpriseSessionCreateRoute: 'EnterpriseSessionCreateRoute',
  EnterpriseSessionConnectRoute: 'EnterpriseSessionConnectRoute',
  EnterpriseSessionDeleteRoute: 'EnterpriseSessionDeleteRoute',
  EnterpriseSessionWebsocketRoute: 'EnterpriseSessionWebsocketRoute',
};

export interface AugmentedRequest extends Request {
  __bless__: {
    /**
     * The original URL of the request prior
     * to any processing or modifications.
     */
    originalUrl: string;

    /**
     * Request id to track logs
     */
    requestId: string;

    /**
     * The total number of captcha's solved
     */
    captchasSolved: number;

    /**
     * The total number bytes used for proxying in BQL Queries
     */
    bqlProxyBytes: number;

    /**
     * The type of proxy to use
     */
    proxyType: 'residential';

    /**
     * Proxy Country, if any
     */
    proxyCountry: string;

    /**
     * Proxy State, if any
     */
    proxyState: string | null;

    /**
     * Proxy City, if any
     */
    proxyCity: string | null;

    /**
     * Whether or not to use the same IP for all requests
     */
    proxySticky: boolean;

    /**
     * The internal port for the server to listen on for BaaS
     */
    proxyPort?: number;

    /**
     * Whether or not the request requires captcha solving
     */
    requiresCaptchaSolving: boolean;

    /**
     * The type of request
     */
    type: 'bql' | 'baas' | 'rest';
  };
}

export interface BeforeHook extends BeforeRequest {
  req: AugmentedRequest;
}

export interface AfterHook {
  req: AugmentedRequest;
  status: AfterResponse['status'];
  start: AfterResponse['start'];
}

/**
 * An array of statistics for a 5-minute period. The values, in order, are
 * [0]: Seconds (integer) of time consumed for entire period
 * [1]: Integer of successful calls (exited normally)
 * [2]: Integer of timedout calls
 * [3]: Integer of errored calls
 * [4]: Date timestamp in UNIX epoch (Date.now()) in UTC.
 * [5]: The number of call-based calls (30 second increments)
 * [6]: The number of units used for residential proxying
 * [7]: The number of units used for captcha solving
 */
export type SharedFleetStat = [
  number,
  number,
  number,
  number,
  number,
  number,
  number,
  number,
];

/**
 * A payload of meta-data for a particular token. The values, in order, are:
 * [0]: Seconds or Units, depending on the type of account.
 * [1]: The number identifier of the plan type they are on.
 */
export type SharedFleetTokenBitmap = [number | 'Infinity', number];

/**
 * In-memory payloads here convert the 'Infinity' string to a JS
 * Infinity number for doing decrement and other math operations on.
 */
export interface ISharedFleetMemoized {
  [apiToken: string]: [number, number];
}

export const cloudUnitPlanNumbers = {
  free: 0,
  fifteenThousand: 10,
  fortyThousand: 20,
  oneHundredThousand: 30,
  oneHundredEightyThousand: 40,
  threeHundredTwentyThousand: 50,
  fiveHundredThousand: 60,
  oneMillion: 70,
  elevenHundredThousand: 80,
  twentyFiveHundredThousand: 90,
  fiveMillion: 100,
  tenMillion: 120,
  twentyFiveMillion: 130,
  fiftyMillion: 140,
  oneHundredMillion: 150,
  oneBillion: 160,
};

export interface ISharedFleetGetPayload {
  [apiToken: string]: SharedFleetTokenBitmap;
}

/**
 * A collection of all API tokens and their relevant statistics
 * for the current period (5 minute intervals)
 */
export interface ISharedFleetPutPayload {
  [key: string]: SharedFleetStat;
}

/**
 * A browser session ended event to track metrics
 */
export interface SessionEndedEvent extends IAmplitudeEvent {
  event_properties: {
    seconds?: number;
    time_units?: number;
    proxy_units?: number;
    captcha_units?: number;
    total_units?: number;
    status?: string;
    endpoint?: string;
    user_agent?: string;
    programming_language?: string;
    request_id: string;
    token: string;
    worker_id?: string;
    error_type?: string;
    error_description?: string;
    request_type?: string;
    browserless_version?: string;
  };
}

export interface IAmplitudeEvent {
  event_type: string;
  time: number;
  session_id?: number;
  platform?: string;
  ip?: string;
  event_properties: {
    token: string;
    [key: string]: any;
  };
}

export interface ConnectionFailureEvent {
  event_type: string;
  time: number;
  session_id?: number;
  platform?: string;
  ip?: string;
  event_properties: {
    error_type: string;
    error_description: string;
    endpoint?: string;
    user_agent?: string;
    client_ip?: string;
    request_id: string;
    worker_id?: string;
    [key: string]: any;
  };
}

export interface SessionMetadata {
  args: string[];
  blockAds: boolean;
  browser: 'chrome' | 'chromium';
  createdAt: number;
  createdBy: string;
  expiresAt: number;
  headless: boolean;
  id: string;
  proxy?: {
    city?: string;
    country?: string;
    state?: string;
    sticky?: boolean;
    type?: 'residential';
  };
  running: boolean;
  stealth: boolean;
  ttl: number;
  url?: string;
  userDataDir: string;
}

export enum CaptchaType {
  Recaptcha = 'recaptcha',
  RecaptchaInvisible = 'recaptcha-invisible',
  RecaptchaV3 = 'recaptcha-v3',
  Capy = 'capy',
  Funcaptcha = 'funcaptcha',
  Friendlycaptcha = 'friendlycaptcha',
  Hcaptcha = 'hcaptcha',
  Cloudflare = 'cloudflare',
  Geetest = 'geetest',
  Keycaptcha = 'keycaptcha',
  Lemin = 'lemin',
  Mtcaptcha = 'mtcaptcha',
  Yandex = 'yandex',
  Unknown = 'unknown',
  AmazonWAF = 'amazonWaf',
  Normal = 'normal',
  TextCaptcha = 'textcaptcha',
}

export interface RecaptchaWidgetInfo {
  sitekey: string;
  version: string;
  s: string | null;
  action: string | null;
  enterprise: boolean;
}

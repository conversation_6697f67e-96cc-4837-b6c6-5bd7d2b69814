import { ServerResponse } from 'http';
import {
  contentTypes,
  Methods,
  APITags,
  jsonResponse,
  dedent,
  fetchJson,
  SystemQueryParameters,
  BrowserHTTPRoute,
  CDPJSONPayload,
  CDPLaunchOptions,
  Logger,
  WaitForEventOptions,
  WaitForFunctionOptions,
  WaitForSelectorOptions,
  waitForEvent as waitForEvt,
  waitForFunction as waitForFn,
  sleep,
  bestAttemptCatch,
  bestAttempt,
} from '@browserless.io/browserless';
import puppeteer, { Connection, Page, Cookie } from 'puppeteer-core';
import { NodeWebSocketTransport } from 'puppeteer-core/lib/esm/puppeteer/node/NodeWebSocketTransport.js';

import { EnterpriseRoutes } from '../../paths.js';
import { ChromiumStealthBrowser } from '../../browsers/chromium.stealth.js';
import { unblock } from '../utils/solver.js';
import { AugmentedRequest, BrowserlessEnterpriseRoutes } from '../../types.js';
import { getBooleanRequestParameter } from '../../utils.js';

const MAX_TTL = 60 * 1000;

export interface QuerySchema extends SystemQueryParameters {
  launch?: CDPLaunchOptions | string;
}

export interface BodySchema {
  bestAttempt?: bestAttempt;

  /**
   * The URL of the site you want to unblock.
   */
  url: string;

  /**
   * Whether or not to keep the underlying browser alive and around for
   * future reconnects. Defaults to false.
   */
  browserWSEndpoint?: boolean;

  /**
   * Whether or not to to return cookies for the site, defaults to true.
   */
  cookies?: boolean;

  /**
   * Whether or not to to return content for the site, defaults to true.
   */
  content?: boolean;

  /**
   * Whether or not to to return a full-page screenshot for the site, defaults to true.
   */
  screenshot?: boolean;

  /**
   * When the browserWSEndpoint is requested this tells
   * browserless how long to keep this browser alive for
   * re-connection until shutting it down completely.
   * Maximum of 30000 for 30 seconds (30,000ms).
   */
  ttl?: number;

  /**
   * An optional goto parameter object for considering when the page is done loading.
   */
  gotoOptions?: Parameters<Page['goto']>[1];
  waitForEvent?: WaitForEventOptions;
  waitForFunction?: WaitForFunctionOptions;
  waitForSelector?: WaitForSelectorOptions;
  waitForTimeout?: number;
}

export interface ResponseSchema {
  /**
   * A list of cookies which can be used for new connections or for usage elsewhere.
   * Value is "null" when the request body specifies cookies: false.
   */
  cookies: Cookie[] | null;

  /**
   * The HTML content of the page once it is passed bot detection.
   * Value is "null" when the request body specifies cookies: false.
   */
  content: string | null;

  /**
   * The browserWSEndpoint of the response when the POST body contains a
   * browserWSEndpoint: true property
   */
  browserWSEndpoint: string | null;

  /**
   * The time the browser will remain alive until it is shutdown. Zero
   * when browserWSEndpoint: false is set in the request payload. The limit
   * is 30000 or 30 seconds, which is the maximum allowed time.
   */
  ttl: number;

  /**
   * A base64 encoded JPEG of the of the final site page.
   */
  screenshot: string | null;
}

export default class ChromiumUnblockPostRoute extends BrowserHTTPRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseChromiumUnblockPostRoute;
  auth = true;
  accepts = [contentTypes.json];

  // @ts-ignore @todo Request extensions
  browser = ChromiumStealthBrowser;
  contentTypes = [contentTypes.json];
  concurrency = true;
  description = dedent(`
    > This API is only available for Enterprise and Cloud-unit plans. [Contact us for more information here.](https://www.browserless.io/contact/), or [sign-up here](https://www.browserless.io/pricing/).

    Unblocks the provided URL from being blocked due to bot detection.
    Returns a payload of Cookies, HTML, a base64 encoded screenshot,
    and a "browserWSEndpoint" to allow connecting to the browser when
    specified in the JSON Payload. Only supports CDP or Puppeteer
    like libraries when connecting to the "browserWSEndpoint".
  `);
  method = Methods.post;
  path = [EnterpriseRoutes.unblock, EnterpriseRoutes.chromiumUnblock];
  tags = [APITags.browserAPI];

  // @ts-ignore @todo Request extensions
  handler = async (
    req: AugmentedRequest,
    res: ServerResponse,
    logger: Logger,
    stealthBrowser: ChromiumStealthBrowser,
  ): Promise<void> => {
    const body = req.body as BodySchema;
    const {
      browserWSEndpoint: keepBrowserAlive,
      waitForEvent,
      waitForFunction,
      waitForSelector,
      waitForTimeout,
      ttl = keepBrowserAlive ? MAX_TTL : 0,
      url,
    } = body;

    const browserTTL = Math.abs(
      isFinite(ttl) ? Math.min(ttl, MAX_TTL) : MAX_TTL,
    );
    const internalWSEndpoint = stealthBrowser.wsEndpoint() as string;
    const privateWSEndpoint = stealthBrowser.privateWSEndpoint(
      req.parsed.searchParams.get('token'),
    ) as string;
    const { port } = new URL(internalWSEndpoint);
    logger.trace(`Fetching /json/list from Chrome`);
    const targets = (await fetchJson(
      `http://127.0.0.1:${port}/json/list`,
    )) as Array<CDPJSONPayload>;
    logger.trace(`Successfully fetched /json/list from Chrome!`);

    // Find the first URL that isn't a Chrome Extension
    const { webSocketDebuggerUrl } = targets.find(
      (t) => t.type === 'page' && !t.url.startsWith('chrome://'),
    ) as CDPJSONPayload;
    logger.trace(`Connecting CDP to the underlying page`);
    const cdp = new Connection(
      webSocketDebuggerUrl,
      await NodeWebSocketTransport.create(webSocketDebuggerUrl),
    );
    logger.trace(`Starting unblock helper`);
    await unblock(cdp, url, logger);
    logger.trace(
      `Unblock complete, connecting local puppeteer to URL "${privateWSEndpoint}"`,
    );
    const browser = await puppeteer.connect({
      browserWSEndpoint: privateWSEndpoint,
    });
    const pages = await browser.pages();
    logger.trace(`Found "${pages.length}" available pages`);
    const page = pages.find((p) => !p.url().startsWith('chrome://'))!;

    const content = getBooleanRequestParameter(body.content, '', true);
    const cookies = getBooleanRequestParameter(body.cookies, '', true);
    const screenshot = getBooleanRequestParameter(body.screenshot, '', true);
    const bestAttempt = getBooleanRequestParameter(body.bestAttempt);

    if (waitForTimeout) {
      await sleep(waitForTimeout).catch(bestAttemptCatch(bestAttempt));
    }

    if (waitForFunction) {
      await waitForFn(page, waitForFunction).catch(
        bestAttemptCatch(bestAttempt),
      );
    }

    if (waitForSelector) {
      const { selector, hidden, timeout, visible } = waitForSelector;
      await page
        .waitForSelector(selector, { hidden, timeout, visible })
        .catch(bestAttemptCatch(bestAttempt));
    }

    if (waitForEvent) {
      await waitForEvt(page, waitForEvent).catch(bestAttemptCatch(bestAttempt));
    }

    const response: ResponseSchema = {
      browserWSEndpoint: null,
      content: null,
      cookies: null,
      screenshot: null,
      ttl: browserTTL,
    };

    if (keepBrowserAlive) {
      logger.trace(`Creating the public browserWSEndpoint`);
      const publicWSEndpoint = stealthBrowser.publicWSEndpoint();
      response.browserWSEndpoint = publicWSEndpoint;
    }

    if (content) {
      logger.trace(`Generating content`);
      response.content = await page.content();
    }

    if (cookies) {
      logger.trace(`Generating cookies`);
      response.cookies = await page.cookies();
    }

    if (screenshot) {
      logger.trace(`Generating screenshot`);
      response.screenshot = await page.screenshot({
        encoding: 'base64',
        type: 'jpeg',
        fullPage: true,
        quality: 50,
      });
    }

    logger.info(`Response generated, returning JSON.`);

    setTimeout(() => {
      logger.info(
        `Unblock timer of ${browserTTL.toLocaleString()}ms has been reached, shutting down.`,
      );
      browser.disconnect();
      cdp.dispose();
    }, browserTTL);

    return jsonResponse(res, 200, response);
  };
}

import cssUnits, { CSSUnits } from 'css-unit-converter';
import fetch, { Head<PERSON> } from 'node-fetch';
import Queue from 'queue';
import { Protocol } from 'puppeteer-core';

import { Context } from './context.js';
import { FetchInterception, operators } from './types.js';
import { isMatch } from '../../utils.js';
import { HttpsProxyAgent } from 'https-proxy-agent';

const isString = (obj: unknown): obj is string => {
  return typeof obj === 'string' || obj instanceof String;
};

const typedArrayToBase64 = (typedArray: Uint8Array): string => {
  const chunkSize = 65534;
  const chunks = [];

  for (let i = 0; i < typedArray.length; i += chunkSize) {
    const chunk = typedArray.subarray(i, i + chunkSize);
    chunks.push(String.fromCodePoint.apply(null, chunk as unknown as number[]));
  }

  const binaryString = chunks.join('');
  return btoa(binaryString);
};

export const getBodySize = (body?: string) => {
  if (typeof body === 'string') {
    return Buffer.from(body).length;
  }

  return 0;
};

export const getHeaderBytes = (
  headers?: Headers | Protocol.Network.Headers,
) => {
  if (!headers) {
    return 0;
  }

  return Object.entries(headers).reduce((total, [key, value]) => {
    return total + Buffer.byteLength(key) + Buffer.byteLength(value) + 4;
  }, 0);
};

const getResponse = (
  body: string | Uint8Array,
): {
  contentLength: number;
  base64: string;
} => {
  // Needed to get the correct byteLength
  const byteBody: Uint8Array = isString(body)
    ? new TextEncoder().encode(body)
    : body;

  return {
    contentLength: byteBody.byteLength,
    base64: typedArrayToBase64(byteBody),
  };
};

export const fetchWithProxy = async ({
  url,
  proxy,
  body,
  headers,
  method,
}: {
  url: string;
  proxy: string;
  body?: string;
  headers: Protocol.Network.Headers;
  method: string;
}) => {
  const agent = new HttpsProxyAgent(proxy);

  // Add default Chrome headers
  const enhancedHeaders = {
    ...headers,
    'Sec-Fetch-Dest': headers['Sec-Fetch-Dest'] || 'document',
    'Sec-Fetch-Mode': headers['Sec-Fetch-Mode'] || 'navigate',
    'Sec-Fetch-Site': headers['Sec-Fetch-Site'] || 'none',
    'Sec-Fetch-User': headers['Sec-Fetch-User'] || '?1',
    Priority: headers['Priority'] || 'u=0, i',
  };
  const response = await fetch(url, {
    agent,
    body,
    headers: enhancedHeaders,
    method,
  });

  const responseBody = new Uint8Array(await response.arrayBuffer());
  const responseHeaders = response.headers;
  const parsedBody = responseBody ? getResponse(responseBody) : undefined;

  if (parsedBody?.contentLength && !responseHeaders.has('content-length')) {
    responseHeaders.set('content-length', String(parsedBody.contentLength));
  }

  const sentBytes = getHeaderBytes(headers) + getBodySize(body);
  const receivedBytes =
    getHeaderBytes(response.headers) + responseBody.byteLength;

  return {
    status: response.status,
    body: parsedBody?.base64,
    headers: response.headers,
    sentBytes,
    receivedBytes,
  };
};

export const serial = () => {
  const limit = new Queue({
    concurrency: 1,
    autostart: true,
  });
  return (handler: (args: any, context: Context) => Promise<unknown>) =>
    (args: unknown, context: Context) =>
      new Promise(async (resolve, reject) =>
        limit.push(async () => {
          try {
            const result = await handler(args, context);
            return resolve(result);
          } catch (e) {
            return reject(e);
          }
        }),
      );
};

export const asyncPoll = async <T>(
  fn: () => Promise<T | null>,
  interval: number,
  timeout: number = 30000,
): Promise<T> => {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      const data = await fn();

      if (data !== null) {
        return data;
      }

      await new Promise((resolve) => setTimeout(resolve, interval));
    } catch (err) {
      throw err;
    }
  }

  throw new Error('Timed out waiting for condition.');
};

/**
 * Converts a CSS unit to inches.
 *
 * @param unit - The CSS unit to convert. Can be a string or a number. If a number is provided, it is assumed to be in inches.
 * @returns The equivalent value in inches.
 * @throws Will throw an error if the provided unit is not a valid CSS unit.
 */
export const cssUnitToInches = (unit: string | number) => {
  if (typeof unit === 'number') {
    return unit;
  }

  const regex =
    /(\d+(?:\.\d+)?)\s*(px|cm|mm|in|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)/g;

  if (!unit.match(regex)) {
    throw new Error(`Invalid css unit: ${unit}`);
  }

  const [, n, unitType] = regex.exec(unit)!;

  return cssUnits(Number(n), unitType as CSSUnits, 'in');
};

export const getDebuggerURL = (targetId: string, port: string) =>
  `ws://127.0.0.1:${port}/devtools/page/${targetId}`;

export const networkMatchesPattern = ({
  method,
  methods,
  operator,
  status,
  statuses,
  type,
  types,
  url,
  urls,
}: {
  method: string;
  methods?: string[];
  operator: operators;
  status?: number;
  statuses?: number[];
  type: string;
  types?: string[];
  url: string;
  urls?: string[];
}): boolean => {
  const matchingFunction =
    operator === 'or' ? Array.prototype.some : Array.prototype.every;
  const urlsMatches = urls?.length
    ? matchingFunction.call(urls, (urlPattern) => isMatch(url, urlPattern))
    : true;
  const methodsMatch = methods?.length
    ? matchingFunction.call(
        methods,
        (m) => m.toLowerCase() === method.toLowerCase(),
      )
    : true;
  const typesMatch = types?.length
    ? matchingFunction.call(
        types,
        (t) => t.toLowerCase() === type.toLowerCase(),
      )
    : true;

  const statusesMatch =
    status && statuses?.length
      ? matchingFunction.call(status, (s) => s === status)
      : true;

  const matches = [];

  if (urls?.length) {
    matches.push(urlsMatches);
  }

  if (methods?.length) {
    matches.push(methodsMatch);
  }

  if (types?.length) {
    matches.push(typesMatch);
  }

  if (status && statuses?.length) {
    matches.push(statusesMatch);
  }

  return matches.length ? matchingFunction.call(matches, (_) => !!_) : true;
};

export const findPatternForRequest = (
  request: Protocol.Fetch.RequestPausedEvent,
  patterns: FetchInterception[],
) => {
  return patterns
    .sort((a, b) => {
      if (a.action === b.action) {
        return 0;
      }

      if (
        a.action === 'abort' ||
        (a.action === 'proxy' && b.action !== 'abort')
      ) {
        return -1;
      }

      return 0;
    })
    .find((p) =>
      networkMatchesPattern({
        url: request.request.url,
        method: request.request.method.toLowerCase(),
        type: request.resourceType.toLowerCase(),
        urls: p.url,
        methods: p.method,
        types: p.type,
        operator: p.operator,
      }),
    );
};

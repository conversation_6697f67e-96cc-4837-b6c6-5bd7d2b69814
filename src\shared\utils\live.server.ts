import { EventEmitter } from 'events';
import { noop, once, <PERSON><PERSON> } from '@browserless.io/browserless';

import puppeteer, { <PERSON><PERSON><PERSON>, CDPSession, Page } from 'puppeteer-core';
import { WebSocket } from 'ws';

import {
  InteractiveCommands,
  HostCommands,
  Message,
  ClientCommands,
  StartMessage,
  ClipboardMessage,
} from './types.js';

import {
  EnterpriseChromeCDP,
  EnterpriseChromiumCDP,
} from '../../browsers/core.js';
import { CDPProxy } from '../cdp/proxy.js';
import { ChromeStealthBrowser } from '../../browsers/chrome.stealth.js';
import { ChromiumStealthBrowser } from '../../browsers/chromium.stealth.js';

// @ts-ignore - this is not typed
import keyboardModule from '../../shared/3rd-party/simple-keyboard/main.js';

const protocolCommands = Object.keys(InteractiveCommands);

const selectCSS = `div.cb-pvt-dropdown {
  position:fixed !important;
  background-color:White !important;
  border:1px solid #7f9db9 !important;
  overflow-x:hidden;
  overflow-y:auto;
  font-family: Arial;
  line-height: normal;
  font-size: 13px;
  cursor:Default;
  /* 1e90ff */
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

div.cb-pvt-dropdown .cb-pvt-option {
  padding: 0 5px;
  display:block;
  white-space: nowrap;
  overflow:hidden;
}

div.cb-pvt-dropdown .cb-pvt-option.cb-pvt-selected {
  background-color: #1e90ff;
  color: White;
}

div.cb-pvt-dropdown.cb-pvt-hidden {
  display:none;
}
`;

const iframeDetectionJS = `
(function(){
  let currentIframe = null;

  function updateFrameBounds() {
    const iframes = document.querySelectorAll('iframe');
    const frameData = Array.from(iframes).map(iframe => {
      const bounds = iframe.getBoundingClientRect();
      return {
        src: iframe.src,
        bounds: { x: bounds.left, y: bounds.top, width: bounds.width, height: bounds.height }
      };
    });

    window.__browserlessIframeData = frameData;

    window.dispatchEvent(new CustomEvent('browserless-iframe-update', {
      detail: { frames: frameData }
    }));
  }

  document.addEventListener('mouseover', (e) => {
    if (e.target.tagName === 'IFRAME' && currentIframe !== e.target) {
      currentIframe = e.target;
      updateFrameBounds();
    }
  });

  document.addEventListener('mouseout', (e) => {
    if (e.target.tagName === 'IFRAME' && currentIframe === e.target) {
      currentIframe = null;
    }
  });

  window.addEventListener('resize', updateFrameBounds);
  window.addEventListener('load', updateFrameBounds);
  updateFrameBounds();
})();
`;

const selectJS = `// REPLACEMENTS
// .ownerDocument with .ownerDocument
// .parentNode with .parentNode
// .target with .target

(function(){

  //noprotect
  var dropdownDiv = null;

  function getDropdownDiv(){
    if (dropdownDiv) {
      return dropdownDiv;
    }
    dropdownDiv = document.createElement("DIV");
    dropdownDiv.className = "cb-pvt-dropdown";

    dropdownDiv.addEventListener("mouseup", function(e){
      if (e.target.className.indexOf("cb-pvt-option") >= 0) {
        var option = e.target;
        var selectNode = dropdownDiv.selectNode;
        selectNode.value = option.getAttribute("data-value");

        var evt = new Event("change", {
          view: window,
          bubbles: true,
          cancelable: true
        });
        selectNode.dispatchEvent(evt);

        hideDropdown();
      }
    }, false);
    debugger;
    document.body.appendChild(dropdownDiv);
    return dropdownDiv;
  }

  function isDropdownHidden(){
    if (!dropdownDiv) { return true; }
    var displayStyle = dropdownDiv.style.display;
    if (displayStyle != null && displayStyle.toUpperCase() === "NONE") {
      return true;
    }
    return false;
  }

  function copyStylesFromSelectNode(){
    var selectNode = dropdownDiv.selectNode;
    var selectNodeCS = window.getComputedStyle(selectNode);
    dropdownDiv.style.fontFamily = selectNodeCS.fontFamily;
    dropdownDiv.style.fontSize = selectNodeCS.fontSize;
  }

  function positionDropdownDiv(){

    var selectNode = dropdownDiv.selectNode;

    dropdownDiv.style.display = "block";
    dropdownDiv.style.zIndex = "20001";
    dropdownDiv.style.bottom = "100%";
    dropdownDiv.style.right = "100%";

    var selectRect = selectNode.getBoundingClientRect();
    var vpRect = { height: window.innerHeight, width: window.innerWidth };

    var firstOption = dropdownDiv.children[0];
    var optionHeight = 15; // default height when no option present

    if (firstOption) {
      optionHeight = firstOption.getBoundingClientRect().height;
    }

    dropdownDiv.style.maxHeight = (optionHeight * 20 + 2) + "px";
    var dropdownMaxWidth = vpRect.width - 30,
        dropdownMinWidth = selectRect.width;
    dropdownDiv.style.maxWidth = (vpRect.width - 30) + "px";
    dropdownDiv.style.minWidth = selectRect.width + "px";

    var dropdownRect = dropdownDiv.getBoundingClientRect();

    // determine whether there is more space at the bottom or at the top
    var spaceAtBottom = vpRect.height - selectRect.bottom;
    var spaceAtTop = selectRect.top;
    var spaceAtLeft = selectRect.left + selectRect.width;
    var spaceAtRight = vpRect.width - selectRect.left;

    var outPosition = {};

    dropdownDiv.style.bottom = "";
    dropdownDiv.style.right = "";

    if ( dropdownRect.height <= spaceAtBottom ) {
      dropdownDiv.style.top = selectRect.bottom + "px";
      // dropdownDiv.style.maxHeight = (spaceAtBottom - ((spaceAtBottom-2) % optionHeight) + 2)  + "px";
    }
    else {
      if ( spaceAtBottom >= spaceAtTop ) {
        dropdownDiv.style.top = selectRect.bottom + "px";
        dropdownDiv.style.maxHeight = (spaceAtBottom - ((spaceAtBottom-2) % optionHeight) + 2)  + "px";
      }
      else {
        dropdownDiv.style.bottom = (vpRect.height - selectRect.top) + "px";
        dropdownDiv.style.maxHeight = (spaceAtTop - ((spaceAtTop-2) % optionHeight) + 2) + "px";
      }
    }

    if ( (dropdownRect.width + 20) <= spaceAtRight ) {
      dropdownDiv.style.left = selectRect.left + "px";
      // dropdownDiv.style.maxWidth = (spaceAtRight - 20) + "px";
    }
    else {
      if ( spaceAtRight >= spaceAtLeft ) {
        dropdownDiv.style.left = selectRect.left + "px";
        dropdownDiv.style.maxWidth = (spaceAtRight - 20) + "px";
      }
      else {
        dropdownDiv.style.right = (vpRect.width - selectRect.right) + "px";
        dropdownDiv.style.maxWidth = (spaceAtLeft - 20) + "px";
      }
    }
  }

  function scrollToSelected(){
    var hasScrollbar = dropdownDiv.scrollHeight > dropdownDiv.clientHeight;
    if (hasScrollbar) {
      var selectedOption = dropdownDiv.querySelector(".cb-pvt-selected");
      if (selectedOption) {
        dropdownDiv.scrollTop = selectedOption.offsetTop;
        // Reason for adding this here is quite odd
        // The event will get appended before DOMMutation call gets through meaning this will become unplayable
        // as the DIV with that ID will not be there
        setTimeout(function(){
          var evt = document.createEvent("CustomEvent");
          evt.initEvent("screenjsscroll", true, true);
          dropdownDiv.dispatchEvent(evt);
        }, 0);
      }
    }
  }

  var mousedownHideDropdownHandler, hoverOptionHandler,
      scrollHideDropdownHandler, mousewheelDropdownHandler,
      focusHideDropdownHandler, changeUpdateDropdownHandler;

  function showDropdown(){
    copyStylesFromSelectNode();
    positionDropdownDiv();
    scrollToSelected();
    mousedownHideDropdownHandler = function(e){
      if ( dropdownDiv == e.target || dropdownDiv.contains(e.target) ) { return; }
      if (!isDropdownHidden()) {
        hideDropdown();
      }
    };
    document.addEventListener("mousedown", mousedownHideDropdownHandler, true);
    scrollHideDropdownHandler = function(e){
      if ( dropdownDiv == e.target || dropdownDiv.contains(e.target) ) { return; }
      if (!isDropdownHidden()) {
        hideDropdown();
      }
    };
    document.addEventListener("scroll", scrollHideDropdownHandler, true);
    mousewheelDropdownHandler = function(e){
      if ( (e.deltaY > 0 && dropdownDiv.scrollHeight <= (dropdownDiv.scrollTop + dropdownDiv.clientHeight)) ||
           (e.deltaY < 0 && dropdownDiv.scrollTop == 0)
         ) {
        e.preventDefault();
      }
    };
    dropdownDiv.addEventListener("mousewheel", mousewheelDropdownHandler, true);
    hoverOptionHandler = function(e){
      var target = e.target;
      if (target.className.indexOf("cb-pvt-option")>=0){
        var selectedOption = dropdownDiv.querySelector(".cb-pvt-selected");
        selectedOption.className = selectedOption.className.replace(" cb-pvt-selected", "");
        target.className += " cb-pvt-selected";
      }
    };
    dropdownDiv.addEventListener("mouseenter", hoverOptionHandler, true);
    focusHideDropdownHandler = function(e){
      var selectNode = dropdownDiv.selectNode;
      if ( e.target != selectNode && !dropdownDiv.contains(e.target) ) {
        hideDropdown();
      }
    };
    document.addEventListener("focus", focusHideDropdownHandler, true);
    changeUpdateDropdownHandler = function(e){
      var selectNode = dropdownDiv.selectNode;
      var updatedValue = selectNode.value;
      var selectedOption = dropdownDiv.querySelector(".cb-pvt-selected");
      if (selectedOption) {
        selectedOption.className = selectedOption.className.replace(" cb-pvt-selected", "");
      }
      var newSelectedOption = dropdownDiv.querySelector(".cb-pvt-option[data-value='"+updatedValue+"']");
      if ( newSelectedOption ) {
        newSelectedOption.className += " cb-pvt-selected";
        // TODO: This causes multiple scroll events
        // in normal circumstances.
        // I believe this handler is there for changes using keyboard
        scrollToSelected();
      }

    };
    dropdownDiv.selectNode.addEventListener("change", changeUpdateDropdownHandler, false);
  }

  function hideDropdown(){
    dropdownDiv.style.display = "none";
    document.removeEventListener("mousedown", mousedownHideDropdownHandler, true);
    document.removeEventListener("scroll", scrollHideDropdownHandler, true);
    dropdownDiv.removeEventListener("scroll", mousewheelDropdownHandler, true);
    dropdownDiv.removeEventListener("mouseenter", hoverOptionHandler, true);
    document.removeEventListener("focus", focusHideDropdownHandler, true);
    dropdownDiv.selectNode.removeEventListener("change", changeUpdateDropdownHandler, false);
    document.body.removeChild(dropdownDiv);
    dropdownDiv = undefined;
  }

  function openDropdown(selectNode){
    if (!isDropdownHidden()) { return; }
    var dropdownDiv = getDropdownDiv();
    dropdownDiv.innerHTML = "";
    dropdownDiv.selectNode = selectNode;

    for (var i=0,len=selectNode.options.length;i<len;i++){
      var option = selectNode.options[i];
      var optionSpan = document.createElement("SPAN");
      optionSpan.setAttribute("data-value", option.value);
      optionSpan.setAttribute("data-text", option.text);
      optionSpan.textContent = option.text;
      optionSpan.className = "cb-pvt-option";
      if (option.selected) {
        optionSpan.className += " cb-pvt-selected";
      }
      dropdownDiv.appendChild(optionSpan);
    }
    showDropdown();
  }

  function proxifySelect(selectNode){
    if ( selectNode.hasAttribute("no-proxy-select") ) { return false; }
    if (selectNode.hasAttribute("multiple")) { return; }
    if(selectNode.hasProxified) return;
    selectNode.hasProxified = true;
    selectNode.addEventListener("mousedown", function(e){
      e.preventDefault();
      openDropdown(selectNode);
    });

    selectNode.addEventListener("keydown", function(e){
      if (e.keyCode == 13 || e.keyCode == 32) {
        if ( isDropdownHidden() ) {
          e.preventDefault();
          openDropdown(selectNode);
        }
        else {
          e.preventDefault();
          hideDropdown();
        }
      }
    });
  }

  document.addEventListener("DOMContentLoaded", function(event) {
    var selectNodes = document.getElementsByTagName("select");
    for (var i=0,len=selectNodes.length; i<len; i++){
      var selectNode = selectNodes[i];
      proxifySelect(selectNode);
    }
    proxifyDynamicallyAddedSelects();
  });

  //********************************
  // Observe changes happening later in the DOM
  //********************************
  // PERF: Check for performance implications of following
  // select the target node
  function proxifyDynamicallyAddedSelects(){

    var target = document.documentElement;

    // create an observer instance
    var observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if ( mutation.type == "childList" ) {
          var added = mutation.addedNodes;
          var newNode;
          for(var i=0; i<added.length; i++){
            if ( (newNode = added[i]).nodeName == "SELECT" ) {
              proxifySelect(newNode);
            }
            else if ( newNode.childElementCount > 0 && newNode.querySelectorAll) {
              var childIframes = newNode.querySelectorAll("select");
              for (var j=0; j < childIframes.length; j++){
                var select = childIframes[j];
                proxifySelect(select);
              }
            }
          }
        }
      });
    });

    // configuration of the observer:
    var config = { // attributes: true,
                   // attributeFilter: [],
                   childList: true,
                   subtree: true };

    // pass in the target node, as well as the observer options
    observer.observe(target, config);

    // later, you can stop observing
    // observer.disconnect();
  }
  //********************************
  // Observe changes happening later in the DOM ENDS HERE
  //********************************

})();
`;

// load the keyboard js and css
const keyboardJS = `(${keyboardModule.toString()})()`;

export class LiveServer extends EventEmitter {
  private pageId: string;
  private client: WebSocket;
  private keyboardEnabled: boolean;
  private browserlessBrowser:
    | EnterpriseChromeCDP
    | EnterpriseChromiumCDP
    | ChromeStealthBrowser
    | ChromiumStealthBrowser;
  private quality: number;
  private type: 'jpeg' | 'png';
  private puppeteerBrowser?: Browser;
  private page?: Page;
  private cdp?: CDPSession;
  private width?: number;
  private height?: number;
  private resizable?: boolean = true;

  protected logger = new Logger(`live-server`);

  private frameContexts: Map<
    string,
    {
      sessionId: string;
      targetId: string;
      bounds: { x: number; y: number; width: number; height: number };
    }
  > = new Map();
  private activeFrameContext: string | null = null;
  private frameDetectionEnabled = false;
  private iframeBoundaryPollingInterval?: NodeJS.Timeout;

  static navigationEvents = ['framenavigated', 'load', 'domcontentloaded'];

  constructor({
    client,
    browser,
    pageId,
    interactable,
    type,
    quality,
    resizable,
  }: {
    client: WebSocket;
    browser: LiveServer['browserlessBrowser'];
    pageId: string;
    interactable: boolean;
    type: 'jpeg' | 'png';
    quality: number;
    resizable: LiveServer['resizable'];
  }) {
    super();

    this.resizable = resizable;
    this.pageId = pageId;
    this.browserlessBrowser = browser;
    this.quality = quality;
    this.type = type;
    this.client = client;
    this.keyboardEnabled = false;
    this.client.once('close', this.close);
    this.client.on('message', async (message: string) => {
      const { command, data } = JSON.parse(message) as Message;

      this.logger.debug('Received message:', {
        command,
        hasData: !!data,
      });

      if (command === HostCommands.start) {
        return this.start(data as StartMessage);
      }

      if (command === HostCommands.enableKeyboard) {
        this.keyboardEnabled = true;
        return this.enableKeyboard();
      }

      if (command === HostCommands.setViewport && this.resizable) {
        return this.setViewport(data as any);
      }

      if (command === HostCommands.close) {
        return this.close();
      }

      if (command === HostCommands.pasteClipboard) {
        return this.pasteClipboard(data as ClipboardMessage);
      }

      if (command === HostCommands.requestClipboard) {
        return this.requestClipboard();
      }

      if (protocolCommands.includes(command)) {
        this.logger.debug('Protocol command detected:', {
          command,
          interactable,
          hasCdp: !!this.cdp,
        });
        if (interactable) {
          if (!this.cdp) return;
          const protocolCommand = command as InteractiveCommands;
          const targetSession = this.determineTargetSession(data);
          const transformedData = this.transformCoordinatesForFrame(data);
          return targetSession.send(protocolCommand, transformedData);
        } else {
          return;
        }
      }

      console.warn(`Unknown worker command:`, message);
    });
  }

  private send = (message: Message) => {
    this.client.send(JSON.stringify(message));
  };

  private onScreencastFrame = ({
    data,
    sessionId,
  }: {
    data: string;
    sessionId?: number;
  }) => {
    if (this.cdp) {
      this.client.send(Buffer.from(data, 'base64'), {
        compress: true,
        binary: true,
      });

      if (sessionId) {
        this.cdp.send('Page.screencastFrameAck', { sessionId }).catch(noop);
      }
    }
  };

  private setViewport = (data: {
    deviceScaleFactor?: number;
    width: number;
    height: number;
  }) => {
    this.width = data.width;
    this.height = data.height;
    this.page && this.page.setViewport(data);
  };

  public start = async ({
    width: clientWidth,
    height: clientHeight,
  }: StartMessage) => {
    const browserWSEndpoint = this.browserlessBrowser.wsEndpoint() as string;

    this.puppeteerBrowser = await puppeteer
      .connect({
        browserWSEndpoint,
        // Setting to null to avoid resizing the window to puppeteer's default size
        // which is 800x600. Otherwise let the specified width and height be used.
        defaultViewport: this.resizable
          ? { width: clientWidth, height: clientHeight }
          : null,
      })
      .catch((error) => {
        console.error(error);
        return undefined;
      });

    if (!this.puppeteerBrowser) {
      this.send({
        command: ClientCommands.error,
        data: `Couldn't establish a connection "${browserWSEndpoint}". Is your browser running?`,
      });
      return this.close();
    }

    const pages = await this.puppeteerBrowser.pages();
    const page = pages.find(
      // @ts-ignore private members
      (p) => p.target()._targetId === this.pageId,
    );

    if (!page) {
      this.send({
        command: ClientCommands.error,
        data: `Couldn't find page ID "${this.pageId}"`,
      });
      return this.close();
    }

    const { width, height } = this.resizable
      ? { width: clientWidth, height: clientHeight }
      : await page.evaluate(() => ({
          width: window.innerWidth,
          height: window.innerHeight,
        }));

    this.width = width;
    this.height = height;

    const onNavigation = () => {
      this.setViewport({
        width: this.width!,
        height: this.height!,
        deviceScaleFactor: 1,
      });

      this.keyboardEnabled && this.enableKeyboard();
    };

    // When headless is false, the page reverts to the original size,
    // so we need to set the viewport again when these events fire
    await page.setViewport({ height, width });
    LiveServer.navigationEvents.forEach((event) => {
      page.on(event, onNavigation);
    });
    await page.evaluateOnNewDocument(selectJS);
    await page.evaluateOnNewDocument(iframeDetectionJS);
    await page.evaluateOnNewDocument((selectCSS) => {
      window.addEventListener('load', () => {
        const style = document.createElement('style');
        style.innerHTML = selectCSS;
        document.body.appendChild(style);
      });
    }, selectCSS);

    // @ts-ignore private members
    this.cdp = page._client.call(page) as CDPSession;
    this.page = page;

    await page.addScriptTag({
      content: keyboardJS,
      type: 'text/javascript',
    });

    const onClose = once(() => {
      LiveServer.navigationEvents.forEach((event) =>
        page.off(event, onNavigation),
      );
      this.send({ command: ClientCommands.browserClose, data: null });
      this.close();
    });

    this.page.once('close', onClose);
    this.puppeteerBrowser.once('disconnected', onClose);

    const firstFrame = await this.cdp.send('Page.captureScreenshot', {
      format: this.type,
      quality: this.quality,
    });

    this.onScreencastFrame({ data: firstFrame.data });

    await this.cdp.send('Page.startScreencast', {
      format: this.type,
      quality: this.quality,
    });

    this.cdp.on('Page.screencastFrame', this.onScreencastFrame);

    await this.setupFrameDetection();

    this.startIframeBoundaryPolling();

    await this.setupClipboardMonitoring();

    await page.evaluateOnNewDocument(keyboardJS);

    this.send({
      command: ClientCommands.startComplete,
      data: {
        targetId: (page as any).target()._targetId,
        resizable: this.resizable,
        width,
        height,
      },
    });
  };

  public close = async () => {
    if (this.iframeBoundaryPollingInterval) {
      clearInterval(this.iframeBoundaryPollingInterval);
    }

    if (this.puppeteerBrowser?.connected && this.page) {
      const cdpProxy = this.browserlessBrowser.getCDPProxy() as CDPProxy;
      cdpProxy.send('Browserless.__liveURLComplete__', this.pageId);
    }

    if (this.puppeteerBrowser) this.puppeteerBrowser.disconnect();
    this.send({
      command: ClientCommands.runComplete,
      data: null,
    });
    this.emit('close');
    this.client.close();
  };

  private enableKeyboard = () => {
    this.page?.evaluate('window.__vkEnabled = true');
  };

  private determineTargetSession(eventData: any): CDPSession {
    if (eventData.iframeContext) {
      const frameContext = Array.from(this.frameContexts.values()).find(
        (ctx) =>
          ctx.targetId.includes(eventData.iframeContext.src) ||
          eventData.iframeContext.src.includes(ctx.targetId),
      );
      if (frameContext) {
        return this.getFrameSession(frameContext.sessionId);
      }
    }
    return this.cdp!;
  }

  private transformCoordinatesForFrame(eventData: any): any {
    if (eventData.iframeContext) {
      return {
        ...eventData,
        x: eventData.iframeContext.relativeX,
        y: eventData.iframeContext.relativeY,
      };
    }
    return eventData;
  }

  private getFrameSession(_sessionId: string): CDPSession {
    return this.cdp!;
  }

  private async setupFrameDetection(): Promise<void> {
    if (!this.cdp || this.frameDetectionEnabled) return;

    await this.cdp.send('Target.setAutoAttach', {
      autoAttach: true,
      waitForDebuggerOnStart: false,
      flatten: true,
    });

    this.cdp.on('Target.attachedToTarget', this.onFrameAttached);
    this.cdp.on('Target.detachedFromTarget', this.onFrameDetached);

    this.frameDetectionEnabled = true;
  }

  private onFrameAttached = async (event: any) => {
    const { targetInfo, sessionId } = event;

    if (targetInfo.type === 'iframe') {
      this.frameContexts.set(targetInfo.targetId, {
        sessionId,
        targetId: targetInfo.targetId,
        bounds: { x: 0, y: 0, width: 0, height: 0 }, // Will be updated
      });
    }
  };

  private onFrameDetached = (event: any) => {
    const { targetId } = event;
    this.frameContexts.delete(targetId);

    if (this.activeFrameContext === targetId) {
      this.activeFrameContext = null;
    }
  };

  private startIframeBoundaryPolling = () => {
    this.iframeBoundaryPollingInterval = setInterval(async () => {
      if (!this.page) return;

      try {
        const iframeData = await this.page.evaluate(() => {
          const data = (window as any).__browserlessIframeData;
          if (data) {
            (window as any).__browserlessIframeData = null;
            return data;
          }
          return null;
        });

        if (iframeData) {
          this.handleIframeBoundsUpdate({ frames: iframeData });
          this.sendIframeBoundsToClient(iframeData);
        }
      } catch (error) {}
    }, 100);
  };

  private sendIframeBoundsToClient = (frames: any[]) => {
    this.send({
      command: ClientCommands.iframeBoundsUpdate,
      data: { frames },
    });
  };

  private handleIframeBoundsUpdate = (data: any) => {
    const { frames } = data;
    frames.forEach((frame: any) => {
      const frameContext = Array.from(this.frameContexts.values()).find(
        (ctx) =>
          ctx.targetId.includes(frame.src) || frame.src.includes(ctx.targetId),
      );
      if (frameContext) {
        frameContext.bounds = frame.bounds;
      }
    });
  };

  /**
   * Pastes content from the client's clipboard into the browser
   */
  private pasteClipboard = async (clipboardData: ClipboardMessage) => {
    if (!this.page) return;

    this.logger.debug('Pasting clipboard content to browser:', {
      contentLength: clipboardData.content.length,
      type: clipboardData.type,
      preview: clipboardData.content.substring(0, 50) + '...',
    });

    let pasteSuccessful = false;

    try {
      // First, try to write the content to the browser's clipboard
      await this.page.evaluate((content) => {
        return navigator.clipboard.writeText(content);
      }, clipboardData.content);

      // Get the platform to determine which key to use
      const platform = await this.page.evaluate(() => navigator.platform);
      const isMac = platform.includes('Mac');
      const modifierKey = isMac ? 'Meta' : 'Control';

      this.logger.debug('Detected platform:', { platform, isMac, modifierKey });

      // Try multiple approaches to paste, but stop after first success

      // Approach 1: Direct text insertion for input/textarea elements
      try {
        const insertResult = await this.page.evaluate((content) => {
          const activeElement = document.activeElement as
            | HTMLInputElement
            | HTMLTextAreaElement;
          if (
            activeElement &&
            (activeElement.tagName === 'INPUT' ||
              activeElement.tagName === 'TEXTAREA')
          ) {
            const start = activeElement.selectionStart || 0;
            const end = activeElement.selectionEnd || 0;
            const currentValue = activeElement.value;

            // Insert the content at the cursor position
            const newValue =
              currentValue.substring(0, start) +
              content +
              currentValue.substring(end);
            activeElement.value = newValue;

            // Set cursor position after the inserted text
            const newCursorPos = start + content.length;
            activeElement.setSelectionRange(newCursorPos, newCursorPos);

            // Trigger input and change events
            activeElement.dispatchEvent(new Event('input', { bubbles: true }));
            activeElement.dispatchEvent(new Event('change', { bubbles: true }));

            return {
              success: true,
              elementType: activeElement.tagName,
              method: 'direct_insertion',
            };
          }
          return { success: false, reason: 'no_input_element' };
        }, clipboardData.content);

        this.logger.debug('Direct insertion result:', insertResult);

        if (insertResult.success) {
          this.logger.debug('Successfully pasted via direct insertion');
          pasteSuccessful = true;
          return;
        }
      } catch (error) {
        this.logger.warn('Direct insertion failed:', error);
      }

      // Approach 2: Keyboard shortcut simulation (only if direct insertion failed)
      if (!pasteSuccessful) {
        try {
          await this.page.keyboard.down(modifierKey);
          await this.page.keyboard.press('KeyV');
          await this.page.keyboard.up(modifierKey);
          this.logger.debug('Attempted paste via keyboard shortcut');
          pasteSuccessful = true;
          return;
        } catch (error) {
          this.logger.warn('Keyboard paste failed:', error);
        }
      }

      // Approach 3: Focus and try again with execCommand (only if previous approaches failed)
      if (!pasteSuccessful) {
        try {
          const execResult = await this.page.evaluate((content) => {
            // Try to focus on any input element first
            const activeElement = document.activeElement;
            if (activeElement && activeElement instanceof HTMLElement) {
              activeElement.focus();
            }

            // Try execCommand as fallback
            const success = document.execCommand('insertText', false, content);
            return { success, method: 'execCommand' };
          }, clipboardData.content);

          this.logger.debug('ExecCommand result:', execResult);

          if (execResult.success) {
            this.logger.debug('Successfully pasted via execCommand');
            pasteSuccessful = true;
            return;
          }
        } catch (error) {
          this.logger.warn('ExecCommand paste failed:', error);
        }
      }

      this.logger.debug('Paste operation completed', { pasteSuccessful });
    } catch (error) {
      this.logger.warn('Failed to paste clipboard content:', error);
    }
  };

  /**
   * Reads content from the browser's clipboard and sends it to the client
   */
  private requestClipboard = async () => {
    if (!this.page) return;

    this.logger.debug(
      'Requesting clipboard content by triggering copy and capture',
    );
    try {
      // Instead of trying to read the clipboard directly, we'll trigger a copy
      // and capture the selected content through a different mechanism
      const clipboardContent = await this.page.evaluate(async () => {
        // First, try to get any selected text
        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
          return selection.toString();
        }

        // If no selection, try to get the focused element's value
        const activeElement = document.activeElement as
          | HTMLInputElement
          | HTMLTextAreaElement;
        if (
          activeElement &&
          (activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA')
        ) {
          if (
            activeElement.selectionStart !== null &&
            activeElement.selectionEnd !== null &&
            activeElement.selectionStart !== undefined &&
            activeElement.selectionEnd !== undefined
          ) {
            const start = activeElement.selectionStart;
            const end = activeElement.selectionEnd;
            if (start !== end) {
              return activeElement.value.substring(start, end);
            } else {
              // If no selection in input, return the whole value
              return activeElement.value;
            }
          }
          return activeElement.value;
        }

        // Try to simulate a select all and copy to capture content
        try {
          // Focus on body and try select all
          document.body.focus();
          document.execCommand('selectAll');
          const newSelection = window.getSelection();
          if (newSelection && newSelection.toString().length > 0) {
            const content = newSelection.toString();
            // Clear the selection
            newSelection.removeAllRanges();
            return content;
          }
        } catch (error) {
          console.warn('Failed to select all content:', error);
        }

        return '';
      });

      this.logger.debug('Successfully captured content from browser:', {
        contentLength: clipboardContent.length,
        preview: clipboardContent.substring(0, 50) + '...',
      });

      if (clipboardContent.length > 0) {
        this.send({
          command: ClientCommands.clipboardContent,
          data: {
            content: clipboardContent,
            type: 'text',
          } as ClipboardMessage,
        });
      } else {
        this.logger.debug('No content found to copy');
        // Send empty content to let client know the operation completed
        this.send({
          command: ClientCommands.clipboardContent,
          data: {
            content: '',
            type: 'text',
          } as ClipboardMessage,
        });
      }
    } catch (error) {
      this.logger.warn('Failed to capture clipboard content:', error);
      // Send empty content on error
      this.send({
        command: ClientCommands.clipboardContent,
        data: {
          content: '',
          type: 'text',
        } as ClipboardMessage,
      });
    }
  };

  /**
   * Sets up automatic clipboard monitoring to detect copy operations
   */
  private setupClipboardMonitoring = async () => {
    if (!this.page) return;

    this.logger.debug('Setting up clipboard monitoring');

    // First expose the function that will handle clipboard updates
    await this.page.exposeFunction(
      'browserlessClipboardUpdate',
      (clipboardData: ClipboardMessage) => {
        this.logger.debug('Automatic clipboard update detected:', {
          contentLength: clipboardData.content.length,
          type: clipboardData.type,
          preview: clipboardData.content.substring(0, 50) + '...',
        });
        this.send({
          command: ClientCommands.clipboardContent,
          data: clipboardData,
        });
      },
    );

    // Inject clipboard monitoring script
    await this.page.evaluateOnNewDocument(() => {
      let lastClipboardContent = '';

      const sendClipboardUpdate = async (content: string) => {
        if (content !== lastClipboardContent && content.length > 0) {
          lastClipboardContent = content;
          try {
            // Use the exposed function to send clipboard content
            (window as any).browserlessClipboardUpdate({
              content,
              type: 'text',
            });
          } catch (error) {
            console.warn('Failed to send clipboard update:', error);
          }
        }
      };

      const captureCurrentSelection = () => {
        // Get selected text
        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
          return selection.toString();
        }

        // Get selected text from input/textarea
        const activeElement = document.activeElement as
          | HTMLInputElement
          | HTMLTextAreaElement;
        if (
          activeElement &&
          (activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA')
        ) {
          if (
            activeElement.selectionStart !== null &&
            activeElement.selectionEnd !== null &&
            activeElement.selectionStart !== undefined &&
            activeElement.selectionEnd !== undefined
          ) {
            const start = activeElement.selectionStart;
            const end = activeElement.selectionEnd;
            if (start !== end) {
              return activeElement.value.substring(start, end);
            }
          }
        }

        return '';
      };

      // Monitor copy events
      document.addEventListener('copy', () => {
        // Immediately capture the selected content when copy event fires
        const content = captureCurrentSelection();
        if (content) {
          sendClipboardUpdate(content);
        }
      });

      // Monitor keyboard shortcuts for copy (more reliable)
      document.addEventListener('keydown', (e) => {
        if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
          // Capture content immediately when copy shortcut is pressed
          setTimeout(() => {
            const content = captureCurrentSelection();
            if (content) {
              sendClipboardUpdate(content);
            }
          }, 10); // Small delay to ensure the copy operation has started
        }
      });

      // Also monitor for selection changes that might indicate a copy operation
      let selectionTimeout: NodeJS.Timeout;
      document.addEventListener('selectionchange', () => {
        clearTimeout(selectionTimeout);
        selectionTimeout = setTimeout(() => {
          const selection = window.getSelection();
          if (selection && selection.toString().length > 0) {
            // Store the current selection for potential copy operations
            (window as any).__browserlessLastSelection = selection.toString();
          }
        }, 100);
      });

      // Listen for right-click context menu copy
      document.addEventListener('contextmenu', () => {
        setTimeout(() => {
          const content = captureCurrentSelection();
          if (content) {
            sendClipboardUpdate(content);
          }
        }, 100);
      });
    });

    this.logger.debug('Clipboard monitoring setup complete');
  };
}

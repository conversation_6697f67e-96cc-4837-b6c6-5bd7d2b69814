import { Socket } from 'net';
import { Duplex } from 'stream';
import WebSocket, { WebSocketServer } from 'ws';
import {
  once,
  getRandomNegativeInt,
  sleep,
  makeExternalURL,
  Logger,
  getTokenFromRequest,
  exists,
} from '@browserless.io/browserless';
import type { ProtocolMapping } from 'devtools-protocol/types/protocol-mapping.js';

import {
  blockSelectors,
  captchaNetworkCalls,
  getRandomArbitrary,
  getURLAndUA,
  getWAFData,
  identifyCaptcha,
  isMatch,
  modalBlocker,
  setGeetestResponse,
  startRecording,
  stopRecording,
  THREE_MINUTES_IN_MS,
  URLAndUA,
} from '../../utils.js';
import { SharedConfig } from '../../config.js';
import fileSystem from '../../file-system.js';
import { AugmentedRequest, CaptchaType } from '../../types.js';
import {
  logLiveUrlCompleted,
  logLiveUrlInvoked,
  logReconnectUrlCreated,
} from '../browserql/utils/url-logging.js';
import { asyncPoll } from '../browserql/utils.js';
import {
  friendlyCaptcha,
  geetest,
  hCaptcha,
  recaptcha,
} from '../browserql/constants.js';
import * as captchas from '../3rd-party/capsolver.js';
import {
  callRecaptchaCallback,
  findIframeURLs,
  getCaptchaImg,
  getTextCaptchaQuestion,
  setCaptchaValue,
  setHcaptchaResponse,
  setInputValue,
  setTextCaptchaAnswer,
} from '../browserql/browser.utils.js';
import {
  captchaLoggerFactory,
  detectCloudflareType,
  evalInBrowser,
  extractCaptchaSubtype,
  getDeepClientRect,
  sendCDPMessage,
  waitForSelector,
  setDownloadBehavior,
} from '../utils/cdp-utils.js';
import { getRecaptchaWidgetInfo } from '../browserql/browser.utils.js';
import { path } from '../browserql/cursor.js';
import { join } from 'path';
import { createReadStream, unlink } from 'fs';
import {
  createDomProtectionScript,
  createMathProtectionScript,
  createWorkerProtectionScript,
  createProfileProtectionScript,
  createCanvasProtectionScript,
  createInlineProfileProtectionScript,
} from './util.js';

const browserlessCDPAPI = {
  pageId: 'Browserless.pageId',
  startRecording: 'Browserless.startRecording',
  stopRecording: 'Browserless.stopRecording',
  liveURL: 'Browserless.liveURL',
  closeLiveURL: 'Browserless.closeLiveURL',
  reconnect: 'Browserless.reconnect',
  solveCaptcha: 'Browserless.solveCaptcha',
};

const browserlessCDPEvents = {
  liveComplete: 'Browserless.liveComplete',
  captchaFound: 'Browserless.captchaFound',
  heartbeat: 'Browserless.heartbeat',
};

const protectedBrowserlessCDP = {
  liveComplete: 'Browserless.__liveURLComplete__',
};

const targetClosed = () => ({
  method: 'Target.createTarget',
  params: {
    targetId: null,
  },
});

export class CDPProxy {
  protected req: AugmentedRequest;
  protected allowBrowserClose: boolean;
  protected retries: number;
  protected maxPages = 0;
  protected config: SharedConfig;
  protected allowLiveURLs: boolean;
  protected allowCaptchaSolving: boolean;
  protected allowRecording: boolean;
  protected keepUntilTimeStamp: number;
  protected allowReconnect: boolean;
  protected logger: Logger;
  protected record: boolean;

  /** sessionId, pageId */
  protected sessionIdToPageId: Map<string, string> = new Map();
  protected browserContexts: Map<string, string> = new Map();
  protected sessionIds: Map<string, string> = new Map();
  protected targets: Map<string, string> = new Map();
  protected ids: Map<string, string> = new Map();

  protected browser?: WebSocket;
  protected cdp?: WebSocket;

  protected cleanupFns: Array<() => Promise<void>> = [];
  protected requests: Map<
    string,
    {
      method: string;
      url: string;
      type: string;
      headers: Record<string, string>;
    }
  > = new Map();

  constructor({
    allowCaptchaSolving,
    allowLiveURLs,
    allowReconnect,
    allowRecording,
    allowBrowserClose,
    config,
    maxPages,
    record,
    retries,
    req,
    logger,
  }: {
    allowCaptchaSolving: CDPProxy['allowCaptchaSolving'];
    allowLiveURLs: CDPProxy['allowLiveURLs'];
    allowBrowserClose: CDPProxy['allowBrowserClose'];
    allowReconnect: CDPProxy['allowReconnect'];
    allowRecording: CDPProxy['allowRecording'];
    config: CDPProxy['config'];
    maxPages: CDPProxy['maxPages'];
    retries: CDPProxy['retries'];
    record: CDPProxy['record'];
    req: CDPProxy['req'];
    logger: CDPProxy['logger'];
  }) {
    this.logger = logger;
    this.logger.debug(
      {
        allowBrowserClose,
        maxPages,
      },
      `Instantiating new CDP-Proxy`,
    );
    this.allowCaptchaSolving = allowCaptchaSolving;
    this.allowLiveURLs = allowLiveURLs;
    this.allowReconnect = allowReconnect;
    this.allowRecording = allowRecording;
    this.config = config;
    this.allowBrowserClose = allowBrowserClose;
    this.maxPages = maxPages;
    this.record = record;
    this.retries = retries;
    this.req = req;
    this.keepUntilTimeStamp = 0;
  }

  protected onClose = once(async () => {
    const start = Date.now();
    this.logger.debug(`Shutting down Firewall Proxy`);

    for (const fn of this.cleanupFns) {
      await fn().catch((err) => {
        this.logger.error(`Error in shutdown`, err);
      });
    }

    this.logger.debug(`Shutdown completed in ${Date.now() - start}ms`);
  });

  /**
   * Extract captcha type and subtype from widget information for accurate logging
   * Supports reCAPTCHA and Cloudflare detailed detection
   */
  private async getCaptchaTypeAndSubtype(
    browser: WebSocket,
    sessionId: string,
    fallbackType: CaptchaType,
  ): Promise<{ captchaType: string; captchaSubtype?: string }> {
    // Handle Cloudflare detection with ray-id logic
    if (fallbackType === CaptchaType.Cloudflare) {
      try {
        // Use shared Cloudflare detection logic
        const { result: cloudflareInfo } = await evalInBrowser<any>({
          browser,
          sessionId,
          expression: `(${detectCloudflareType.toString()})()`,
          logger: this.logger,
        });

        // Use detection result if available, otherwise default to turnstile
        const captchaSubtype = cloudflareInfo?.type || 'turnstile';
        return {
          captchaType: 'cloudflare',
          captchaSubtype,
        };
      } catch (error) {
        this.logger.debug(
          'Failed to get detailed Cloudflare info, using fallback',
          error,
        );
      }

      // Fallback for Cloudflare - default assumption for undetectable cases
      return {
        captchaType: 'cloudflare',
        captchaSubtype: 'turnstile',
      };
    }

    // For non-reCAPTCHA and non-Cloudflare types, return as-is
    if (
      fallbackType !== CaptchaType.Recaptcha &&
      fallbackType !== CaptchaType.RecaptchaInvisible &&
      fallbackType !== CaptchaType.RecaptchaV3
    ) {
      return {
        captchaType: fallbackType,
        captchaSubtype: extractCaptchaSubtype(fallbackType),
      };
    }

    try {
      // Try to get detailed reCAPTCHA widget info
      const { result: widgetInfo } = await evalInBrowser<any>({
        browser,
        sessionId,
        expression: `(${getRecaptchaWidgetInfo.toString()})()`,
        logger: this.logger,
      });

      if (widgetInfo && widgetInfo.sitekey) {
        const isEnterprise = widgetInfo.enterprise || false;
        const version = widgetInfo.version;

        if (version === 'v3') {
          return {
            captchaType: 'recaptcha',
            captchaSubtype: isEnterprise ? 'v3-enterprise' : 'v3',
          };
        } else if (version === 'v2_invisible') {
          return {
            captchaType: 'recaptcha',
            captchaSubtype: isEnterprise ? 'invisible-enterprise' : 'invisible',
          };
        } else {
          return {
            captchaType: 'recaptcha',
            captchaSubtype: isEnterprise ? 'v2-enterprise' : 'v2-checkbox',
          };
        }
      }
    } catch (error) {
      this.logger.debug(
        'Failed to get detailed reCAPTCHA info, using fallback',
        error,
      );
    }

    // Fallback to enum-based extraction
    return {
      captchaType: fallbackType,
      captchaSubtype: extractCaptchaSubtype(fallbackType),
    };
  }

  protected onCDPMessage = async (data: WebSocket.RawData) => {
    const browser = this.browser as WebSocket;
    const cdp = this.cdp as WebSocket;

    const parsed = JSON.parse(data.toString());

    this.logger.trace(`CDP>`, JSON.stringify(parsed, null, '  '));

    // Handle the Browserless CDP API
    if (parsed.method === browserlessCDPAPI.pageId) {
      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          sessionId: parsed.sessionId,
          result: {
            pageId: this.sessionIdToPageId.get(parsed.sessionId),
          },
        }),
      );
    }

    if (parsed.method === 'Browser.setDownloadBehavior') {
      this.logger.debug(
        parsed.params,
        'Browser.setDownloadBehavior intercepted, modifying path',
      );
      parsed.params.behavior = 'allow';
      parsed.params.eventsEnabled = true;
      parsed.params.downloadPath = await this.config.getDownloadsDir();
    }

    if (parsed.method === browserlessCDPAPI.solveCaptcha) {
      if (!this.allowCaptchaSolving) {
        const message = `Captcha solving isn't available, please refer to "https://docs.browserless.io/Recipes/captcha-solving" for details.`;

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              ok: false,
              captchaFound: false,
              solveAttempted: false,
              message,
              solved: false,
              error: message,
            },
          }),
        );
      }

      const apiKey = getTokenFromRequest(this.req);
      const res = await evalInBrowser<string>({
        browser,
        sessionId: parsed.sessionId,
        expression: `{ website: location.href };`,
        logger: this.logger,
      });

      const { result: captchaType, error } = await evalInBrowser<CaptchaType>({
        browser,
        sessionId: parsed.sessionId,
        expression: `(${identifyCaptcha.toString()})()`,
        logger: this.logger,
      });

      if (error) {
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              ok: false,
              captchaFound: false,
              solveAttempted: false,
              message: error,
            },
          }),
        );
      }

      // Get detailed captcha type and subtype information
      const { captchaType: detailedType, captchaSubtype } =
        await this.getCaptchaTypeAndSubtype(
          browser,
          parsed.sessionId,
          captchaType,
        );

      const captchaLogger = captchaLoggerFactory(
        apiKey!,
        detailedType,
        res.result,
        this.logger,
        this.config,
        'baas',
        captchaSubtype, // Use detected subtype instead of parsing
      );
      captchaLogger.attempt();

      if (captchaType === CaptchaType.Hcaptcha) {
        const res = await this.solveHCaptcha({
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
          cdp,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              ...res,
            },
          }),
        );
      }

      if (
        captchaType === CaptchaType.Recaptcha ||
        captchaType === CaptchaType.RecaptchaInvisible
      ) {
        const res = await this.solveRecaptcha({
          isInvisible: captchaType === CaptchaType.RecaptchaInvisible,
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
          cdp,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              ...res,
            },
          }),
        );
      }

      if (captchaType === CaptchaType.RecaptchaV3) {
        const res = await this.solveRecaptchaV3({
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: { ...res },
          }),
        );
      }

      if (captchaType === CaptchaType.Cloudflare) {
        const res = await this.solveCloudflare({
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              ...res,
            },
          }),
        );
      }

      if (captchaType === CaptchaType.Friendlycaptcha) {
        const res = await this.solveFriendlyCaptcha({
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              ...res,
            },
          }),
        );
      }

      if (captchaType === CaptchaType.Normal) {
        const res = await this.solveNormalCaptcha({
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              ...res,
            },
          }),
        );
      }

      if (captchaType === CaptchaType.Geetest) {
        const res = await this.solveGeeTest({
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: { ...res },
          }),
        );
      }

      if (captchaType === CaptchaType.TextCaptcha) {
        const res = await this.solveTextCaptcha({
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: { ...res },
          }),
        );
      }

      if (captchaType === CaptchaType.AmazonWAF) {
        const res = await this.solveAmazonWAF({
          timeout: THREE_MINUTES_IN_MS,
          sessionId: parsed.sessionId,
          browser,
        });

        const status = res.solved ? 'success' : 'failure';
        captchaLogger[status]();

        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: { ...res },
          }),
        );
      }

      captchaLogger.failure();

      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          sessionId: parsed.sessionId,
          result: {
            ok: false,
            captchaFound: false,
            solveAttempted: false,
            message: 'Captcha not found in page or not supported',
          },
        }),
      );
    }

    if (parsed.method === browserlessCDPAPI.reconnect) {
      const timeout = parsed.params?.timeout ?? this.config.getTimeout();
      const apiKey = getTokenFromRequest(this.req);
      if (!this.allowReconnect) {
        if (apiKey) {
          logReconnectUrlCreated(
            apiKey,
            'baas',
            false,
            timeout,
            this.config,
            this.logger,
          );
        }
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              error: `Reconnect is not supported.`,
              browserWSEndpoint: null,
            },
          }),
        );
      }
      const auth = parsed.params?.auth;
      this.allowBrowserClose = false;
      this.setKeepUntil(timeout);

      const browserID = this.browser?.url.split('/').pop() as string;

      this.logger.debug(`Saving reconnect to "${browserID}"`);
      const reconnectID = fileSystem.saveId(browserID, timeout, { auth });

      if (apiKey) {
        logReconnectUrlCreated(
          apiKey,
          'baas',
          true,
          timeout,
          this.config,
          this.logger,
        );
      }

      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          sessionId: parsed.sessionId,
          result: {
            auth,
            error: null,
            browserWSEndpoint: makeExternalURL(
              this.config.getExternalWebSocketAddress(),
              'reconnect',
              reconnectID,
            ),
          },
        }),
      );
    }

    if (parsed.method === browserlessCDPAPI.liveURL) {
      const quality: number = parsed.params?.quality ?? 100;
      const timeout: number = parsed.params?.timeout ?? 30000;
      const resizable: boolean = parsed.params?.resizable ?? true;
      const interactable: boolean = parsed.params?.interactable ?? true;
      const type: string = parsed.params?.type ?? 'jpeg';
      const apiKey = getTokenFromRequest(this.req);
      if (!this.allowLiveURLs) {
        if (apiKey) {
          logLiveUrlInvoked({
            created: false,
            token: apiKey,
            endpointType: 'baas',
            type,
            quality,
            interactable,
            timeout,
            config: this.config,
            logger: this.logger,
          });
        }
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              error: `Live URLs are not supported.`,
              liveURL: null,
            },
          }),
        );
      }
      const browserID: string = this.browser?.url.split('/').pop()!;
      const pageID = this.sessionIdToPageId.get(parsed.sessionId)!;
      const liveURLLocation = `${browserID}.${pageID}`;
      const liveURLId = fileSystem.saveId(liveURLLocation, timeout, {
        type,
        quality,
        interactable,
        resizable,
      });

      this.logger.debug(`Saving "${liveURLLocation}"`);

      if (apiKey) {
        logLiveUrlInvoked({
          created: true,
          token: apiKey,
          endpointType: 'baas',
          type,
          quality,
          interactable,
          timeout,
          liveURLId,
          config: this.config,
          logger: this.logger,
        });
      }

      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          sessionId: parsed.sessionId,
          result: {
            error: null,
            liveURLId,
            liveURL: makeExternalURL(
              this.config.getExternalAddress(),
              'live',
              `index.html?i=${liveURLId}&t=${timeout}`,
            ),
          },
        }),
      );
    }

    if (parsed.method === browserlessCDPAPI.closeLiveURL) {
      if (!this.allowLiveURLs) {
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              error: `Live URLs are not supported.`,
              liveURL: null,
            },
          }),
        );
      }
      const liveURLId: string = parsed.params?.liveURLId;
      if (!liveURLId) {
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              error: `The "liveURLId" parameter is required.`,
              liveURL: null,
            },
          }),
        );
      }
      fileSystem.deleteId(liveURLId);
      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          sessionId: parsed.sessionId,
          result: {
            error: null,
            liveURLId,
          },
        }),
      );
    }

    if (parsed.method === browserlessCDPAPI.startRecording) {
      if (!this.allowRecording) {
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              error: `Recording is not supported`,
              recording: false,
            },
          }),
        );
      }

      if (!this.record) {
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              error: `Connect to browser setting a record query-param to "true" in order to use recording`,
              recording: false,
            },
          }),
        );
      }

      // Inject cookie-consent-blocker
      await sendCDPMessage(
        browser,
        'Runtime.evaluate',
        getRandomNegativeInt(),
        {
          returnByValue: true,
          awaitPromise: false,
          expression: `(${modalBlocker.toString()})(${JSON.stringify(await blockSelectors())})`,
        },
        parsed.sessionId,
      ).catch((e) => {
        return {
          error: e.message,
        };
      });

      const response = await sendCDPMessage(
        browser,
        'Runtime.evaluate',
        getRandomNegativeInt(),
        {
          returnByValue: true,
          awaitPromise: false,
          expression: `(${startRecording.toString()})("${this.sessionIdToPageId.get(parsed.sessionId)!}")`,
        },
        parsed.sessionId,
      ).catch((e) => {
        return {
          error: e.message,
        };
      });

      const err = (response as any).error;

      if (err) {
        this.logger.error(`Error starting recording: ${err}`);
      }

      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          sessionId: parsed.sessionId,
          result: {
            ...((response as any).result || {}),
            error: err || null,
            recording: !Boolean(err),
          },
        }),
      );
    }

    if (parsed.method === browserlessCDPAPI.stopRecording) {
      if (!this.allowRecording) {
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              error: `Recording is not supported`,
              recording: false,
            },
          }),
        );
      }

      if (!this.record) {
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            sessionId: parsed.sessionId,
            result: {
              error: `Connect to browser setting a record query-param to "true" in order to use recording`,
              recording: false,
            },
          }),
        );
      }

      const pageId = this.sessionIdToPageId.get(parsed.sessionId)!;
      const response = await sendCDPMessage(
        browser,
        'Runtime.evaluate',
        getRandomNegativeInt(),
        {
          returnByValue: true,
          awaitPromise: true,
          expression: `(${stopRecording.toString()})("${pageId}")`,
        },
        parsed.sessionId,
      ).catch((e) => {
        return {
          error: e.message,
        };
      });

      const err = (response as any).error;

      if (err) {
        this.logger.error(`Error stopping recording: ${err}`);
      }

      const pollForWebm = new Promise<Buffer>(async (resolve, reject) => {
        const downloadPath = await this.config.getDownloadsDir();
        const filePath = join(downloadPath, `${pageId}.webm`);
        const tmpPath = join(downloadPath, `${pageId}.webm.crdownload`);

        try {
          const resolvedFilePath = await asyncPoll(
            async () => {
              if (!(await exists(filePath)) || (await exists(tmpPath))) {
                return null;
              }
              return filePath;
            },
            500,
            30 * 1000, // 30 seconds, in case the file is really big
          );

          if (!resolvedFilePath) {
            reject(new Error('WebM file not found within timeout'));
            return;
          }

          const chunks: Buffer[] = [];
          const stream = createReadStream(resolvedFilePath);

          stream.on('data', (chunk: string | Buffer) => {
            const bufferChunk =
              typeof chunk === 'string' ? Buffer.from(chunk) : chunk;
            chunks.push(bufferChunk);
          });

          stream.on('end', () => {
            unlink(filePath, () => {});
            resolve(Buffer.concat(chunks));
          });

          stream.on('error', (error) => {
            unlink(filePath, () => {});
            reject(error);
          });
        } catch (error) {
          unlink(filePath, () => {});
          reject(error);
        }
      });

      const bin = await pollForWebm;

      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          sessionId: parsed.sessionId,
          result: {
            value: bin,
            error: err || null,
            recording: !Boolean(err),
          },
        }),
      );
    }

    // Alter filter to disallow Puppeteer to see background workers
    // which commonly trip up bot detection algo's
    if (parsed.method === 'Target.setDiscoverTargets') {
      this.logger.debug(`Overriding default "Target.setDiscoverTargets"`);
      parsed.params.filter = [
        { type: 'tab', exclude: true },
        { type: 'worker', exclude: true },
        { type: 'page', exclude: false },
        { type: 'browser', exclude: false },
      ];
    }

    if (
      parsed.method === 'Emulation.setDeviceMetricsOverride' &&
      parsed.params.width === 800 &&
      parsed.params.height === 600
    ) {
      this.logger.debug(
        `Overriding default "Emulation.setDeviceMetricsOverride"`,
      );
      parsed.params.width = 0;
      parsed.params.height = 0;
    }

    // Don't let close happen
    if (!this.allowBrowserClose && parsed.method === 'Browser.close') {
      this.logger.debug(`Sending mock "Browser.close()" response`);

      if (this.ids.size) {
        this.logger.debug(
          `Pending messages are dispatching, pausing 100ms to complete`,
        );
        await sleep(100);
      }

      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          result: {},
        }),
      );
    }

    // Don't let consumers crash the browser
    if (!this.allowBrowserClose && parsed.method === 'Browser.crash') {
      this.logger.debug(`Sending mock Browser.crash response`);

      if (this.ids.size) {
        this.logger.debug(
          `Pending messages dispatching, pausing 100ms to complete`,
        );
        await sleep(100);
      }

      return cdp.send(
        JSON.stringify({
          id: parsed.id,
          result: {},
        }),
      );
    }

    // Handle Target.createTarget for page creation
    if (parsed.method === 'Target.createTarget') {
      this.logger.debug(`Inbound Target creation`);

      if (this.maxPages > 0 && this.targets.size >= this.maxPages) {
        return cdp.send(
          JSON.stringify({
            id: parsed.id,
            result: targetClosed(),
          }),
        );
      }
    }

    // Capture the ID so we can verify it when chrome responds
    this.ids.set(parsed.id, parsed.id);
    this.logger.trace('CDP>', parsed);
    browser.send(JSON.stringify(parsed));
  };

  protected onBrowserMessage = async (data: WebSocket.RawData) => {
    const cdp = this.cdp as WebSocket;
    const parsed = JSON.parse(data.toString());

    if (parsed.method === 'Network.requestWillBeSent') {
      this.requests.set(parsed.params.requestId, {
        method: parsed.params.request.method,
        url: parsed.params.request.url,
        type: (parsed.params.type || '').toLowerCase(),
        headers: parsed.params.request.headers,
      });
    }

    // Handle Target attachment to track sessions and pages
    if (parsed.method === 'Target.attachedToTarget') {
      const { sessionId, targetInfo } = parsed.params;
      const { targetId, type } = targetInfo;

      if (type === 'page') {
        this.logger.debug(`Session ${sessionId} attached to page ${targetId}`);
        this.sessionIdToPageId.set(sessionId, targetId);
        this.sessionIds.set(targetId, sessionId);
        this.targets.set(targetId, targetId);

        // Apply protections after a page context is created
        setTimeout(async () => {
          await this.applyProtections(sessionId);
        }, 1000); // Wait 1 second to ensure the context is fully established
      }

      if (type === 'browser') {
        this.browserContexts.set(sessionId, targetId);
      }
    }

    // Handle Target detachment to clean up session tracking
    if (parsed.method === 'Target.detachedFromTarget') {
      const { sessionId, targetId } = parsed.params;

      this.logger.debug(
        `Session ${sessionId} detached from target ${targetId}`,
      );

      if (this.sessionIds.has(targetId)) {
        this.sessionIdToPageId.delete(sessionId);
        this.sessionIds.delete(targetId);
        this.targets.delete(targetId);
      }

      if (this.browserContexts.has(sessionId)) {
        this.browserContexts.delete(sessionId);
      }
    }

    // Protect against fingerprinting
    if (parsed.method === 'Runtime.evaluate') {
      const expr = parsed.params?.expression || '';
      const messageId = parsed.id;
      const sessionId = parsed.sessionId;
      const contextId = parsed.params?.contextId;

      // Helper function to safely inject protection script with proper context
      const injectProtectionScript = async (script: string): Promise<void> => {
        const browser = this.browser;
        if (!browser || browser.readyState !== WebSocket.OPEN) {
          throw new Error('Browser not available or not connected');
        }

        // Create a promise that resolves when the script is executed
        return new Promise((resolve, reject) => {
          const evalId = getRandomNegativeInt();
          let timeoutId: NodeJS.Timeout;

          // Set up one-time handler for the evaluation response
          const handleResponse = (data: WebSocket.RawData) => {
            try {
              const response = JSON.parse(data.toString());
              if (response.id === evalId) {
                clearTimeout(timeoutId);
                browser.removeListener('message', handleResponse);

                if (response.error) {
                  reject(
                    new Error(
                      `Script injection failed: ${response.error.message}`,
                    ),
                  );
                } else {
                  resolve();
                }
              }
            } catch (parseError: unknown) {
              clearTimeout(timeoutId);
              browser.removeListener('message', handleResponse);
              const errorMessage =
                parseError instanceof Error
                  ? parseError.message
                  : 'Unknown parse error';
              reject(new Error(`Failed to parse response: ${errorMessage}`));
            }
          };

          // Set timeout to prevent hanging
          timeoutId = setTimeout(() => {
            browser.removeListener('message', handleResponse);
            reject(new Error('Script injection timed out'));
          }, 5000); // 5 second timeout

          try {
            browser.on('message', handleResponse);

            // Send the evaluation request with the correct session context
            browser.send(
              JSON.stringify({
                id: evalId,
                method: 'Runtime.evaluate',
                params: {
                  expression: script,
                  returnByValue: true,
                  contextId: contextId,
                  sessionId: sessionId,
                  timeout: 3000, // CDP timeout
                },
              }),
            );
          } catch (sendError: unknown) {
            clearTimeout(timeoutId);
            browser.removeListener('message', handleResponse);
            const errorMessage =
              sendError instanceof Error
                ? sendError.message
                : 'Unknown send error';
            reject(new Error(`Failed to send script: ${errorMessage}`));
          }
        });
      };

      try {
        if (
          expr.includes('getBoundingClientRect') ||
          expr.includes('getClientRects') ||
          expr.includes('performance') ||
          expr.includes('timing') ||
          expr.includes('DOMRect')
        ) {
          await injectProtectionScript(
            `(${createDomProtectionScript.toString()})()`,
          );

          const result = await sendCDPMessage(
            this.browser!,
            'Runtime.evaluate',
            getRandomNegativeInt(),
            {
              ...parsed.params,
              sessionId,
              contextId,
            },
            sessionId,
          );

          cdp.send(
            JSON.stringify({
              id: messageId,
              result,
            }),
          );
          this.ids.delete(messageId);
          return;
        }

        if (
          expr.includes('toDataURL') ||
          expr.includes('getImageData') ||
          expr.includes('toBlob')
        ) {
          await injectProtectionScript(
            `(${createCanvasProtectionScript.toString()})()`,
          );

          const result = await sendCDPMessage(
            this.browser!,
            'Runtime.evaluate',
            getRandomNegativeInt(),
            {
              ...parsed.params,
              sessionId,
              contextId,
            },
            sessionId,
          );

          cdp.send(
            JSON.stringify({
              id: messageId,
              result,
            }),
          );
          this.ids.delete(messageId);
          return;
        }

        // Handle other protection scripts similarly...
        if (
          expr.includes('Math.') ||
          expr.includes('Error') ||
          expr.includes('console.')
        ) {
          await injectProtectionScript(
            `(${createMathProtectionScript.toString()})()`,
          );
        }

        if (
          expr.includes('Worker') ||
          expr.includes('ServiceWorker') ||
          expr.includes('SharedWorker')
        ) {
          await injectProtectionScript(
            `(${createWorkerProtectionScript.toString()})()`,
          );
        }

        if (
          expr.includes('navigator.') ||
          expr.includes('window.history') ||
          expr.includes('sessionStorage') ||
          expr.includes('localStorage')
        ) {
          await injectProtectionScript(
            `(${createProfileProtectionScript.toString()})()`,
          );
        }

        // Execute original expression after all protections are in place
        const result = await sendCDPMessage(
          this.browser!,
          'Runtime.evaluate',
          getRandomNegativeInt(),
          {
            ...parsed.params,
            sessionId,
            contextId,
          },
          sessionId,
        );

        cdp.send(
          JSON.stringify({
            id: messageId,
            result,
          }),
        );
        this.ids.delete(messageId);
        return;
      } catch (error: unknown) {
        // Enhanced error handling with proper categorization
        let errorMessage: string;
        let errorCode: number;

        if (error instanceof Error) {
          if (error.message.includes('timeout')) {
            errorMessage = 'Protection script injection timed out';
            errorCode = 504;
          } else if (error.message.includes('parse')) {
            errorMessage = 'Failed to parse protection script response';
            errorCode = 500;
          } else if (error.message.includes('context')) {
            errorMessage = 'Invalid execution context';
            errorCode = 400;
          } else {
            errorMessage = error.message;
            errorCode = 500;
          }
        } else {
          errorMessage = 'Unknown error in fingerprinting protection';
          errorCode = 500;
        }

        this.logger.error(
          `Fingerprinting protection error (${errorCode}): ${errorMessage}`,
        );

        cdp.send(
          JSON.stringify({
            id: messageId,
            error: {
              code: errorCode,
              message: errorMessage,
              data: {
                timestamp: Date.now(),
                sessionId,
                contextId,
              },
            },
          }),
        );
        this.ids.delete(messageId);
        return;
      }
    }

    // Handle video codec detection with proper CDP response format
    if (
      parsed.method === 'Runtime.evaluate' &&
      parsed.params?.expression?.includes('canPlayType')
    ) {
      const messageId = parsed.id;
      this.ids.delete(messageId);

      return cdp.send(
        JSON.stringify({
          id: messageId,
          result: {
            result: {
              type: 'string',
              value: 'probably',
            },
          },
        }),
      );
    }

    // Handle browser profile metrics
    if (
      parsed.method === 'Runtime.evaluate' &&
      (parsed.params?.expression?.includes('navigator.') ||
        parsed.params?.expression?.includes('window.history') ||
        parsed.params?.expression?.includes('sessionStorage') ||
        parsed.params?.expression?.includes('localStorage'))
    ) {
      const profileProtectionScript = `(${createInlineProfileProtectionScript.toString()})()`;

      const messageId = parsed.id;
      this.ids.delete(messageId);

      try {
        await this.browser?.send(
          JSON.stringify({
            id: getRandomNegativeInt(),
            method: 'Runtime.evaluate',
            params: {
              expression: profileProtectionScript,
              returnByValue: true,
            },
          }),
        );

        const result = await sendCDPMessage(
          this.browser!,
          'Runtime.evaluate',
          getRandomNegativeInt(),
          parsed.params,
          parsed.sessionId,
        );

        return cdp.send(
          JSON.stringify({
            id: messageId,
            result: result,
          }),
        );
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        return cdp.send(
          JSON.stringify({
            id: messageId,
            error: {
              message: 'Error executing protected evaluation',
              data: errorMessage,
            },
          }),
        );
      }
    }

    this.logger.trace(`CHROME>`, parsed);

    // Don't mirror our protocol messages back to the client
    if (parsed.id < 0) return;

    if (this.allowCaptchaSolving) {
      // emit captchaFound events
      if (
        parsed.method === 'Network.responseReceived' &&
        captchaNetworkCalls.some((pattern) =>
          isMatch(parsed.params.response.url, pattern),
        )
      ) {
        const pageId = this.sessionIdToPageId.get(parsed.sessionId);
        if (!pageId) return;
        const sessionIds = [...this.sessionIdToPageId]
          .filter(([, p]) => p === pageId)
          .map(([s]) => s);
        sessionIds.forEach((sessionId) => {
          return cdp.send(
            JSON.stringify({
              method: browserlessCDPEvents.captchaFound,
              sessionId,
            }),
          );
        });
      }
    }

    // Remove the ID since it's been handled
    if (parsed.id) {
      this.ids.delete(parsed.id);
    }

    cdp.send(JSON.stringify(parsed));
  };

  // Add a new method to apply protections after context is created
  protected async applyProtections(sessionId: string): Promise<void> {
    const browser = this.browser;
    if (!browser || browser.readyState !== WebSocket.OPEN) {
      this.logger.error('Cannot apply protections: browser not connected');
      return;
    }

    // Check if this is a page session
    const pageId = this.sessionIdToPageId.get(sessionId);
    if (!pageId) {
      this.logger.debug(
        `No page ID found for session ${sessionId}, skipping protections`,
      );
      return;
    }

    // Helper function to execute scripts in the page context
    const executeInPage = async (
      name: string,
      script: string,
    ): Promise<boolean> => {
      try {
        // First try the direct approach that's working for user agent
        await sendCDPMessage(
          browser,
          'Runtime.evaluate',
          getRandomNegativeInt(),
          {
            expression: script,
            returnByValue: true,
            awaitPromise: false,
          },
          sessionId,
        );

        this.logger.debug(
          `${name} applied successfully for session ${sessionId}`,
        );
        return true;
      } catch (err) {
        this.logger.error(
          `Failed to apply ${name} for session ${sessionId}: ${err}`,
        );
        return false;
      }
    };

    // Apply user agent override - this works
    try {
      await sendCDPMessage(
        browser,
        'Emulation.setUserAgentOverride',
        getRandomNegativeInt(),
        {
          userAgent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
          acceptLanguage: 'en-US,en;q=0.9',
          platform: 'Win32',
        },
        sessionId,
      );
      this.logger.debug(
        `User agent override applied successfully for session ${sessionId}`,
      );
    } catch (err) {
      this.logger.error(
        `Failed to apply user agent override for session ${sessionId}: ${err}`,
      );
    }

    // Apply network prediction blocking
    await executeInPage(
      'network prediction blocking',
      `
      // Disable network predictions
      (() => {
        try {
          // Track blocked resources for debugging
          window._browserlessPrivacy = {
            blockedPrefetch: 0,
            blockedPrerender: 0,
            blockedPreload: 0,
            isActive: true
          };

          // Add toggle function for testing/debugging
          window._toggleBrowserlessPrivacy = function(enable) {
            window._browserlessPrivacy.isActive = enable !== false;
            console.debug('[Privacy Protection] ' +
              (window._browserlessPrivacy.isActive ? 'Enabled' : 'Disabled'));
            return window._browserlessPrivacy.isActive;
          };

          // Set saveData mode if available
          if (navigator.connection) {
            try {
              // Use a non-enumerable property to avoid detection
              Object.defineProperty(navigator.connection, 'saveData', {
                get: function() {
                  return window._browserlessPrivacy.isActive ? true : false;
                },
                enumerable: false,
                configurable: true
              });
            } catch (e) {
              console.debug('[Privacy Protection] Could not set saveData:', e);
            }
          }

          // Block prefetch - with safe fallback
          try {
            const originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
              const element = originalCreateElement.call(document, tagName);

              // Only modify link elements
              if (tagName.toLowerCase() === 'link') {
                const originalSetAttribute = element.setAttribute;
                element.setAttribute = function(name, value) {
                  // Only intercept rel attributes
                  if (name.toLowerCase() === 'rel' && window._browserlessPrivacy.isActive) {
                    const lowerValue = value.toLowerCase();

                    // Check for prediction-related values
                    if (lowerValue.includes('prefetch')) {
                      window._browserlessPrivacy.blockedPrefetch++;
                      console.debug('[Privacy Protection] Blocked prefetch');
                      return;
                    } else if (lowerValue.includes('prerender')) {
                      window._browserlessPrivacy.blockedPrerender++;
                      console.debug('[Privacy Protection] Blocked prerender');
                      return;
                    } else if (lowerValue.includes('preload') &&
                              // Allow preloading critical resources
                              !element.hasAttribute('as') ||
                              !['style', 'font', 'script'].includes(element.getAttribute('as'))) {
                      window._browserlessPrivacy.blockedPreload++;
                      console.debug('[Privacy Protection] Blocked preload');
                      return;
                    }
                  }

                  // Pass through for all other attributes
                  return originalSetAttribute.call(this, name, value);
                };
              }
              return element;
            };
          } catch (e) {
            console.debug('[Privacy Protection] Could not override createElement:', e);
          }

          console.debug('[Privacy Protection] Network prediction features blocked');
          return true;
        } catch (e) {
          console.error('[Privacy Protection] Error:', e);
          return false;
        }
      })();
    `,
    );

    // Wait a bit between protection applications
    await sleep(200);

    // Apply WebRTC protection
    await executeInPage(
      'WebRTC protection',
      `
      // Apply WebRTC IP protection
      (() => {
        try {
          // Check if WebRTC is supported before applying protection
          if (!window.RTCPeerConnection) {
            console.debug('[WebRTC Protection] RTCPeerConnection not available, skipping protection');
            return true;
          }

          // Store original functions
          const originalRTCPeerConnection = window.RTCPeerConnection;
          let originalGetUserMedia = null;

          if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            originalGetUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
          }

          // Override RTCPeerConnection to prevent IP leaks
          window.RTCPeerConnection = function(...args) {
            // Force using STUN servers that won't leak IPs
            if (args[0] && typeof args[0] === 'object') {
              const config = args[0];

              // Keep track of original servers for fallback
              const originalIceServers = config.iceServers ? [...config.iceServers] : [];

              // Only allow TURN servers with credentials
              if (config.iceServers) {
                config.iceServers = config.iceServers.filter(server => {
                  return server.urls &&
                        (server.urls.includes('turn:') ||
                          (Array.isArray(server.urls) &&
                          server.urls.some(url => url.includes('turn:'))));
                });

                // Force IP masking mode, but allow fallback
                config.iceTransportPolicy = 'relay';

                // If no TURN servers available, create a connection timeout monitor
                if (config.iceServers.length === 0) {
                  // Store original servers as a fallback but don't use them initially
                  config._originalIceServers = originalIceServers;
                }
              }
            }

            // Create the peer connection with our modified config
            const pc = new originalRTCPeerConnection(...args);

            // Monitor and filter ICE candidates
            const originalAddIceCandidate = pc.addIceCandidate;
            pc.addIceCandidate = function(candidate, ...rest) {
              try {
                // Only allow relay candidates unless we're in fallback mode
                if (candidate && candidate.candidate &&
                    !candidate.candidate.includes('typ relay') &&
                    !pc._triedFallback) {
                  // Silently drop non-relay candidates
                  return Promise.resolve();
                }
                return originalAddIceCandidate.apply(this, [candidate, ...rest]);
              } catch (e) {
                // If anything fails, just pass through to original
                return originalAddIceCandidate.apply(this, [candidate, ...rest]);
              }
            };

            return pc;
          };

          // Also protect webkitRTCPeerConnection for older browsers
          if (window.webkitRTCPeerConnection) {
            window.webkitRTCPeerConnection = window.RTCPeerConnection;
          }

          console.debug('[WebRTC Protection] Successfully applied WebRTC IP protection');
          return true;
        } catch(e) {
          console.error('[WebRTC Protection] Error:', e);
          return false;
        }
      })();
    `,
    );

    // Wait a bit between protection applications
    await sleep(200);

    // Try to apply script for future page loads
    try {
      // We'll try a direct approach for Page.addScriptToEvaluateOnNewDocument
      await sendCDPMessage(
        browser,
        'Page.addScriptToEvaluateOnNewDocument',
        getRandomNegativeInt(),
        {
          source: `
          // WebRTC Protection and Privacy Enhancements for all new page loads
          (() => {
            try {
              console.debug('[Privacy Protection] Setting up protections for new page');

              // Create privacy control object if not exists
              window._browserlessPrivacy = {
                blockedPrefetch: 0,
                blockedPrerender: 0,
                blockedPreload: 0,
                isActive: true
              };

              // Apply WebRTC protection
              if (window.RTCPeerConnection) {
                const originalRTCPeerConnection = window.RTCPeerConnection;
                window.RTCPeerConnection = function(...args) {
                  if (args[0] && typeof args[0] === 'object') {
                    const config = args[0];
                    if (config.iceServers) {
                      config.iceTransportPolicy = 'relay';
                    }
                  }
                  return new originalRTCPeerConnection(...args);
                };
              }

              // Block prefetch
              const originalCreateElement = document.createElement;
              document.createElement = function(tagName) {
                const element = originalCreateElement.call(document, tagName);
                if (tagName.toLowerCase() === 'link') {
                  const originalSetAttribute = element.setAttribute;
                  element.setAttribute = function(name, value) {
                    if (name.toLowerCase() === 'rel') {
                      const lowerValue = value.toLowerCase();
                      if (lowerValue.includes('prefetch') ||
                          lowerValue.includes('prerender') ||
                          lowerValue.includes('preload')) {
                        return;
                      }
                    }
                    return originalSetAttribute.call(this, name, value);
                  };
                }
                return element;
              };

              console.debug('[Privacy Protection] Applied to new page');
            } catch (e) {
              console.error('[Privacy Protection] Error in new page script:', e);
            }
          })();
        `,
        },
        sessionId,
      );
      this.logger.debug(
        `Page script injection applied successfully for session ${sessionId}`,
      );
    } catch (err) {
      this.logger.error(
        `Failed to apply page script injection for session ${sessionId}: ${err}`,
      );

      // Try alternative approach - inject a script that will run on DOMContentLoaded
      await executeInPage(
        'DOMContentLoaded script injection',
        `
        document.addEventListener('DOMContentLoaded', function() {
          // Apply WebRTC protection
          if (window.RTCPeerConnection) {
            const originalRTCPeerConnection = window.RTCPeerConnection;
            window.RTCPeerConnection = function(...args) {
              if (args[0] && typeof args[0] === 'object') {
                const config = args[0];
                if (config.iceServers) {
                  config.iceTransportPolicy = 'relay';
                }
              }
              return new originalRTCPeerConnection(...args);
            };
          }

          console.debug('[Privacy Protection] Applied via DOMContentLoaded');
        });
      `,
      );
    }

    this.logger.debug(
      `Applied all available protections for session ${sessionId}`,
    );
  }

  protected async setup(cdp: WebSocket, browser: WebSocket) {
    this.cdp = cdp;
    this.browser = browser;

    process.nextTick(async () => {
      const downloadDir = await this.config.getDownloadsDir();
      this.logger.debug(`Setting default download path to "${downloadDir}"`);

      // Only set download behavior here
      try {
        await setDownloadBehavior({
          browser,
          behavior: 'allow',
          downloadPath: downloadDir,
        });
        this.logger.debug('Download behavior set successfully');
      } catch (err) {
        this.logger.error(`Failed to set download behavior: ${err}`);
      }
    });

    cdp.on('message', this.onCDPMessage);
    browser.on('message', this.onBrowserMessage);

    const intervalId = setInterval(() => {
      const sessionIds = [...this.sessionIdToPageId];
      sessionIds.forEach(([sessionId]) => {
        this.logger.trace(`Sending heartbeat to session: ${sessionId}`);
        return cdp.send(
          JSON.stringify({
            method: browserlessCDPEvents.heartbeat,
            sessionId,
          }),
        );
      });
    }, this.config.getHeartBeatInterval());

    this.cleanupFns.push(async () => {
      cdp.off('message', this.onCDPMessage);
      browser.off('message', this.onBrowserMessage);

      this.ids.clear();
      this.browserContexts.clear();
      this.targets.clear();
      this.sessionIds.clear();
      this.sessionIdToPageId.clear();
      intervalId && clearInterval(intervalId);

      cdp.close();
      browser.close();
    });
  }

  protected setupConnection = async (
    target: string,
    retries: number,
  ): Promise<WebSocket> =>
    new Promise((resolve, reject) => {
      let responded = false;
      const browser = new WebSocket(target);

      this.logger.trace(
        `Attempting connecting to Chrome, retries available: ${retries}`,
      );

      const onError = (err: Error) => {
        this.logger.error(
          `Error setting up socket (retries: ${retries}): ${err}`,
        );
        browser.removeListener('open', onOpen);
        if (!responded && retries <= 0) {
          responded = true;
          this.logger.debug(`Rejecting connection setup request`);
          return reject(err);
        }
        --retries;
        return resolve(this.setupConnection(target, retries));
      };

      const onOpen = () => {
        if (responded) return;
        responded = true;
        browser.removeListener('error', onError);
        this.logger.trace(`Established connection to ${target}!`);
        return resolve(browser);
      };

      browser.once('error', onError).once('open', onOpen);
    });

  public keepUntilTime = () => this.keepUntilTimeStamp;

  public send = (message: string, pageId: string) => {
    const cdp = this.cdp;
    const sessionIds = [...this.sessionIdToPageId].filter(
      ([, pId]) => pId === pageId,
    );
    if (cdp && sessionIds.length) {
      if (message === protectedBrowserlessCDP.liveComplete) {
        const apiKey = getTokenFromRequest(this.req);
        if (apiKey) {
          logLiveUrlCompleted(apiKey, this.config, this.logger);
        }
        sessionIds.forEach(([sessionId]) => {
          this.logger.debug(
            `Sending "Browserless.liveComplete" message back to client ${sessionId}`,
          );
          return cdp.send(
            JSON.stringify({
              method: browserlessCDPEvents.liveComplete,
              sessionId,
            }),
          );
        });
      }
    }
  };

  public setKeepUntil(timeAlive: number) {
    const timestamp = Date.now() + timeAlive;

    this.keepUntilTimeStamp = this.keepUntilTimeStamp
      ? Math.max(this.keepUntilTimeStamp, timestamp)
      : timestamp;
  }

  public ws = async (
    req: AugmentedRequest,
    socket: Duplex,
    head: Buffer,
    opts: {
      changeOrigin: boolean;
      target: string;
    },
  ): Promise<void> =>
    new Promise(async (resolve, reject) => {
      const onClose = once(async () => {
        this.logger.debug(`Socket has closed, proceeding with shutdown`);
        await this.onClose();
        return resolve();
      });

      socket.once('close', onClose);

      this.logger.debug(`Setting up for inbound Websocket connection`);

      try {
        const browser = await this.setupConnection(opts.target, this.retries);
        const client = (await new Promise((r) =>
          new WebSocketServer({ noServer: true }).handleUpgrade(
            req,
            socket as Socket,
            head,
            (ws) => r(ws),
          ),
        )) as WebSocket;

        return this.setup(client, browser);
      } catch (err) {
        return reject(err);
      }
    });

  private async solveHCaptcha({
    timeout,
    sessionId,
    browser = this.browser,
  }: {
    timeout: number;
    sessionId: string;
    cdp?: WebSocket;
    browser?: WebSocket;
  }) {
    this.logger.info(
      `Attempting to find and solve hCaptcha for session ${sessionId}`,
    );
    const response = this.getResponseObject();
    const start = Date.now();
    const poll = 200;

    try {
      const sitekey = await asyncPoll(
        async () => {
          const requests = await this.getAllBrowserRequests(
            browser!,
            sessionId,
          );

          return requests.reduce((result: null | string, uri) => {
            if (result) return result;
            if (!isMatch(uri, hCaptcha.url)) {
              return null;
            }
            const url = new URL(uri);
            const params = url.hash
              ? new URLSearchParams(url.hash)
              : url.searchParams;
            const siteKey = params.get(hCaptcha.param);
            return siteKey;
          }, null);
        },
        poll,
        timeout,
      ).catch((error) => {
        this.logger.error('Error solving hCaptcha:', error);
        return null;
      });

      if (!sitekey) {
        response.time = Date.now() - start;
        return response;
      }

      response.found = true;

      const evaluateResponse = await evalInBrowser<URLAndUA>({
        browser: browser!,
        sessionId,
        expression: `(${getURLAndUA.toString()})()`,
        logger: this.logger,
      });

      const {
        result: { website, userAgent },
        error,
      } = evaluateResponse;

      if (error) {
        response.time = Date.now() - start;
        return response;
      }

      this.logger.info(
        `Found hCaptcha sitekey of "${sitekey}" and url of "${website}", solving..."`,
      );

      const res = await captchas.solveHCaptcha(website, sitekey, userAgent);

      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(${setHcaptchaResponse.toString()})(\`${hCaptcha.input}\`, \`${hCaptcha.iframe}\`, \`${res.data}\`)`,
        logger: this.logger,
      });
      await sleep(100);

      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;
      this.logger.error('Error solving hCaptcha:', error);

      return response;
    }
  }

  private async solveRecaptcha({
    timeout,
    sessionId,
    browser = this.browser,
    isInvisible,
  }: {
    timeout: number;
    sessionId: string;
    cdp?: WebSocket;
    browser?: WebSocket;
    isInvisible?: boolean;
  }) {
    this.logger.debug(
      `Attempting to solve ${isInvisible ? 'invisible' : ''} recaptcha v2`,
    );
    const response = this.getResponseObject();
    const start = Date.now();
    const poll = 200;

    try {
      const sitekey = isInvisible
        ? (
            await evalInBrowser<string>({
              browser: browser!,
              sessionId,
              expression: `(() => document.querySelector('[data-sitekey]').getAttribute('data-sitekey'))()`,
              logger: this.logger,
            })
          ).result
        : await asyncPoll(
            async () => {
              const requests = await this.getAllBrowserRequests(
                browser!,
                sessionId,
              );
              return requests.reduce((result: null | string, uri) => {
                if (result) return result;
                if (!isMatch(uri, recaptcha.url)) {
                  return null;
                }
                const url = new URL(uri);
                const siteKey = url.searchParams.get(recaptcha.param);
                return siteKey;
              }, null);
            },
            poll,
            timeout,
          ).catch((error) => {
            this.logger.error('Error solving recaptcha:', error);
            return null;
          });

      if (!sitekey) {
        this.logger.debug(
          `No g-recaptcha sitekey found for session ${sessionId} on ${sitekey}`,
        );
        response.time = Date.now() - start;
        return response;
      }

      response.found = true;

      const evaluateResponse = await evalInBrowser<URLAndUA>({
        browser: browser!,
        sessionId,
        expression: `(${getURLAndUA.toString()})()`,
        logger: this.logger,
      });
      const {
        result: { website, userAgent },
      } = evaluateResponse;

      this.logger.debug(
        `Found g-recaptcha sitekey of "${sitekey}" and url of "${website}", solving..."`,
      );

      const res = await captchas.solveRecaptcha(website, sitekey, userAgent);

      await waitForSelector({
        selector: recaptcha.input,
        browser: browser!,
        sessionId,
        timeout,
        visible: false,
        logger: this.logger,
      });
      const escaped = res.data.replace(/\"/gi, '\\"') || '';

      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(${setInputValue.toString()})("${recaptcha.input}", "${escaped}")`,
        logger: this.logger,
      });

      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(${callRecaptchaCallback})("${recaptcha.callback}", "${escaped}")`,
        logger: this.logger,
      });

      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;
      this.logger.error('Error solving recaptcha:', error);
      return response;
    }
  }

  private async solveRecaptchaV3({
    timeout,
    sessionId,
    browser = this.browser,
  }: {
    timeout: number;
    sessionId: string;
    browser?: WebSocket;
  }) {
    this.logger.debug(`Attempting to solve recaptcha v3`);
    const response = this.getResponseObject();
    const start = Date.now();
    const poll = 200;

    try {
      const sitekey = await asyncPoll(
        async () => {
          const requests = await this.getAllBrowserRequests(
            browser!,
            sessionId,
          );
          return requests.reduce((result: null | string, uri) => {
            if (result) return result;
            if (!isMatch(uri, recaptcha.url)) {
              return null;
            }
            const url = new URL(uri);
            const siteKey = url.searchParams.get(recaptcha.param);
            return siteKey;
          }, null);
        },
        poll,
        timeout,
      ).catch((error) => {
        this.logger.error('Error solving recaptcha-v3:', error);
        return null;
      });

      if (!sitekey) {
        this.logger.debug(
          `No g-recaptcha sitekey found for session ${sessionId} on ${sitekey}`,
        );
        response.time = Date.now() - start;
        return response;
      }

      response.found = true;

      const evaluateResponse = await evalInBrowser<URLAndUA>({
        browser: browser!,
        sessionId,
        expression: `(() => ({ website: location.href, userAgent: navigator.userAgent }))();`,
        logger: this.logger,
      });
      const {
        result: { website, userAgent },
      } = evaluateResponse;

      this.logger.debug(
        `Found g-recaptcha-v3 sitekey of "${sitekey}" and url of "${website}", solving..."`,
      );

      const res = await captchas.solveRecaptchaV3(website, sitekey, userAgent);

      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(() => {
          window.verifyRecaptcha("${res.data}");
        })()`,
        logger: this.logger,
      });

      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;
      this.logger.error('Error solving recaptcha-v3:', error);
      return response;
    }
  }

  private async solveCloudflare({
    timeout,
    sessionId,
    browser = this.browser,
  }: {
    timeout: number;
    sessionId: string;
    browser?: WebSocket;
  }) {
    const poll = 200;
    const start = Date.now();
    this.logger.debug(`Attempting to find and solve Cloudflare`);

    const boundingBox = await asyncPoll(
      async () => {
        const rect = await getDeepClientRect({
          browser: browser!,
          selector: '< *challenges.cloudflare.com* label.cb-lb',
          sessionId,
          port: this.config.getPort(),
        });
        return rect;
      },
      poll,
      timeout,
    );

    if (!boundingBox) {
      this.logger.error(`No Cloudflare challenge found`);

      return {
        found: false,
        solved: false,
        time: Date.now() - start,
      };
    }

    const x = boundingBox.x;
    const y = boundingBox.y;

    await sleep(getRandomArbitrary(500, 800));
    const runCDPCommand = this.runCommandFactory(browser!, sessionId);

    const vectors = path({ x: 0, y: 0 }, { x, y });

    for (const v of vectors) {
      await runCDPCommand('Input.dispatchMouseEvent', {
        type: 'mouseMoved',
        x: v.x,
        y: v.y,
      });
    }

    await sleep(getRandomArbitrary(100, 250));

    await runCDPCommand('Input.dispatchMouseEvent', {
      type: 'mousePressed',
      button: 'left',
      clickCount: 1,
      force: 0.125,
      x,
      y,
    });
    await sleep(getRandomArbitrary(5, 25));

    await runCDPCommand('Input.dispatchMouseEvent', {
      type: 'mouseReleased',
      button: 'left',
      clickCount: 1,
      force: 0.125,
      x,
      y,
    });

    return {
      found: true,
      solved: true,
      time: Date.now() - start,
    };
  }

  private async solveFriendlyCaptcha({
    timeout,
    sessionId,
    browser = this.browser,
  }: {
    timeout: number;
    sessionId: string;
    browser?: WebSocket;
  }) {
    const response = this.getResponseObject();
    const start = Date.now();
    const poll = 200;
    this.logger.debug('Attempting to solve Friendly Captcha');

    try {
      const sitekey = await asyncPoll(
        async () => {
          this.logger.debug('Waiting for Friendly Captcha key selector');
          await waitForSelector({
            selector: friendlyCaptcha.keySelector,
            browser: browser!,
            sessionId,
            logger: this.logger,
            timeout: timeout,
            visible: false,
          });
          this.logger.debug('Found Friendly Captcha key selector');
          const res = await evalInBrowser<string>({
            browser: browser!,
            sessionId,
            expression: `(() => document.querySelector('${friendlyCaptcha.keySelector}').getAttribute('${friendlyCaptcha.param}'))()`,
            logger: this.logger,
          });
          return res.result;
        },
        poll,
        timeout,
      ).catch((error) => {
        this.logger.error('Error finding Friendly Captcha sitekey:', error);
        return null;
      });

      if (!sitekey) {
        this.logger.info(
          `No Friendly Captcha sitekey found for session ${sessionId}`,
        );
        response.time = Date.now() - start;
        return response;
      }

      response.found = true;

      const evaluateResponse = await evalInBrowser<URLAndUA>({
        browser: browser!,
        sessionId,
        expression: `(${getURLAndUA.toString()})()`,
        logger: this.logger,
      });
      const {
        result: { website },
      } = evaluateResponse;

      this.logger.debug(
        `Found Friendly Captcha sitekey of "${sitekey}" and url of "${website}", solving...`,
      );

      const res = await captchas.solveFriendlyCaptcha(website, sitekey);

      // Check for custom solution field name
      const customFieldName = await evalInBrowser<string>({
        browser: browser!,
        sessionId,
        expression: `(() => {
           const element = document.querySelector('${friendlyCaptcha.keySelector}');
           return element ? element.getAttribute('data-solution-field-name') : null;
         })()`,
        logger: this.logger,
      });

      const fieldName = customFieldName.result || 'frc-captcha-solution';

      // Set the token in the solution field
      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(() => (${setInputValue.toString()})('input[name="${fieldName}"]', "${res.data}"))()`,
        logger: this.logger,
      });

      // Check for callback function and call it if present
      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(() => {
           const element = document.querySelector('${friendlyCaptcha.keySelector}');
           const callback = element ? element.getAttribute('data-callback') : null;
           if (callback && typeof window[callback] === 'function') {
             window[callback]('${res.data}');
             return true;
           }
           return false;
         })()`,
        logger: this.logger,
      });

      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;
      this.logger.error('Error solving Friendly Captcha:', error);
      return response;
    }
  }

  private async solveNormalCaptcha({
    timeout,
    sessionId,
    browser = this.browser,
  }: {
    timeout: number;
    sessionId: string;
    browser?: WebSocket;
  }) {
    const response = this.getResponseObject();
    const start = Date.now();
    const poll = 200;
    try {
      const imageData = await asyncPoll(
        async () => {
          const result = await evalInBrowser<string>({
            browser: browser!,
            sessionId,
            expression: `(${getCaptchaImg.toString()})()`,
            logger: this.logger,
          });
          return result.result ? result : null;
        },
        poll,
        timeout,
      ).catch((error) => {
        this.logger.error('Error solving normal captcha:', error);
        return null;
      });

      if (!imageData || !imageData.result) {
        response.time = Date.now() - start;
        return response;
      }

      response.found = true;

      const res = await captchas.solveNormalCaptcha(imageData.result);

      // Find input field and insert solution
      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(${setCaptchaValue.toString()})("${res.data}")`,
        logger: this.logger,
      });

      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;
      this.logger.error('Error solving normal captcha:', error);
      return response;
    }
  }

  private async solveGeeTest({
    timeout,
    sessionId,
    browser = this.browser,
  }: {
    timeout: number;
    sessionId: string;
    browser?: WebSocket;
  }) {
    this.logger.debug('Attempting to solve GeeTest');
    const response = this.getResponseObject();
    const start = Date.now();
    const poll = 200;
    try {
      const sitekey = await asyncPoll(
        async () => {
          const requests = await this.getAllBrowserRequests(
            browser!,
            sessionId,
          );

          const data = requests.reduce(
            (
              result: null | { siteKey: string; challenge: string },
              request,
            ) => {
              if (result) return result;
              if (!isMatch(request, geetest.url)) {
                return null;
              }

              const url = new URL(request);
              const params = url.hash
                ? new URLSearchParams(url.hash)
                : url.searchParams;

              const siteKey = params.get(geetest.gtParam);
              const challenge = params.get(geetest.challengeParam);

              if (siteKey && challenge) {
                return { siteKey, challenge };
              }
              return null;
            },
            null as null | { siteKey: string; challenge: string },
          );

          return data;
        },
        poll,
        timeout,
      ).catch((error) => {
        this.logger.error('Error solving GeeTest:', error);
        return null;
      });

      this.logger.debug('GeeTest sitekey:', sitekey);

      if (!sitekey) {
        this.logger.info(`No GeeTest sitekey found for session ${sessionId}`);
        response.time = Date.now() - start;
        return response;
      }

      response.found = true;

      const evaluateResponse = await evalInBrowser<URLAndUA>({
        browser: browser!,
        sessionId,
        expression: `(${getURLAndUA.toString()})()`,
        logger: this.logger,
      });

      const res = await captchas.solveGeeTest(
        evaluateResponse.result.website,
        sitekey.siteKey,
        sitekey.challenge,
      );

      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(${setGeetestResponse.toString()})(${res.geetest_challenge}, ${res.geetest_validate}, ${res.geetest_seccode})`,
        logger: this.logger,
      });

      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.geetest_validate;
      ++this.req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;
      this.logger.error('Error solving GeeTest:', error);
      return response;
    }
  }

  private async solveTextCaptcha({
    timeout,
    sessionId,
    browser = this.browser,
  }: {
    timeout: number;
    sessionId: string;
    browser?: WebSocket;
  }) {
    this.logger.debug('Attempting to solve text captcha');
    const response = this.getResponseObject();
    const start = Date.now();
    const poll = 200;
    try {
      const questionData = await asyncPoll(
        async () => {
          const result = await evalInBrowser<string>({
            browser: browser!,
            sessionId,
            expression: `(${getTextCaptchaQuestion.toString()})()`,
            logger: this.logger,
          });
          return result.result ? result : null;
        },
        poll,
        timeout,
      ).catch((error) => {
        this.logger.error('Error finding text captcha question:', error);
        return null;
      });

      if (!questionData || !questionData.result) {
        this.logger.info(
          `No text captcha question found for session ${sessionId}`,
        );
        response.time = Date.now() - start;
        return response;
      }

      response.found = true;

      this.logger.info(
        `Found text captcha question: "${questionData.result}", solving...`,
      );

      const res = await captchas.solveTextCaptcha(questionData.result);

      // Set the answer in the appropriate input field
      await evalInBrowser({
        browser: browser!,
        sessionId,
        expression: `(${setTextCaptchaAnswer.toString()})("${res.data}")`,
        logger: this.logger,
      });

      response.time = Date.now() - start;
      response.solved = true;
      response.token = res.data;
      ++this.req.__bless__.captchasSolved;

      return response;
    } catch (error: any) {
      response.time = Date.now() - start;
      this.logger.error('Error solving text captcha:', error);
      return response;
    }
  }

  private async solveAmazonWAF({
    timeout,
    sessionId,
    browser = this.browser,
  }: {
    timeout: number;
    sessionId: string;
    browser?: WebSocket;
  }) {
    this.logger.debug('Attempting to solve AmazonWAF');
    const response = this.getResponseObject();
    const start = Date.now();
    const poll = 200;

    const evaluateResponse = await evalInBrowser<URLAndUA>({
      browser: browser!,
      sessionId,
      expression: `(${getURLAndUA.toString()})()`,
      logger: this.logger,
    });

    const data = await asyncPoll(
      async () => {
        const res = await evalInBrowser<{
          captchaScript: string;
          challengeScript: string;
          context: string;
          iv: string;
          key: string;
        }>({
          browser: browser!,
          sessionId,
          expression: `(${getWAFData.toString()})()`,
          logger: this.logger,
        });

        return res.result;
      },
      poll,
      timeout,
    ).catch((error) => {
      this.logger.error('Error solving AmazonWAF:', error);
      return null;
    });

    if (!data) {
      this.logger.info(`No AmazonWAF data found`);
      response.time = Date.now() - start;
      return response;
    }

    response.found = true;

    const res = await captchas.solveAmazonWAF({
      website: evaluateResponse.result.website,
      captchaScript: data.captchaScript,
      challengeScript: data.challengeScript,
      context: data.context,
      iv: data.iv,
      key: data.key,
    });

    const injectResponse = async (token: string) => {
      // @ts-ignore
      await window.ChallengeScript.submitCaptcha(token);
    };

    await evalInBrowser({
      browser: browser!,
      sessionId,
      expression: `(${injectResponse.toString()})("${res.data.captcha_voucher}")`,
      logger: this.logger,
    });

    response.token = res.data.captcha_voucher;
    response.solved = true;
    response.time = Date.now() - start;
    ++this.req.__bless__.captchasSolved;

    return response;
  }

  private async getAllBrowserRequests(browser: WebSocket, sessionId: string) {
    const requests = [...this.requests]
      .map(([, { url }]) => url)
      .filter((u) => u.startsWith('http'));

    const [iframeURLs, cdpTargetURLs] = await Promise.all([
      evalInBrowser({
        browser,
        sessionId,
        expression: `(${findIframeURLs.toString()})()`,
        logger: this.logger,
      }).then((r) => r.result as string[]),
      sendCDPMessage(
        browser,
        'Target.getTargets',
        getRandomNegativeInt(),
        {},
        sessionId,
      ).then(({ targetInfos }) => {
        return targetInfos.filter((t) => t.type === 'iframe').map((t) => t.url);
      }),
    ]);

    return [...iframeURLs, ...cdpTargetURLs, ...requests];
  }

  private getResponseObject(): {
    token: null | string;
    found: boolean;
    solved: boolean;
    time: number;
  } {
    return {
      token: null,
      found: false,
      solved: false,
      time: 0,
    };
  }

  private runCommandFactory =
    (browser: WebSocket, sessionId: string) =>
    async (command: keyof ProtocolMapping.Commands, params: any) => {
      return sendCDPMessage(
        browser,
        command,
        getRandomNegativeInt(),
        params,
        sessionId,
      );
    };
}

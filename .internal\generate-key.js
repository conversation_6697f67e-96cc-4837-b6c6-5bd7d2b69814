#!/usr/bin/env node

/**
 * <PERSON>ript to generate cryptographically secure time-limited software keys
 * for browserless/enterprise. DO NOT ADD TO THE DOCKERFILE.
 *
 * Usage: node scripts/generate-key.js [days]
 *
 * [days] - Number of days the key should be valid (default: 30)
 */

import crypto from 'crypto';

const SECRET_KEY = 'a0389f7b-5607-4521-a682-623b243bea39';
const SALT = 'browserless-io-enterprise-key';

const ALGORITHM = 'aes-256-cbc';
const ITERATIONS = 100000;
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const DIGEST = 'sha256';

const args = process.argv.slice(2);
const days = parseInt(args[0], 10) || 30;

const now = Date.now();
const expiresAt = now + days * 24 * 60 * 60 * 1000; // Convert days to milliseconds

const key = {
  expiresAt,
  created: now,
  id: crypto.randomBytes(16).toString('hex'),
};

const jsonString = JSON.stringify(key);

/**
 * Encrypts data using AES-256-CBC encryption
 * @param {string} data - Data to encrypt
 * @returns {string} - Hex encoded encrypted string
 */
const encrypt = (data) => {
  try {
    const iv = crypto.randomBytes(IV_LENGTH);

    const key = crypto.pbkdf2Sync(
      SECRET_KEY,
      SALT,
      ITERATIONS,
      KEY_LENGTH,
      DIGEST
    );

    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const ivHex = iv.toString('hex');

    return ivHex + encrypted;
  } catch (error) {
    console.error(`Error encrypting data: ${error}`);
    throw error;
  }
};

const encodedKey = encrypt(jsonString);

console.log('\nBrowserless Enterprise Software Key Generator');
console.log('=============================================');
console.log(`\nGenerated key valid for ${days} days`);
console.log(`Expires on: ${new Date(expiresAt).toISOString()}`);
console.log('\nAdd this to your environment variables:');
console.log(`KEY=${encodedKey}`);

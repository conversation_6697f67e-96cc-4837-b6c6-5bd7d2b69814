import {
  APITags,
  WebSocketRoute,
  dedent,
  NotFound,
  Logger,
  getTokenFromRequest,
  TooManyRequests,
  CDPLaunchOptions,
} from '@browserless.io/browserless';
import { Duplex } from 'stream';
import { AugmentedRequest, BrowserlessEnterpriseRoutes } from '../../types.js';
import { EnterpriseRoutes } from '../../paths.js';
import { FileDatabase } from '../../file-system.js';
import EnterpriseBrowserManager from '../../browser-manager.js';
import { getURLLastSegment } from '../../utils.js';

export interface QuerySchema {
  token?: string;
  timeout?: number;
  launch?: CDPLaunchOptions | string;
}

export default class SessionWebsocketRoute extends WebSocketRoute {
  name = BrowserlessEnterpriseRoutes.EnterpriseSessionWebsocketRoute;
  auth = true;
  concurrency = true;
  description = dedent(
    `Connect to an existing session with a library like puppeteer or playwright that work over chrome-devtools-protocol. See documentation for more details on how to start the session.

    The browser type (Chrome/Chromium, stealth/regular) is determined from the session metadata when it was created initially.`,
  );
  path = [EnterpriseRoutes.sessionConnect];
  tags = [APITags.browserWS];

  handler = async (
    req: AugmentedRequest,
    socket: Duplex,
    head: Buffer,
    logger: Logger,
  ): Promise<void> => {
    const sessionId = getURLLastSegment(req.parsed)!;
    const fileDB = this.fileSystem() as FileDatabase;
    const browserManager = this.browserManager() as EnterpriseBrowserManager;

    // Get and validate API key
    const apiKey = getTokenFromRequest(req);

    const session = await fileDB.getSession(sessionId);

    if (!session) {
      logger.debug('Session not found:', sessionId);
      throw new NotFound(`Session ID "${sessionId}" wasn't found.`);
    }

    const isOwner = session.createdBy === apiKey;

    if (!isOwner) {
      logger.debug('Unauthorized session access attempt');
      throw new NotFound(`Session ID "${sessionId}" wasn't found.`);
    }

    if (session.running) {
      logger.debug('Session is already executing:', sessionId);
      throw new TooManyRequests(
        `Session is already being accessed by another client.`,
      );
    }

    const browser = await browserManager.getBrowserForSession(req, session);
    await fileDB.setSessionExecuting(sessionId, true);

    return browser.proxyWebSocket(req, socket, head).finally(() => {
      fileDB.setSessionExecuting(sessionId, false);
      browserManager.complete(browser);
    });
  };
}

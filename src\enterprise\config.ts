import { SharedConfig } from '../config.js';

export class EnterpriseConfig extends SharedConfig {
  public allowLiveURLs() {
    return true;
  }

  public allowCaptchaSolving() {
    return true;
  }

  public allowReconnect() {
    return true;
  }

  public allowCityProxying() {
    return true;
  }

  public allowStateProxying() {
    return true;
  }

  public allowRecord() {
    return true;
  }

  public allowThirdPartyProxy() {
    return true;
  }

  public getMaxReconnectTime() {
    return Infinity;
  }

  public isFreePlan(_apiKey: string | null) {
    return false;
  }

  public disableBlocklist() {
    return process.env.DISABLE_BLOCKLIST === 'true';
  }

  public getMaxSessionTTL() {
    return 90 * 24 * 60 * 60 * 1000; // 90 days in milliseconds
  }
}

export default new EnterpriseConfig();

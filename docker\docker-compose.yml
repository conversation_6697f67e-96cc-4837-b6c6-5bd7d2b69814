version: '3.8'

services:
  browserless-enterprise:
    build:
      context: .
      dockerfile: ./enterprise.dockerfile
    ports:
      - "5555:5555"
    env_file:
      - config.env
    environment:
      - KEY=<ENTERPRISE_KEY>
      - DISPLAY=99
      - API_URL=https://api.browserless.io
      - API_TOKEN=<YOUR_API_TOKEN>
      - PROXY_USERNAME=<PROXY_USERNAME>
      - PROXY_PASSWORD=<PROXY_PASSWORD>
    volumes:
      # Map downloads directory to host for easy access

      - ./downloads:/tmp/downloads
      # Map logs if needed
      - ./logs:/app/logs
    restart: unless-stopped
    # Add health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

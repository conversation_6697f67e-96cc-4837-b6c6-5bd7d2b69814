import { SharedConfig } from '../../../config.js';
import { AugmentedRequest } from '../../../types.js';
import { ProxyProvider } from './ProxyVendor.js';
import { IPRoyal } from './vendors/IPRoyal.js';
import { Massive } from './vendors/Massive.js';
import { LocalProxy } from './vendors/LocalProxy.js';
export class ProxyingEngine {
  private readonly vendors: {
    iproyal: IPRoyal;
    massive: Massive;
    localproxy: LocalProxy;
  };

  constructor(private config: SharedConfig) {
    this.vendors = {
      iproyal: new IPRoyal(this.config),
      massive: new Massive(this.config),
      localproxy: new LocalProxy(this.config),
    };
  }

  public chooseVendor(ctx: AugmentedRequest['__bless__']): ProxyProvider {
    if (ctx.requiresCaptchaSolving && this.vendors.massive.isHealthy) {
      return this.vendors.massive;
    }

    // Try iproyal if it's healthy
    if (this.vendors.iproyal.isHealthy) {
      return this.vendors.iproyal;
    }

    // If not, try massive
    if (this.vendors.massive.isHealthy) {
      return this.vendors.massive;
    }

    // Always return to localproxy if no healthy vendors
    return this.vendors.localproxy;
  }
}

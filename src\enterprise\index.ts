import { createLogger } from '@browserless.io/browserless';
import BrowserlessEnterprise from './browserless.js';
import { decodeSoftwareKey, isSoftwareKeyValid } from '../key-utils.js';

(async () => {
  const log = createLogger('enterprise-index');

  const softwareKey = process.env.KEY;

  if (!softwareKey) {
    log(`ERROR: KEY parameter is required and not passed in, exiting`);
    process.exit(2); // Exit with semantic code 2 for key expiration
  }

  log(`Software key found, validating...`);
  if (!isSoftwareKeyValid(softwareKey)) {
    const expiresAt = decodeSoftwareKey(softwareKey);
    const expiredDate = expiresAt
      ? new Date(expiresAt).toISOString()
      : 'unknown';
    log(`ERROR: Software key has expired on ${expiredDate}`);
    process.exit(2); // Exit with semantic code 2 for key expiration
  }

  const expiresAt = decodeSoftwareKey(softwareKey);
  if (expiresAt) {
    const expirationDate = new Date(expiresAt).toISOString();
    log(`Software key is valid until ${expirationDate}`);
  }

  const browserless = new BrowserlessEnterprise();
  await browserless.start();

  log(`Binding signal interruption handlers and uncaught errors`);
  process
    .on('unhandledRejection', async (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    })
    .once('uncaughtException', async (err, origin) => {
      console.error('Unhandled exception at:', origin, 'error:', err);
      await browserless.stop();
      process.exit(1);
    })
    .once('SIGTERM', async () => {
      log(`SIGTERM received, saving and closing down`);
      await browserless.stop();
      process.exit(0);
    })
    .once('SIGINT', async () => {
      log(`SIGINT received, saving and closing down`);
      await browserless.stop();
      process.exit(0);
    })
    .once('SIGHUP', async () => {
      log(`SIGHUP received, saving and closing down`);
      await browserless.stop();
      process.exit(0);
    })
    .once('SIGUSR2', async () => {
      log(`SIGUSR2 received, saving and closing down`);
      await browserless.stop();
      process.exit(0);
    })
    .once('exit', () => {
      log(`Process is finished, exiting`);
      process.exit(0);
    });
})();

export const getContent = (selector?: string) => {
  let content = '';
  const element = (() => {
    if (selector) {
      let node = null;
      try {
        node = document.querySelector(selector);
      } catch {
        try {
          node = eval(selector);
        } catch {
          node = null;
        }
      }
      if (!node || !(node instanceof Element)) {
        return null;
      }
      return node;
    }
    return document;
  })();

  if (!element) {
    throw new Error(`Couldn't locate selector "${selector}" on the page.`);
  }

  for (const node of element.childNodes) {
    switch (node) {
      case document.documentElement:
        content += document.documentElement.outerHTML;
        break;
      default:
        content += new XMLSerializer().serializeToString(node);
        break;
    }
  }

  return content;
};

export const removeAttributes = (attributes?: string[]) => {
  [...document.querySelectorAll('*')].forEach((n) => {
    if (attributes) {
      attributes.forEach((a) => n.removeAttribute(a));
    } else {
      while (n.attributes.length > 0) n.removeAttribute(n.attributes[0].name);
    }
  });
};

export const removeSelectors = (selectors: string[]) => {
  return selectors
    .flatMap((s) => {
      let n = null;
      try {
        n = document.querySelectorAll(s);
      } catch {
        try {
          n = eval(s);
        } catch {
          n = null;
        }
      }
      if (!n || !(n instanceof NodeList)) {
        return null;
      }
      return [...n];
    })
    .filter((_) => !!_)
    .forEach((n) => (n as Element).remove());
};

export const getNodeText = (selector: string) => {
  let node = null;
  try {
    node = document.querySelector(selector);
  } catch {
    try {
      node = eval(selector);
    } catch {
      node = null;
    }
  }

  if (!node || !(node instanceof Element)) {
    return null;
  }

  // @ts-ignore
  return node.innerText;
};

export const setContent = (html: string) => {
  document.open();
  document.write(html);
  document.close();
};

export const findIframeURLs = () => {
  return [...document.querySelectorAll('iframe')]
    .map((f) => f.src)
    .filter((_) => !!_);
};

export const setHcaptchaResponse = (
  textareaSelector: string,
  iframeSelector: string,
  value: string,
) => {
  const textarea = document.querySelector(textareaSelector) as
    | HTMLInputElement
    | HTMLTextAreaElement;

  const iframe = document.querySelector(iframeSelector) as HTMLIFrameElement;
  const callbacks = [
    ...document.querySelectorAll(
      'div[data-sitekey][data-callback]:has(iframe[src*="hcaptcha.com"])',
    ),
  ];
  const win = window as unknown as any;

  if (textarea) {
    textarea.value = value;
  }

  if (iframe) {
    iframe.setAttribute('data-hcaptcha-response', value);
  }

  if (callbacks) {
    callbacks.forEach((el) => {
      const callback = el.getAttribute('data-callback');
      if (callback && win[callback]) {
        win[callback](value);
      }
    });
  }

  if (win.hcaptcha && win.hcaptcha.close) {
    win.hcaptcha.close();
  }

  return {
    textarea: !!textarea,
    iframe: !!iframe,
  };
};

export const setInputValue = (selector: string, value: string) => {
  const node = document.querySelector(selector) as
    | HTMLInputElement
    | HTMLTextAreaElement;

  if (node) {
    node.value = value;
  }
};

export const callRecaptchaCallback = (selector: string, solution: string) => {
  const el = document.querySelector(selector);

  if (el) {
    const callback = el.getAttribute('data-callback');

    if (callback && (window as unknown as any)[callback]) {
      (window as unknown as any)[callback](solution);
    }
  }
};

export const setFriendlyCaptchaResponse = (
  keySelector: string,
  solution: string,
) => {
  // Check for custom solution field name
  const element = document.querySelector(keySelector);
  const customFieldName = element
    ? element.getAttribute('data-solution-field-name')
    : null;

  const fieldName = customFieldName || 'frc-captcha-solution';
  const input = document.querySelector(
    `input[name="${fieldName}"]`,
  ) as HTMLInputElement;

  if (input) {
    input.value = solution;
  }

  return {
    input: !!input,
    fieldName,
  };
};

export const callFriendlyCaptchaCallback = (
  keySelector: string,
  solution: string,
) => {
  const element = document.querySelector(keySelector);
  const callback = element ? element.getAttribute('data-callback') : null;

  if (callback && typeof (window as any)[callback] === 'function') {
    (window as any)[callback](solution);
    return true;
  }

  return false;
};

export const getBoundingRect = (selector: string, visible: boolean) => {
  let node = null;
  try {
    node = document.querySelector(selector);
  } catch {
    try {
      node = eval(selector);
    } catch {
      node = null;
    }
  }

  if (!node || !(node instanceof Element)) {
    return null;
  }
  const rect = node.getBoundingClientRect();

  if (visible) {
    const isVisible = node.checkVisibility({
      checkOpacity: true,
      checkVisibilityCSS: true,
    });

    if (!isVisible) {
      return null;
    }
  }

  return JSON.stringify(rect);
};

export const getQuerySelectorAll = (selector: string) => {
  let nodes = null;
  try {
    nodes = document.querySelectorAll(selector);
  } catch {
    try {
      nodes = eval(selector);
    } catch {
      nodes = null;
    }
  }

  if (!nodes || !nodes.length) {
    return null;
  }

  return JSON.stringify(
    // NodeList's are not iterable
    [...nodes].map((el: HTMLElement) => ({
      id: el.id,
      childElementCount: el.childElementCount,
      className: el.className,
      innerHTML: el.innerHTML,
      // @ts-ignore
      innerText: el.innerText,
      localName: el.localName,
      outerHTML: el.outerHTML,
    })),
  );
};

export const getQuerySelector = (selector: string) => {
  let node = null;
  try {
    node = document.querySelector(selector);
  } catch {
    try {
      node = eval(selector);
    } catch {
      node = null;
    }
  }

  if (!node || !(node instanceof Element)) {
    return null;
  }

  return JSON.stringify({
    id: node.id,
    childElementCount: node.childElementCount,
    className: node.className,
    innerHTML: node.innerHTML,
    // @ts-ignore
    innerText: node.innerText,
    localName: node.localName,
    outerHTML: node.outerHTML,
  });
};

export const mapSelector = ({
  selector,
  parent,
  parentIdx,
}: {
  selector: string;
  parent?: string;
  parentIdx?: number;
}) => {
  let root: any = document;
  if (parent !== undefined && parentIdx !== undefined) {
    try {
      root = document.querySelectorAll(parent)[parentIdx];
    } catch {
      try {
        root = eval(parent)[parentIdx];
      } catch {
        root = document;
      }
    }
  }
  let nodes = null;
  try {
    nodes = root.querySelectorAll(selector);
  } catch {
    try {
      nodes = eval(selector);
    } catch {
      nodes = null;
    }
  }

  if (!nodes || !(nodes instanceof NodeList)) {
    return null;
  }

  return [...nodes].map((node: any) => ({
    innerHTML: node.innerHTML,
    innerText: (node as unknown as any).innerText,
    id: node.id,
    class: [...node.classList],
    attributes: [...node.attributes].reduce(
      (accum, { name }) => {
        if (name) {
          accum[name] = node.getAttribute(name);
        }
        return accum;
      },
      {} as { [key: string]: string | null },
    ),
  }));
};

export const selectFn = (selector: string, ...vals: string[]): string[] => {
  let node = null;
  try {
    node = document.querySelector(selector);
  } catch {
    try {
      node = eval(selector);
    } catch {
      node = null;
    }
  }

  if (!node || !(node instanceof Element)) {
    return [];
  }

  const values = new Set(vals);
  if (!(node instanceof HTMLSelectElement)) {
    throw new Error('Element is not a <select> element.');
  }

  const selectedValues = new Set<string>();
  if (!node.multiple) {
    for (const option of node.options) {
      option.selected = false;
    }
    for (const option of node.options) {
      if (values.has(option.value)) {
        option.selected = true;
        selectedValues.add(option.value);
        break;
      }
    }
  } else {
    for (const option of node.options) {
      option.selected = values.has(option.value);
      if (option.selected) {
        selectedValues.add(option.value);
      }
    }
  }
  node.dispatchEvent(new Event('input', { bubbles: true }));
  node.dispatchEvent(new Event('change', { bubbles: true }));
  return [...selectedValues.values()];
};

export const selectorIsInteractable = (selector: string) => {
  let node = null;
  try {
    node = document.querySelector(selector);
  } catch {
    try {
      node = eval(selector);
    } catch {
      node = null;
    }
  }

  if (!node || !(node instanceof Element)) {
    return null;
  }
  const rect = node.getBoundingClientRect();
  const x = rect.x + rect.width / 2;
  const y = rect.y + rect.height / 2;
  const atPos = document.elementFromPoint(x, y);

  return node === atPos;
};

export const selectorIsChecked = (selector: string) => {
  let node = null;
  try {
    node = document.querySelector(selector);
  } catch {
    try {
      node = eval(selector);
    } catch {
      node = null;
    }
  }

  if (!node || !(node instanceof Element)) {
    return `Unable to find node: "${selector}"`;
  }

  if (
    !(
      node?.tagName === 'INPUT' &&
      ['checkbox', 'radio'].includes((node as HTMLInputElement).type)
    )
  ) {
    return `"${selector}" must be a checkbox or a radio button`;
  }
  return (node as HTMLInputElement).checked;
};

export const scrollIntoView = (
  selector: string,
  smooth: boolean,
): Promise<void> =>
  new Promise((resolve, reject) => {
    let node = null;
    try {
      node = document.querySelector(selector);
    } catch {
      try {
        node = eval(selector);
      } catch {
        node = null;
      }
    }

    if (!node || !(node instanceof Element)) {
      return reject(new Error(`Unable to locate node: "${selector}"`));
    }

    const eventListenerCB = () => {
      clearTimeout(timer);
      timer = setTimeout(timerCB, 50);
    };

    const timerCB = () => {
      document.removeEventListener('scroll', eventListenerCB);
      resolve();
    };

    let timer = setTimeout(timerCB, 50);

    document.addEventListener('scroll', eventListenerCB);

    const options = smooth
      ? {
          behavior: 'smooth',
          block: 'center',
          inline: 'center',
        }
      : {
          block: 'center',
          inline: 'center',
        };

    node.scrollIntoView(options as any);

    if (!smooth) {
      return resolve();
    }
  });

export const getCaptchaImg = () => {
  return new Promise((resolve) => {
    const selectors = [
      'img[class*="captcha"]',
      'img[alt*="captcha"]',
      'img[aria-label*="captcha"]',
      'img[src*="captcha"]',
    ];

    for (const selector of selectors) {
      const img = document.querySelector(selector) as HTMLImageElement;

      if (img && img.src) {
        // Ensure image is loaded
        if (img.complete && img.naturalHeight > 0) {
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) continue;
            canvas.width = img.naturalWidth || img.width;
            canvas.height = img.naturalHeight || img.height;
            ctx.drawImage(img, 0, 0);
            const base64 = canvas.toDataURL('image/png').split(',')[1];
            return resolve(base64);
          } catch (error) {
            continue;
          }
        } else {
          // Wait for image to load
          img.onload = () => {
            try {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              if (!ctx) return resolve(null);
              canvas.width = img.naturalWidth || img.width;
              canvas.height = img.naturalHeight || img.height;
              ctx.drawImage(img, 0, 0);
              return resolve(canvas.toDataURL('image/png').split(',')[1]);
            } catch (error) {
              return resolve(null);
            }
          };
          img.onerror = () => resolve(null);
          return;
        }
      }
    }
    resolve(null);
  });
};

export const setCaptchaValue = (value: string) => {
  const selectors = [
    'input[name*="captcha"]',
    'input[id*="captcha"]',
    '.captcha input[type="text"]',
    '#captcha input[type="text"]',
    'input[name*="challenge"]',
    'input[id*="challenge"]',
    'input[name*="verification"]',
    'input[id*="verification"]',
    // 2captcha.com specific patterns
    'input[name="simpleCaptcha"]',
    'input[id*="simple-captcha"]',
  ];

  for (const selector of selectors) {
    const input = document.querySelector(selector) as HTMLInputElement;
    if (input) {
      input.value = value;
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }
  return false;
};

/**
 * Extract text captcha question from the page
 * Returns the question text that should be sent to the text captcha solving service
 */
export const getTextCaptchaQuestion = () => {
  const questionSelectors = [
    '.captcha-question',
    '[class*="captcha-question"]',
    '[id*="captcha-question"]',
    '.text-captcha-question',
    '[class*="text-captcha"]',
    '[aria-label*="captcha question"]',
    '[data-captcha-question]',
    'label[for*="captcha"]',
    '.math-captcha',
    '[class*="math-captcha"]',
  ];

  // First try to find dedicated question elements
  for (const selector of questionSelectors) {
    const questionElement = document.querySelector(selector);
    if (questionElement && questionElement.textContent) {
      const questionText = questionElement.textContent.trim();
      // Validate it looks like a captcha question
      const textCaptchaPatterns = [
        'what',
        'solve',
        'calculate',
        'answer',
        'math',
        'arithmetic',
        'add',
        'subtract',
        'multiply',
        'divide',
        '+',
        '-',
        '*',
        '/',
        '=',
        'equals',
        'sum',
        'difference',
        'product',
        'quotient',
      ];

      const hasPattern = textCaptchaPatterns.some((pattern) =>
        questionText.toLowerCase().includes(pattern),
      );

      if (hasPattern && questionText.length > 3 && questionText.length < 200) {
        return questionText;
      }
    }
  }

  // Alternative approach: scan all text elements for question patterns
  const allTextElements = Array.from(document.querySelectorAll('*')).filter(
    (el) =>
      el.children.length === 0 &&
      el.textContent &&
      el.textContent.trim().length > 3 &&
      el.textContent.trim().length < 200,
  );

  for (const element of allTextElements) {
    const text = element.textContent?.trim() || '';

    // Look for mathematical expressions or questions
    const mathPatterns = [
      /what\s+is\s+\d+\s*[\+\-\*\/]\s*\d+/i,
      /solve\s*:?\s*\d+\s*[\+\-\*\/]\s*\d+/i,
      /calculate\s*:?\s*\d+\s*[\+\-\*\/]\s*\d+/i,
      /\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\?/,
      /answer\s*:?\s*\d+\s*[\+\-\*\/]\s*\d+/i,
      /\d+\s*[\+\-\*\/]\s*\d+/,
    ];

    const hasMatchingPattern = mathPatterns.some((pattern) =>
      pattern.test(text),
    );

    if (hasMatchingPattern) {
      // Check if there's a nearby input field to confirm this is a captcha
      const parent = element.closest(
        'form, div, section, .captcha, .text-captcha',
      );
      if (parent) {
        const nearbyInput = parent.querySelector(
          'input[type="text"], input:not([type])',
        );
        if (nearbyInput) {
          return text;
        }
      }
    }
  }

  // Final fallback: look for any text near captcha input fields
  const captchaInputs = document.querySelectorAll(
    [
      'input[name*="captcha"]',
      'input[id*="captcha"]',
      'input[name*="answer"]',
      'input[id*="answer"]',
      'input[name*="question"]',
      'input[id*="question"]',
      'input[name*="challenge"]',
      'input[id*="challenge"]',
      'input[name*="verification"]',
      'input[id*="verification"]',
      'input[name*="text-captcha"]',
      'input[id*="text-captcha"]',
      'input[name*="math"]',
      'input[id*="math"]',
      '.captcha input[type="text"]',
      '.text-captcha input[type="text"]',
      '.math-captcha input[type="text"]',
    ].join(', '),
  );

  for (const input of Array.from(captchaInputs)) {
    const container = input.closest('form, div, section, label');
    if (container) {
      const containerText = container.textContent?.trim() || '';

      // Extract potential question from container text
      const lines = containerText
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line.length > 0);

      for (const line of lines) {
        if (line.length > 3 && line.length < 200) {
          const mathPatterns = [
            /what\s+is\s+\d+\s*[\+\-\*\/]\s*\d+/i,
            /solve\s*:?\s*\d+\s*[\+\-\*\/]\s*\d+/i,
            /calculate\s*:?\s*\d+\s*[\+\-\*\/]\s*\d+/i,
            /\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\?/,
            /answer\s*:?\s*\d+\s*[\+\-\*\/]\s*\d+/i,
            /\d+\s*[\+\-\*\/]\s*\d+/,
          ];

          const hasPattern = mathPatterns.some((pattern) => pattern.test(line));
          if (hasPattern) {
            return line;
          }
        }
      }
    }
  }

  return null;
};

/**
 * Set the text captcha answer in the appropriate input field
 */
export const setTextCaptchaAnswer = (answer: string) => {
  const inputSelectors = [
    'input[name*="captcha"]',
    'input[id*="captcha"]',
    'input[name*="answer"]',
    'input[id*="answer"]',
    'input[name*="question"]',
    'input[id*="question"]',
    'input[name*="challenge"]',
    'input[id*="challenge"]',
    'input[name*="verification"]',
    'input[id*="verification"]',
    'input[name*="text-captcha"]',
    'input[id*="text-captcha"]',
    'input[name*="math"]',
    'input[id*="math"]',
    '.captcha input[type="text"]',
    '.text-captcha input[type="text"]',
    '.math-captcha input[type="text"]',
  ];

  for (const selector of inputSelectors) {
    const input = document.querySelector(selector) as HTMLInputElement;
    if (input) {
      input.value = answer;
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
      input.dispatchEvent(new Event('keyup', { bubbles: true }));

      // Also trigger focus/blur cycle to ensure any validation runs
      input.focus();
      input.blur();

      return true;
    }
  }

  return false;
};

/**
 * Extract complete reCAPTCHA widget information from DOM
 * Based on the 2captcha extension approach for comprehensive widget detection
 */
export const getRecaptchaWidgetInfo = () => {
  try {
    // Check if grecaptcha configuration exists
    if (typeof window === 'undefined' || !(window as any).___grecaptcha_cfg) {
      return null;
    }

    const grecaptchaCfg = (window as any).___grecaptcha_cfg;
    if (!grecaptchaCfg.clients) {
      return null;
    }

    // Find the first available widget
    const clientIds = Object.keys(grecaptchaCfg.clients);
    if (clientIds.length === 0) {
      return null;
    }

    const widget = grecaptchaCfg.clients[clientIds[0]];
    if (!widget) {
      return null;
    }

    const info: {
      sitekey: string | null;
      version: 'v2' | 'v2_invisible' | 'v3';
      action: string | null;
      s: string | null; // data-s parameter for Google Service Captcha
      enterprise: boolean;
    } = {
      sitekey: null,
      version: 'v2',
      action: null,
      s: null,
      enterprise: false,
    };

    // Check if it's enterprise (following 2captcha extension approach)
    info.enterprise =
      (window as any).grecaptcha && (window as any).grecaptcha.enterprise
        ? true
        : false;

    // Determine if it's a badge (v3 or invisible v2)
    let isBadge = false;
    for (const k1 in widget) {
      if (typeof widget[k1] !== 'object') continue;
      for (const k2 in widget[k1]) {
        if (widget[k1][k2]?.classList?.contains('grecaptcha-badge')) {
          isBadge = true;
          break;
        }
      }
      if (isBadge) break;
    }

    // Determine version
    if (isBadge) {
      info.version = 'v3';
      // Check if it's invisible v2
      for (const k1 in widget) {
        const obj = widget[k1];
        if (typeof obj !== 'object') continue;
        for (const k2 in obj) {
          if (typeof obj[k2] === 'string' && obj[k2] === 'fullscreen') {
            info.version = 'v2_invisible';
            break;
          }
        }
        if (info.version === 'v2_invisible') break;
      }
    }

    // Extract sitekey, action, and data-s parameter
    for (const k1 in widget) {
      const obj = widget[k1];
      if (typeof obj !== 'object') continue;

      for (const k2 in obj) {
        if (obj[k2] === null || typeof obj[k2] !== 'object') continue;
        if (obj[k2].sitekey === undefined && obj[k2].action === undefined)
          continue;

        for (const k3 in obj[k2]) {
          if (k3 === 'sitekey') info.sitekey = obj[k2][k3];
          if (k3 === 'action') info.action = obj[k2][k3];
          if (k3 === 's') info.s = obj[k2][k3]; // Critical: data-s parameter
        }
      }
    }

    return info.sitekey ? info : null;
  } catch (error) {
    console.error('Error extracting reCAPTCHA widget info:', error);
    return null;
  }
};

name: Enterprise

on: [pull_request]

jobs:
  tags:
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.generate_tags.outputs.tag }}
    steps:
      - uses: actions/checkout@v4
      - name: Install and generate tags
        id: generate_tags
        run: npm i && node ./scripts/get-tags.js >> "$GITHUB_OUTPUT"

  test:
    needs: tags
    runs-on: ubuntu-latest
    env:
      SESSION_ENCRYPTION_KEY: tEgo0gUcbE7dAM6g4WaKgsMluzRsjt67iThk+KAoXDw=
    steps:
      - uses: actions/checkout@v4
      - name: Build the image
        run: docker build -f ./docker/enterprise.dockerfile -t ghcr.io/browserless/enterprise:${{needs.tags.outputs.tag}} .
      - name: Test the image
        run: docker run --ipc=host --entrypoint ./scripts/test.sh ghcr.io/browserless/enterprise:${{needs.tags.outputs.tag}}

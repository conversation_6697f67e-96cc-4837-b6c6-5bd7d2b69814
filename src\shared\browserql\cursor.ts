import { Bezier } from 'bezier-js';
import {
  KeyInput,
  _keyDefinitions as keyDefinitions,
} from 'puppeteer-core/lib/cjs/puppeteer/common/USKeyboardLayout.js';
import { getRandomArbitrary } from '../../utils.js';

const overshootThreshold = 500;
const overshootRadius = 120;

export interface Vector {
  x: number;
  y: number;
}

export const origin: Vector = { x: 0, y: 0 };

const randomFloat = (min: number, max: number): number => {
  return Math.random() * (max - min) + min;
};

const sub = (a: Vector, b: Vector): Vector => ({
  x: a.x - b.x,
  y: a.y - b.y,
});

const div = (a: Vector, b: number): Vector => ({
  x: a.x / b,
  y: a.y / b,
});

const mult = (a: Vector, b: number): Vector => ({
  x: a.x * b,
  y: a.y * b,
});

const add = (a: Vector, b: Vector): Vector => ({
  x: a.x + b.x,
  y: a.y + b.y,
});

const distance = (a: Vector, b: Vector): number =>
  Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2);
const direction = (a: Vector, b: Vector): Vector => sub(b, a);
const perpendicular = (a: Vector): Vector => ({ x: a.y, y: -1 * a.x });
const magnitude = (a: Vector): number =>
  Math.sqrt(Math.pow(a.x, 2) + Math.pow(a.y, 2));
const unit = (a: Vector): Vector => div(a, magnitude(a));
const setMagnitude = (a: Vector, amount: number): Vector =>
  mult(unit(a), amount);

const randomVectorOnLine = (a: Vector, b: Vector): Vector => {
  const vec = direction(a, b);
  const multiplier = Math.random();
  return add(a, mult(vec, multiplier));
};

const randomNormalLine = (
  a: Vector,
  b: Vector,
  range: number,
): [Vector, Vector] => {
  const randMid = randomVectorOnLine(a, b);
  const normalV = setMagnitude(perpendicular(direction(a, randMid)), range);
  return [randMid, normalV];
};

const generateBezierAnchors = (
  a: Vector,
  b: Vector,
  spread: number,
): [Vector, Vector] => {
  const side = Math.round(Math.random()) === 1 ? 1 : -1;
  const calc = (): Vector => {
    const [randMid, normalV] = randomNormalLine(a, b, spread);
    const choice = mult(normalV, side);
    return randomVectorOnLine(randMid, add(randMid, choice));
  };
  return [calc(), calc()].sort((a, b) => a.x - b.x) as [Vector, Vector];
};

const clamp = (target: number, min: number, max: number): number =>
  Math.min(max, Math.max(min, target));

const bezierCurve = (
  start: Vector,
  finish: Vector,
  overrideSpread?: number,
): Bezier => {
  // could be played around with
  const min = 2;
  const max = 200;
  const vec = direction(start, finish);
  const length = magnitude(vec);
  const spread = clamp(length, min, max);
  const anchors = generateBezierAnchors(
    start,
    finish,
    overrideSpread ?? spread,
  );
  return new Bezier(start, ...anchors, finish);
};

const clampPositive = (vectors: Vector[]): Vector[] => {
  const clamp0 = (elem: number): number => Math.max(0, elem);
  return vectors.map((vector) => {
    return {
      x: clamp0(vector.x),
      y: clamp0(vector.y),
    };
  });
};

const fitts = (distance: number, width: number): number => {
  const a = 0;
  const b = 2;
  const id = Math.log2(distance / width + 1);
  return a + b * id;
};

export const overshoot = (coordinate: Vector, radius: number): Vector => {
  const a = Math.random() * 2 * Math.PI;
  const rad = radius * Math.sqrt(Math.random());
  const vector = { x: rad * Math.cos(a), y: rad * Math.sin(a) };
  return add(coordinate, vector);
};

const shouldOvershoot = (a: Vector, b: Vector): boolean =>
  magnitude(direction(a, b)) > overshootThreshold;

export function path(start: Vector, end: Vector): Vector[] {
  const overshooting = shouldOvershoot(start, end);

  const vectorDistance = distance(start, end);
  const defaultWidth = 100;
  const minSteps = getRandomArbitrary(15, 25);
  const width = defaultWidth;
  const curve = bezierCurve(
    start,
    overshooting ? overshoot(end, overshootRadius) : end,
  );
  const length = randomFloat(0, curve.length() * 0.8);

  const speed =
    vectorDistance <= 150 ? randomFloat(0.1, 0.25) : randomFloat(0.5, 0.7);
  const baseTime = speed * minSteps;
  const steps = Math.ceil((Math.log2(fitts(length, width) + 1) + baseTime) * 3);
  const re = curve.getLUT(steps);

  if (overshooting)
    return (() => {
      const middlePoint = overshoot(end, overshootRadius);
      const curve = bezierCurve(middlePoint, end);
      const speed = 0.2;
      const baseTime = speed * minSteps;
      const steps = Math.ceil(
        (Math.log2(fitts(length, width) + 1) + baseTime) * 3,
      );
      return [...re, ...curve.getLUT(steps)];
    })();
  return clampPositive(re);
}

export const sleepRandom = async (min: number, max: number) => {
  const time = getRandomArbitrary(min, max);
  return new Promise((resolve) => setTimeout(resolve, time));
};

/**
 * Generates a random letter from the English alphabet
 * @returns string
 */
export const randomEnglishLetter = (): KeyInput =>
  String.fromCharCode(getRandomArbitrary(97, 122)) as KeyInput;

export const pressKey = (key: string) => {
  const definition =
    keyDefinitions[key as KeyInput] ||
    keyDefinitions[key.toLowerCase() as KeyInput];

  const isSpecialKey = !definition;

  if (key === 'Backspace') {
    return {
      keyDown: {
        autoRepeat: false,
        code: keyDefinitions.Backspace.code,
        isKeypad: false,
        key: keyDefinitions.Backspace.key,
        location: keyDefinitions.Backspace.location,
        modifiers: 0,
        text: '',
        type: 'rawKeyDown',
        unmodifiedText: '',
        windowsVirtualKeyCode: keyDefinitions.Backspace.keyCode,
      },
      keyUp: {
        code: keyDefinitions.Backspace.code,
        key: keyDefinitions.Backspace.key,
        location: keyDefinitions.Backspace.location,
        modifiers: 0,
        type: 'keyUp',
        windowsVirtualKeyCode: keyDefinitions.Backspace.keyCode,
      },
    };
  }

  if (isSpecialKey) {
    const data = {
      autoRepeat: false,
      code: 'Key' + key.toLowerCase(),
      isKeypad: false,
      isSystemKey: false,
      key: key,
      text: key,
      type: 'keyDown',
      unmodifiedText: key ? key.toLowerCase() : undefined,
    };

    return {
      keyDown: {
        ...data,
      },
      keyUp: {
        ...data,
        type: 'keyUp',
      },
    };
  }

  return {
    keyDown: {
      autoRepeat: false,
      code: definition.code,
      isKeypad: false,
      key: definition.key,
      location: definition.location ?? 0,
      modifiers: 0,
      text: isSpecialKey ? '' : definition.key,
      type: isSpecialKey ? 'rawKeyDown' : 'keyDown',
      unmodifiedText: isSpecialKey ? '' : definition.key,
      windowsVirtualKeyCode: definition.keyCode,
    },
    keyUp: {
      code: definition.code,
      key: definition.key,
      location: definition.location ?? 0,
      modifiers: 0,
      type: 'keyUp',
      windowsVirtualKeyCode: definition.keyCode,
    },
  };
};

name: Publish Stable Tags

# Only build for v2.x.x git tags, otherwise ignore
on:
  push:
    tags: [v2.*]

jobs:
  tags:
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.generate_tags.outputs.tag }}
      version: ${{ steps.generate_tags.outputs.version }}
      puppeteer: ${{ steps.generate_tags.outputs.puppeteer }}
      playwright: ${{ steps.generate_tags.outputs.playwright }}
      chromium: ${{ steps.generate_tags.outputs.chromium }}
      firefox: ${{ steps.generate_tags.outputs.firefox }}
      webkit: ${{ steps.generate_tags.outputs.webkit }}
    steps:
      - uses: actions/checkout@v2
      - name: Install and generate tags
        id: generate_tags
        run: npm i && node ./scripts/get-tags.js >> "$GITHUB_OUTPUT"

  publish_cloud_unit:
    needs: tags
    name: Push stable multi-platform Cloud-Unit
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }}
          password: ${{ secrets.GHCR_PASSWORD }}

      - name: Publish the Cloud-Unit Image
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          file: ./docker/cloud.dockerfile
          tags: |
            ghcr.io/browserless/cloud-unit:${{needs.tags.outputs.tag}}
            ghcr.io/browserless/cloud-unit:${{needs.tags.outputs.version}}
          labels: |
            ${{ needs.tags.outputs.puppeteer }}
            ${{ needs.tags.outputs.playwright }}
            ${{ needs.tags.outputs.chromium }}
            ${{ needs.tags.outputs.firefox }}
            ${{ needs.tags.outputs.webkit }}
          context: .
          push: true
          platforms: |
            linux/amd64
            linux/arm64

  publish_enterprise:
    needs: tags
    name: Push stable multi-platform Enterprise
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }}
          password: ${{ secrets.GHCR_PASSWORD }}

      - name: Publish the Enterprise Image
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          file: ./docker/enterprise.dockerfile
          tags: |
            ghcr.io/browserless/enterprise:${{needs.tags.outputs.tag}}
            ghcr.io/browserless/enterprise:${{needs.tags.outputs.version}}
          labels: |
            ${{ needs.tags.outputs.puppeteer }}
            ${{ needs.tags.outputs.playwright }}
            ${{ needs.tags.outputs.chromium }}
            ${{ needs.tags.outputs.firefox }}
            ${{ needs.tags.outputs.webkit }}
          context: .
          push: true
          platforms: |
            linux/amd64
            linux/arm64

import { URL } from 'url';
import { SharedConfig } from '../../../config.js';
import { IAmplitudeEvent } from '../../../types.js';
import {
  SendMessageBatchCommand,
  SendMessageBatchRequestEntry,
  SQSClient,
} from '@aws-sdk/client-sqs';
import { randomUUID } from 'crypto';
import { Logger } from 'src/logger.js';

let sqsClient: SQSClient;

/**
 * Publishes events to Amplitude with retry logic
 * @param events - Array of Amplitude events to publish
 * @param config - SharedConfig instance
 * @returns Promise<boolean> indicating success
 */
export const publishEvents = async (
  events: IAmplitudeEvent[],
  config: SharedConfig,
): Promise<boolean> => {
  if (!sqsClient) {
    sqsClient = new SQSClient({
      region: config.getSessionEndedEventsQueueRegion(),
    });
  }
  if (!events.length) {
    return true;
  }
  let success = false;
  let retries = 3;
  let entries: SendMessageBatchRequestEntry[] = events.map((event) => ({
    Id: randomUUID(),
    MessageBody: JSON.stringify(event),
  }));
  while (retries-- > 0) {
    try {
      const command = new SendMessageBatchCommand({
        QueueUrl: config.getSessionEndedEventsQueueUrl(),
        Entries: entries,
      });
      const data = await sqsClient.send(command);
      if (data.Failed?.length) {
        const failedIds = data.Failed.map((failed) => failed.Id);
        console.log('Error pushing session events', data.Failed);
        const failedEntries: SendMessageBatchRequestEntry[] = entries.filter(
          (entry) => failedIds.includes(entry.Id),
        );
        entries = failedEntries;
      } else {
        entries = [];
        break;
      }
    } catch (error) {
      console.log('Error sending event to amplitude', events, error);
    }
  }
  return success;
};

/**
 * Parses a URL into its components for detailed logging
 * @param url - The URL to parse
 * @returns Object containing parsed URL components or null if URL is invalid
 */
export function parseUrlForAnalytics(url: string) {
  try {
    const parsedUrl = new URL(url);
    return {
      href: parsedUrl.href,
      origin: parsedUrl.origin,
      protocol: parsedUrl.protocol.replace(':', ''),
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? '443' : '80'),
      pathname: parsedUrl.pathname,
      search: parsedUrl.search,
      searchParams: Object.fromEntries(parsedUrl.searchParams.entries()),
      hash: parsedUrl.hash.replace('#', ''),
    };
  } catch (error) {
    console.error('Invalid URL:', url);
    return null;
  }
}

/**
 * Creates an Amplitude event for a URL visit
 * @param url - The URL to log
 * @param eventName - Optional custom event name (defaults to 'URL Visited')
 * @returns IAmplitudeEvent object
 */
export function createUrlAmplitudeEvent(
  url: string,
  eventName: string = 'URL Visited',
  token: string,
  endpointType: 'bql' | 'baas',
): IAmplitudeEvent | null {
  try {
    const parsedUrl = new URL(url);
    return {
      event_type: eventName,
      event_properties: {
        token,
        url: parsedUrl.href,
        protocol: parsedUrl.protocol.replace(':', ''),
        hostname: parsedUrl.hostname,
        port:
          parsedUrl.port || (parsedUrl.protocol === 'https:' ? '443' : '80'),
        pathname: parsedUrl.pathname,
        search: parsedUrl.search,
        hash: parsedUrl.hash.replace('#', ''),
        endpoint_type: endpointType,
      },
      time: Date.now(),
    };
  } catch (error) {
    console.error('Failed to create Amplitude event - invalid URL:', url);
    return null;
  }
}

/**
 * Logs a URL visit with both console and Amplitude logging
 * @param url - The URL to log
 * @param config - SharedConfig instance
 * @param eventName - Optional custom event name for Amplitude
 */
export async function logUrlVisit(
  url: string,
  endpointType: 'bql' | 'baas',
  token: string,
  config: SharedConfig,
  eventName?: string,
) {
  const parsedUrl = parseUrlForAnalytics(url);
  if (parsedUrl) {
    const event = createUrlAmplitudeEvent(url, eventName, token, endpointType);
    if (event) {
      // Fire and forget - don't await the result
      publishEvents([event], config).catch((error) => {
        console.error('Failed to publish URL visit event:', error);
      });
    }
  }
}

export async function logLiveUrlInvoked({
  created,
  token,
  endpointType,
  type,
  quality,
  interactable,
  timeout,
  liveURLId,
  config,
  logger,
}: {
  created: boolean;
  token: string;
  endpointType: 'bql' | 'baas';
  type: string;
  quality: number;
  interactable: boolean;
  timeout: number;
  liveURLId?: string;
  config: SharedConfig;
  logger: Logger;
}) {
  const eventName = created ? 'Live URL Created' : 'Live URL Rejected';
  console.log(
    eventName,
    {
      token: `${token.substring(0, 20)}...`,
      created,
      endpointType,
      type,
      quality,
      interactable,
      timeout,
      liveURLId,
    },
    {
      level: 'info',
    },
  );
  const event: IAmplitudeEvent = {
    event_type: eventName,
    time: Date.now(),
    event_properties: {
      token,
      endpoint_type: endpointType,
      type,
      quality,
      interactable,
      timeout,
      live_url_id: liveURLId,
    },
  };
  publishEvents([event], config).catch((error) => {
    logger.error('Failed to publish live url invoked event:', error);
  });
}

export async function logLiveUrlVisited({
  token,
  isMobile,
  type,
  quality,
  interactable,
  liveURLId,
  config,
  logger,
}: {
  token: string;
  isMobile: boolean;
  type: string;
  quality: number;
  interactable: boolean;
  liveURLId: string;
  config: SharedConfig;
  logger: Logger;
}) {
  const event: IAmplitudeEvent = {
    event_type: 'Live URL Visited',
    time: Date.now(),
    event_properties: {
      token,
      client_type: isMobile ? 'mobile' : 'web',
      type,
      quality,
      interactable,
      live_url_id: liveURLId,
    },
  };
  publishEvents([event], config).catch((error) => {
    logger.error('Failed to publish live url visited event:', error);
  });
}

export async function logLiveUrlCompleted(
  token: string,
  config: SharedConfig,
  logger: Logger,
) {
  console.log('Live URL Completed', { token: `${token.substring(0, 20)}...` });
  const event: IAmplitudeEvent = {
    event_type: 'Live URL Completed',
    time: Date.now(),
    event_properties: { token },
  };
  publishEvents([event], config).catch((error) => {
    logger.error('Failed to publish live url completed event:', error);
  });
}

export async function logReconnectUrlCreated(
  token: string,
  endpointType: 'bql' | 'baas',
  created: boolean,
  timeout: number,
  config: SharedConfig,
  logger: Logger,
) {
  const eventName = created
    ? 'Reconnect URL Created'
    : 'Reconnect URL Rejected';
  logger.info(
    eventName,
    {
      token: `${token.substring(0, 20)}...`,
      endpointType,
      timeout,
    },
    {
      level: 'info',
    },
  );
  const event: IAmplitudeEvent = {
    event_type: eventName,
    time: Date.now(),
    event_properties: {
      token,
      endpoint_type: endpointType,
      timeout,
    },
  };
  publishEvents([event], config).catch((error) => {
    logger.error('Failed to publish reconnect url created event:', error);
  });
}

export async function logCaptchaSolveAttempt({
  token,
  captchaType,
  captchaSubtype,
  endpointType,
  config,
  logger,
  url,
}: {
  token: string | null;
  captchaType: string;
  captchaSubtype?: string;
  endpointType: 'bql' | 'baas';
  config: SharedConfig;
  logger: Logger;
  url?: string;
}) {
  logger.info('Captcha Solve Attempt', {
    token: `${token?.substring(0, 20)}...`,
    captchaType,
    captchaSubtype,
    endpointType,
    url,
  });

  if (!token) {
    return;
  }

  const event: IAmplitudeEvent = {
    event_type: 'Captcha Solve Attempt',
    time: Date.now(),
    event_properties: {
      token,
      captcha_type: captchaType,
      ...(captchaSubtype && { captcha_subtype: captchaSubtype }),
      endpoint_type: endpointType,
      url,
    },
  };

  publishEvents([event], config).catch((error) => {
    logger.error('Failed to publish captcha solve attempt event:', error);
  });
}

export async function logCaptchaSolveSuccess({
  token,
  captchaType,
  captchaSubtype,
  endpointType,
  config,
  logger,
  url,
}: {
  token: string;
  captchaType: string;
  captchaSubtype?: string;
  endpointType: 'bql' | 'baas';
  config: SharedConfig;
  logger: Logger;
  url?: string;
}) {
  logger.info('Captcha Solve Success', {
    token: `${token.substring(0, 20)}...`,
    captchaType,
    captchaSubtype,
    endpointType,
    url,
  });

  if (!token) {
    return;
  }

  const event: IAmplitudeEvent = {
    event_type: 'Captcha Solve Success',
    time: Date.now(),
    event_properties: {
      token,
      captcha_type: captchaType,
      ...(captchaSubtype && { captcha_subtype: captchaSubtype }),
      endpoint_type: endpointType,
      url,
    },
  };

  publishEvents([event], config).catch((error) => {
    logger.error('Failed to publish captcha solve success event:', error);
  });
}

export async function logCaptchaSolveError({
  token,
  captchaType,
  captchaSubtype,
  endpointType,
  errorMessage,
  config,
  logger,
  url,
}: {
  token: string | null;
  captchaType: string;
  captchaSubtype?: string;
  endpointType: 'bql' | 'baas';
  errorMessage: string;
  config: SharedConfig;
  logger: Logger;
  url?: string;
}) {
  logger.info('Captcha Solve Error', {
    token: `${token?.substring(0, 20)}...`,
    captchaType,
    captchaSubtype,
    endpointType,
    errorMessage,
    url,
  });

  if (!token) {
    return;
  }

  const event: IAmplitudeEvent = {
    event_type: 'Captcha Solve Error',
    time: Date.now(),
    event_properties: {
      token,
      captcha_type: captchaType,
      ...(captchaSubtype && { captcha_subtype: captchaSubtype }),
      endpoint_type: endpointType,
      error_message: errorMessage,
      url,
    },
  };

  publishEvents([event], config).catch((error) => {
    logger.error('Failed to publish captcha solve error event:', error);
  });
}

export async function logCaptchaSolveFailure({
  token,
  captchaType,
  captchaSubtype,
  endpointType,
  config,
  logger,
  url,
}: {
  token: string;
  captchaType: string;
  captchaSubtype?: string;
  endpointType: 'bql' | 'baas';
  config: SharedConfig;
  logger: Logger;
  url?: string;
}) {
  logger.info('Captcha Solve Failure', {
    token: `${token.substring(0, 20)}...`,
    captchaType,
    captchaSubtype,
    endpointType,
    url,
  });

  if (!token) {
    return;
  }

  const event: IAmplitudeEvent = {
    event_type: 'Captcha Solve Failure',
    time: Date.now(),
    event_properties: {
      token,
      captcha_type: captchaType,
      ...(captchaSubtype && { captcha_subtype: captchaSubtype }),
      endpoint_type: endpointType,
      url,
    },
  };

  publishEvents([event], config).catch((error) => {
    logger.error('Failed to publish captcha solve failure event:', error);
  });
}

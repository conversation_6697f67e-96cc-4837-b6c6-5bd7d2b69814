import { AugmentedRequest } from '../../../../types.js';
import { SharedConfig } from '../../../../config.js';
import { ProxyProvider } from '../ProxyVendor.js';
import getPort from 'get-port';
import httpProxy from 'http-proxy';
import http from 'http';
import net from 'net';

export class LocalProxy extends ProxyProvider {
  public name = 'LocalProxy';
  public isHealthy = true;
  public lastHealthCheck = Date.now();
  private port: number | null = null;
  private serverStarted = false;

  constructor(private config: SharedConfig) {
    super();
    this.startServer();
    this.config;
  }

  // Start a local HTTP proxy server as a last resort fallback
  // in case no other proxy vendors are healthy so the request doesn't fail
  private async startServer() {
    if (this.serverStarted) return;
    this.serverStarted = true;
    this.port = await getPort();
    const proxy = httpProxy.createProxyServer({});

    const server = http.createServer((req, res) => {
      const url = req.url;
      this.log(`[LocalProxy] ${req.method} ${url}`);

      proxy.web(req, res, { target: url, changeOrigin: true }, (err) => {
        this.log('[LocalProxy] Proxy error:', err);
        res.writeHead(502, { 'Content-Type': 'text/plain' });
        res.end('Proxy error: ' + err.message);
      });
    });

    server.on('connect', (req, clientSocket, head) => {
      const url = req.url!;
      const [host, port] = url.split(':');
      const serverSocket = net.connect(Number(port), host, () => {
        clientSocket.write(
          'HTTP/1.1 200 Connection Established\r\n' +
            'Proxy-agent: LocalProxy\r\n' +
            '\r\n',
        );
        serverSocket.write(head);
        serverSocket.pipe(clientSocket);
        clientSocket.pipe(serverSocket);
      });
      serverSocket.on('error', (err) => {
        this.log('[LocalProxy] Tunnel error:', err);
        clientSocket.end('HTTP/1.1 502 Bad Gateway\r\n\r\n');
      });
    });

    server.listen(this.port, '127.0.0.1', () => {
      this.log(`LocalProxy server started on http://127.0.0.1:${this.port}`);
    });

    server.on('error', (err) => {
      this.log('LocalProxy server error:', err);
      this.isHealthy = false;
    });
  }

  public getProxyURL = (_options: AugmentedRequest['__bless__']) => {
    return `http://127.0.0.1:${this.port}`;
  };

  public healthCheck = async () => {
    this.isHealthy = true;
    this.lastHealthCheck = Date.now();
  };
}

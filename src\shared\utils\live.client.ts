import {
  ClientCommands,
  InteractiveCommands,
  HostCommands,
  Message,
  StartMessage,
  ClipboardMessage,
} from './types.js';

const mobileOSRegex =
  /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i;

const mobileVendorOS =
  /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i;

export type SideEffectFn = (...args: unknown[]) => void;

export type Options = {
  isImmediate?: boolean;
  maxWait?: number;
};

export interface DebouncedFunction<F extends SideEffectFn> {
  (this: ThisParameterType<F>, ...args: Parameters<F>): void;
  cancel: () => void;
}

export function debounce<F extends SideEffectFn>(
  func: F,
  waitMilliseconds = 50,
  options: Options = {},
): DebouncedFunction<F> {
  let timeoutId: ReturnType<typeof setTimeout> | undefined;
  const isImmediate = options.isImmediate ?? false;
  const maxWait = options.maxWait;
  let lastInvokeTime = Date.now();

  function nextInvokeTimeout() {
    if (maxWait !== undefined) {
      const timeSinceLastInvocation = Date.now() - lastInvokeTime;

      if (timeSinceLastInvocation + waitMilliseconds >= maxWait) {
        return maxWait - timeSinceLastInvocation;
      }
    }

    return waitMilliseconds;
  }

  const debouncedFunction = function (
    this: ThisParameterType<F>,
    ...args: Parameters<F>
  ) {
    const invokeFunction = () => {
      timeoutId = undefined;
      lastInvokeTime = Date.now();
      if (!isImmediate) {
        func.apply(this, args);
      }
    };

    const shouldCallNow = isImmediate && timeoutId === undefined;

    if (timeoutId !== undefined) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(invokeFunction, nextInvokeTimeout());

    if (shouldCallNow) {
      func.apply(this, args);
    }
  };

  debouncedFunction.cancel = function cancel() {
    if (timeoutId !== undefined) {
      clearTimeout(timeoutId);
    }
  };

  return debouncedFunction;
}

const createElementFromHTML = (htmlString: string) => {
  const div = document.createElement('div');
  div.innerHTML = htmlString.trim();
  return div.firstChild;
};

const error = (message: string) =>
  createElementFromHTML(`
  <div class="dialog"/>
    <h2>Something happened</h2>
    <p class="text">${message}</p>
  </div>
`);

const done = () =>
  createElementFromHTML(`
  <div class="dialog"/>
    <h2>All done!</h2>
    <p class="text">You can safely close this window</p>
  </div>
`);

const detectMobile = () => {
  const userAgent =
    navigator.userAgent || navigator.vendor || (window as any).opera || null;

  const userAgentPart = userAgent.substr(0, 4);

  return mobileOSRegex.test(userAgent) || mobileVendorOS.test(userAgentPart);
};

export class LiveClient {
  private ws: WebSocket;
  private $canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private img = new Image();
  private width: number;
  private height: number;
  private isMobile: boolean;
  private touchData = {
    lastTouchX: 0,
    lastTouchY: 0,
    firstTouchX: 0,
    firstTouchY: 0,
    isTouching: false,
  };
  private streamWidth = 0;
  private streamHeight = 0;
  private mousePressTimeStamp = Date.now();
  private resizable = true;
  private iframeBounds: Array<{
    src: string;
    bounds: { x: number; y: number; width: number; height: number };
  }> = [];

  static getModifiersForEvent(event: any) {
    return (
      (event.altKey ? 1 : 0) |
      (event.ctrlKey ? 2 : 0) |
      (event.metaKey ? 4 : 0) |
      (event.shiftKey ? 8 : 0)
    );
  }

  constructor({
    canvas,
    height,
    width,
    wsUrl,
  }: {
    canvas: string;
    height: number;
    width: number;
    wsUrl: string;
  }) {
    this.$canvas = document.querySelector(canvas) as HTMLCanvasElement;
    this.ctx = this.$canvas.getContext('2d') as CanvasRenderingContext2D;
    this.ws = new WebSocket(wsUrl);
    this.width = width;
    this.height = height;
    this.isMobile = detectMobile();

    this.start();
  }

  private send = (event: object) => {
    this.ws.send(JSON.stringify(event));
  };

  emitMouse = (evt: any) => {
    const buttons: any = { 0: 'none', 1: 'left', 2: 'middle', 3: 'right' };
    const event: any = evt.type === 'mousewheel' ? window.event || evt : evt;
    const types: any = {
      mousedown: 'mousePressed',
      mousemove: 'mouseMoved',
      mouseup: 'mouseReleased',
      mousewheel: 'mouseWheel',
      touchend: 'mouseReleased',
      touchmove: 'mouseWheel',
      touchstart: 'mousePressed',
    };
    if (!(event.type in types)) {
      return;
    }

    if (
      event.type !== 'mousewheel' &&
      event.type !== 'touchmove' &&
      buttons[event.which] === 'none' &&
      event.type !== 'mousemove'
    ) {
      return;
    }

    const type = types[event.type] as string;
    const isScroll = type.indexOf('wheel') !== -1 && event.type !== 'touchmove';
    const isButtonPress =
      event.type === 'mouseup' || event.type === 'mousedown';
    const now = Date.now();
    const clickCount =
      isButtonPress && now - this.mousePressTimeStamp <= 100 ? 2 : 1;

    const xPos = isScroll ? event.clientX : event.offsetX;
    const yPos = isScroll ? event.clientY : event.offsetY;

    const x = Math.round((xPos / this.width) * this.streamWidth);
    const y = Math.round((yPos / this.height) * this.streamHeight);

    const iframeContext = this.getIframeContextForCoordinates(x, y);

    const data = {
      button: event.type === 'mousewheel' ? 'none' : buttons[event.which],
      clickCount,
      modifiers: LiveClient.getModifiersForEvent(event),
      type: types[event.type],
      x,
      y,
      iframeContext,
    };

    console.log('[LiveClient] Emitting mouse event:', {
      command: InteractiveCommands['Input.dispatchMouseEvent'],
      data,
      hasIframeContext: !!iframeContext,
    });

    if (event.type === 'mousewheel') {
      // @ts-ignore
      data.deltaX = event.wheelDeltaX || 0;
      // @ts-ignore
      data.deltaY = event.wheelDeltaY || event.wheelDelta;
    }

    if (event.type === 'touchmove') {
      // @ts-ignore
      data.deltaX = evt.deltaX;
      // @ts-ignore
      data.deltaY = evt.deltaY;
      data.x = evt.clientX;
      data.y = evt.clientY;
    }

    if (isButtonPress) {
      this.mousePressTimeStamp = Date.now();
    }

    this.send({
      command: InteractiveCommands['Input.dispatchMouseEvent'],
      data,
    });
  };

  emitKeyEvent = (event: KeyboardEvent) => {
    let type;

    // Handle clipboard shortcuts
    if (event.type === 'keydown') {
      this.handleClipboardShortcuts(event);
    }

    // Prevent backspace from going back in history
    if (event.keyCode === 8 || event.code === 'Tab') {
      event.preventDefault();
    }

    switch (event.type) {
      case 'keydown':
        type = 'keyDown';
        break;
      case 'keyup':
        type = 'keyUp';
        break;
      case 'keypress':
        type = 'char';
        break;
      default:
        return;
    }

    // Allow copy shortcut to be sent to browser, but handle paste entirely client-side
    const isCopyShortcut =
      (event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'c';
    const isPasteShortcut =
      (event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'v';

    // Block paste shortcuts from being sent to browser
    if (isPasteShortcut) {
      return;
    }

    if (
      type === 'keyDown' &&
      event.code !== 'Backspace' &&
      event.key !== 'Tab' &&
      !event.code.toLowerCase().includes('arrow') &&
      !isCopyShortcut // Allow copy shortcuts through
    ) {
      return;
    }

    const text =
      type === 'char' ? String.fromCharCode(event.charCode) : undefined;

    const modifiers = [
      event.altKey ? 1 : 0,
      event.ctrlKey ? 2 : 0,
      event.metaKey ? 4 : 0,
      event.shiftKey ? 8 : 0,
    ].reduce((a, b) => a + b, 0);

    const data = {
      autoRepeat: false,
      code: event.code,
      isKeypad: false,
      isSystemKey: false,
      key: event.key,
      keyIdentifier: (event as any).keyIdentifier,
      nativeVirtualKeyCode: event.keyCode,
      text,
      type,
      unmodifiedText: text ? text.toLowerCase() : undefined,
      windowsVirtualKeyCode: event.keyCode,
      modifiers,
    };

    this.send({
      command: InteractiveCommands['Input.dispatchKeyEvent'],
      data,
    });
  };

  onScreencastFrame = (data: string) => {
    // Convert binary data to blob and create object URL
    const blob = new Blob([data], { type: 'image/jpeg' });
    const url = URL.createObjectURL(blob);

    this.img.onload = () => {
      this.ctx.drawImage(
        this.img,
        0,
        0,
        this.$canvas.width,
        this.$canvas.height,
      );
      URL.revokeObjectURL(url);
    };
    this.img.src = url;
  };

  onTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0];

    this.touchData.isTouching = true;
    this.touchData.firstTouchX = touch.clientX;
    this.touchData.firstTouchY = touch.clientY;
    this.touchData.lastTouchX = touch.clientX;
    this.touchData.lastTouchY = touch.clientY;
  };

  onTouchMove = (e: any) => {
    const touch = e.touches[0];
    const deltaX = touch.clientX - (this.touchData.lastTouchX || 0);
    const deltaY = touch.clientY - (this.touchData.lastTouchY || 0);

    this.touchData.lastTouchX = touch.clientX;
    this.touchData.lastTouchY = touch.clientY;

    // Emit the modified event using the calculated deltas
    if (this.touchData.isTouching) {
      e.deltaX = deltaX;
      e.deltaY = deltaY;
      e.clientX = touch.clientX + window.scrollX;
      e.clientY = touch.clientY + window.scrollY;
      this.emitMouse(e);
    }
  };

  onTouchEnd = () => {
    this.touchData.isTouching = false;

    // This might look weird, but consider that touching is and inversion
    // of the mouse wheel: you physically drag your finger up to scroll down.
    // So we need to set the lastTouchX and lastTouchY to the firstTouchX and firstTouchY
    // so the next touchmove event will have the correct delta.
    this.touchData.lastTouchX = this.touchData.firstTouchX;
    this.touchData.lastTouchY = this.touchData.firstTouchY;
  };

  bindKeyEvents = () => {
    document.body.addEventListener('keydown', this.emitKeyEvent);
    document.body.addEventListener('keyup', this.emitKeyEvent);
    document.body.addEventListener('keypress', this.emitKeyEvent);
  };

  unbindKeyEvents = () => {
    document.body.removeEventListener('keydown', this.emitKeyEvent);
    document.body.removeEventListener('keyup', this.emitKeyEvent);
    document.body.removeEventListener('keypress', this.emitKeyEvent);
  };

  resizeCanvas = ({
    width: sourceWidth,
    height: sourceHeight,
  }: {
    width: number;
    height: number;
  }) => {
    const maxWidth = window.innerWidth;
    const maxHeight = window.innerHeight;
    const sourceAspect = sourceWidth / sourceHeight;
    const viewportAspect = maxWidth / maxHeight;

    const { width, height } =
      sourceAspect > viewportAspect
        ? { width: maxWidth, height: maxWidth / sourceAspect }
        : { width: maxHeight * sourceAspect, height: maxHeight };

    this.width = width;
    this.height = height;
    this.streamWidth = sourceWidth;
    this.streamHeight = sourceHeight;
    this.$canvas.width = width;
    this.$canvas.height = height;
  };

  addListeners = () => {
    this.bindKeyEvents();
    this.$canvas.addEventListener('mousedown', this.emitMouse, false);
    this.$canvas.addEventListener('mouseup', this.emitMouse, false);
    this.$canvas.addEventListener('mousewheel', this.emitMouse, false);
    this.$canvas.addEventListener('mousemove', this.emitMouse, false);
    this.$canvas.addEventListener('touchstart', this.onTouchStart, false);
    this.$canvas.addEventListener('touchmove', this.onTouchMove, false);
    this.$canvas.addEventListener('touchend', this.onTouchEnd, false);
    this.$canvas.addEventListener('contextmenu', this.handleContextMenu, false);
    window.addEventListener('resize', this.resizePage);
  };

  removeEventListeners = () => {
    this.$canvas.removeEventListener('mousedown', this.emitMouse, false);
    this.$canvas.removeEventListener('mouseup', this.emitMouse, false);
    this.$canvas.removeEventListener('mousewheel', this.emitMouse, false);
    this.$canvas.removeEventListener('mousemove', this.emitMouse, false);
    this.$canvas.removeEventListener('touchstart', this.onTouchStart, false);
    this.$canvas.removeEventListener('touchmove', this.onTouchMove, false);
    this.$canvas.removeEventListener('touchend', this.onTouchEnd, false);
    this.$canvas.removeEventListener(
      'contextmenu',
      this.handleContextMenu,
      false,
    );
    this.unbindKeyEvents();
    window.removeEventListener('resize', this.resizePage);
  };

  resizePage = debounce(() => {
    const { innerHeight: height, innerWidth: width } = window;

    if (!this.resizable) {
      return this.resizeCanvas({
        width: this.streamWidth,
        height: this.streamHeight,
      });
    }

    this.$canvas.width = width;
    this.$canvas.height = height;

    this.send({
      command: HostCommands.setViewport,
      data: {
        deviceScaleFactor: 1,
        height: Math.floor(height),
        width: Math.floor(width),
      },
    });
  }, 200);

  close = () => {
    this.send({ command: HostCommands.close, data: null });
    this.removeEventListeners();
  };

  start = async () => {
    this.ws.addEventListener('error', (data) => {
      const $error = error(
        `Couldn't establish a secure connection to the server.`,
      );
      document.body.appendChild($error as ChildNode);
      return console.error(data);
    });

    await new Promise((r) => this.ws.addEventListener('open', r));

    this.ws.addEventListener('message', async (evt) => {
      // Handle binary data (MJPEG frames)
      if (evt.data instanceof Blob) {
        const url = URL.createObjectURL(evt.data);
        this.img.onload = () => {
          this.ctx.drawImage(
            this.img,
            0,
            0,
            this.$canvas.width,
            this.$canvas.height,
          );
          URL.revokeObjectURL(url);
        };
        this.img.src = url;
        return;
      }

      // Handle JSON messages
      const { command, data } = JSON.parse(evt.data) as Message;

      if (command === ClientCommands.startComplete) {
        const { width, height, resizable } = data as {
          width: number;
          height: number;
          resizable: boolean;
        };
        this.resizable = resizable;
        this.resizeCanvas({ width, height });

        this.addListeners();
        this.isMobile && this.send({ command: HostCommands.enableKeyboard });
        return;
      }

      if (command === ClientCommands.error) {
        const $error = error(data as string);
        this.$canvas.style.filter = 'blur(10px)';
        document.body.appendChild($error as ChildNode);
        return this.close();
      }

      if (command === ClientCommands.runComplete) {
        const $done = done();
        this.$canvas.style.filter = 'blur(10px)';
        document.body.appendChild($done as ChildNode);
        return this.close();
      }

      if (command === ClientCommands.iframeBoundsUpdate) {
        const { frames } = data as {
          frames: Array<{
            src: string;
            bounds: { x: number; y: number; width: number; height: number };
          }>;
        };
        this.updateIframeBounds(frames);
        return;
      }

      if (command === ClientCommands.clipboardContent) {
        const clipboardData = data as ClipboardMessage;
        console.log('[LiveClient] Received clipboard content from server:', {
          contentLength: clipboardData.content.length,
          type: clipboardData.type,
          hasContent: clipboardData.content.length > 0,
          preview:
            clipboardData.content.length > 0
              ? clipboardData.content.substring(0, 50) + '...'
              : 'EMPTY',
        });

        if (clipboardData.content.length > 0) {
          this.writeToClipboard(clipboardData.content);
        } else {
          console.log(
            '[LiveClient] No content to write to clipboard - server returned empty content',
          );
        }
        return;
      }
    });

    this.send({
      command: HostCommands.start,
      data: {
        height: this.height,
        width: this.width,
      } as StartMessage,
    });
  };

  private updateIframeBounds = (
    frames: Array<{
      src: string;
      bounds: { x: number; y: number; width: number; height: number };
    }>,
  ) => {
    this.iframeBounds = frames;
  };

  private getIframeContextForCoordinates = (x: number, y: number) => {
    const matchingFrame = this.iframeBounds.find((frame) => {
      const { bounds } = frame;
      return (
        x >= bounds.x &&
        x <= bounds.x + bounds.width &&
        y >= bounds.y &&
        y <= bounds.y + bounds.height
      );
    });

    if (matchingFrame) {
      return {
        src: matchingFrame.src,
        bounds: matchingFrame.bounds,
        relativeX: x - matchingFrame.bounds.x,
        relativeY: y - matchingFrame.bounds.y,
      };
    }

    return null;
  };

  /**
   * Writes content to the user's system clipboard
   */
  private writeToClipboard = async (content: string) => {
    console.log('[LiveClient] Writing to local clipboard:', {
      contentLength: content.length,
      preview: content.substring(0, 50) + '...',
    });
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(content);
        console.log(
          '[LiveClient] Successfully wrote to clipboard using Clipboard API',
        );
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = content;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        console.log(
          '[LiveClient] Successfully wrote to clipboard using fallback method',
        );
      }
    } catch (error) {
      console.warn('[LiveClient] Failed to write to clipboard:', error);
    }
  };

  /**
   * Reads content from the user's system clipboard
   */
  private readFromClipboard = async (): Promise<string> => {
    try {
      if (navigator.clipboard && navigator.clipboard.readText) {
        const content = await navigator.clipboard.readText();
        console.log(
          '[LiveClient] Successfully read from clipboard using Clipboard API:',
          {
            contentLength: content.length,
            preview: content.substring(0, 50) + '...',
          },
        );
        return content;
      } else {
        // Fallback for older browsers - this won't work in all scenarios
        // but provides a basic implementation
        console.log(
          '[LiveClient] Clipboard API not available, using fallback (limited functionality)',
        );
        return '';
      }
    } catch (error) {
      console.warn('[LiveClient] Failed to read from clipboard:', error);
      return '';
    }
  };

  /**
   * Handles clipboard keyboard shortcuts
   */
  private handleClipboardShortcuts = (event: KeyboardEvent) => {
    const isCtrlOrCmd = event.ctrlKey || event.metaKey;

    if (isCtrlOrCmd && event.key.toLowerCase() === 'c') {
      console.log(
        '[LiveClient] Copy shortcut detected, will request clipboard after browser copy',
      );
      // Copy operation - let the browser handle the copy first, then request clipboard content
      // Reduced delay since we're now capturing content more directly
      setTimeout(() => {
        console.log('[LiveClient] Requesting clipboard content from server');
        this.send({
          command: HostCommands.requestClipboard,
          data: null,
        });
      }, 50);
    } else if (isCtrlOrCmd && event.key.toLowerCase() === 'v') {
      console.log(
        '[LiveClient] Paste shortcut detected, preventing default and using custom paste',
      );
      // Prevent the default paste behavior to avoid conflicts
      event.preventDefault();
      // Handle paste through our custom flow
      this.handlePaste();
    }
  };

  /**
   * Handles paste operations
   */
  private handlePaste = async () => {
    console.log(
      '[LiveClient] Reading from local clipboard for paste operation',
    );
    const content = await this.readFromClipboard();
    if (content) {
      console.log('[LiveClient] Sending paste content to server:', {
        contentLength: content.length,
        preview: content.substring(0, 50) + '...',
      });
      this.send({
        command: HostCommands.pasteClipboard,
        data: {
          content,
          type: 'text',
        } as ClipboardMessage,
      });
    } else {
      console.log('[LiveClient] No content found in local clipboard for paste');
    }
  };

  /**
   * Handles right-click context menu for clipboard operations
   */
  private handleContextMenu = (event: MouseEvent) => {
    event.preventDefault();

    // Create a simple context menu
    const contextMenu = document.createElement('div');
    contextMenu.style.cssText = `
      position: fixed;
      top: ${event.clientY}px;
      left: ${event.clientX}px;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      z-index: 10000;
      padding: 4px 0;
      min-width: 120px;
      font-family: Arial, sans-serif;
      font-size: 14px;
    `;

    const copyOption = document.createElement('div');
    copyOption.textContent = 'Copy';
    copyOption.style.cssText = `
      padding: 8px 16px;
      cursor: pointer;
      border-bottom: 1px solid #eee;
    `;
    copyOption.onmouseover = () =>
      (copyOption.style.backgroundColor = '#f0f0f0');
    copyOption.onmouseout = () => (copyOption.style.backgroundColor = '');
    copyOption.onclick = () => {
      // First trigger a copy operation in the browser, then request the clipboard content
      this.send({
        command: InteractiveCommands['Input.dispatchKeyEvent'],
        data: {
          type: 'keyDown',
          key: 'c',
          code: 'KeyC',
          modifiers: navigator.platform.includes('Mac') ? 4 : 2, // Cmd on Mac, Ctrl on others
          autoRepeat: false,
          isKeypad: false,
          isSystemKey: false,
          nativeVirtualKeyCode: 67,
          windowsVirtualKeyCode: 67,
        },
      });

      // Then request the clipboard content after a delay
      setTimeout(() => {
        this.send({
          command: HostCommands.requestClipboard,
          data: null,
        });
      }, 50);

      document.body.removeChild(contextMenu);
    };

    const pasteOption = document.createElement('div');
    pasteOption.textContent = 'Paste';
    pasteOption.style.cssText = `
      padding: 8px 16px;
      cursor: pointer;
    `;
    pasteOption.onmouseover = () =>
      (pasteOption.style.backgroundColor = '#f0f0f0');
    pasteOption.onmouseout = () => (pasteOption.style.backgroundColor = '');
    pasteOption.onclick = () => {
      this.handlePaste();
      document.body.removeChild(contextMenu);
    };

    contextMenu.appendChild(copyOption);
    contextMenu.appendChild(pasteOption);
    document.body.appendChild(contextMenu);

    // Remove context menu when clicking elsewhere
    const removeMenu = (e: MouseEvent) => {
      if (!contextMenu.contains(e.target as Node)) {
        document.body.removeChild(contextMenu);
        document.removeEventListener('click', removeMenu);
      }
    };
    setTimeout(() => document.addEventListener('click', removeMenu), 0);
  };
}

(async () => {
  const canvasSelector = '#browserless-screen';
  const width = window.innerWidth;
  const height = window.innerHeight;
  const url = new URL(window.location.href);
  const id = url.searchParams.get('i');
  const timeout = url.searchParams.get('t');

  if (!id) {
    throw new Error(`An ID parameter must be specified in the URL.`);
  }

  url.protocol = url.protocol === 'https:' ? 'wss:' : 'ws:';
  url.search = '';
  url.pathname = url.pathname.replace('index.html', '') + id;

  if (timeout) {
    url.searchParams.set('timeout', timeout);
  }

  const canvas = document.querySelector(canvasSelector) as HTMLCanvasElement;
  canvas.width = width;
  canvas.height = height;

  new LiveClient({
    canvas: canvasSelector,
    height,
    width,
    wsUrl: url.href,
  });
})();

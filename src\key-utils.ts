/**
 * Software key utilities for handling time-limited cryptographically secure keys
 */
import crypto from 'crypto';
import { createLogger } from '@browserless.io/browserless';

const log = createLogger('key-utils');

const SECRET_KEY = 'a0389f7b-5607-4521-a682-623b243bea39';

const SALT = 'browserless-io-enterprise-key';

const ALGORITHM = 'aes-256-cbc';
const ITERATIONS = 100000;
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const DIGEST = 'sha256';

interface SoftwareKey {
  expiresAt: number; // Timestamp in milliseconds
  created: number; // When the key was created
  id: string; // Unique ID for the key
}

/**
 * Encrypts data using AES-256-CBC encryption
 * @param data Data to encrypt
 * @returns Encrypted string (hex encoded)
 */
const encrypt = (data: string): string => {
  try {
    const iv = crypto.randomBytes(IV_LENGTH);

    const key = crypto.pbkdf2Sync(
      SECRET_KEY,
      SALT,
      ITERATIONS,
      KEY_LENGTH,
      DIGEST,
    );

    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const ivHex = iv.toString('hex');

    return ivHex + encrypted;
  } catch (error) {
    log(`Error encrypting data: ${error}`);
    throw error;
  }
};

/**
 * Decrypts data using AES-256-CBC encryption
 * @param encryptedData Encrypted data to decrypt (hex encoded)
 * @returns Decrypted string or null if decryption fails
 */
const decrypt = (encryptedData: string): string | null => {
  try {
    const ivHex = encryptedData.substring(0, IV_LENGTH * 2);
    const iv = Buffer.from(ivHex, 'hex');

    const encrypted = encryptedData.substring(IV_LENGTH * 2);

    const key = crypto.pbkdf2Sync(
      SECRET_KEY,
      SALT,
      ITERATIONS,
      KEY_LENGTH,
      DIGEST,
    );

    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    log(`Error decrypting data: ${error}`);
    return null;
  }
};

/**
 * Encodes a software key with an expiration date
 * @param expiresAt Expiration date timestamp in milliseconds
 * @returns Encoded and encrypted key string
 */
export const encodeSoftwareKey = (expiresAt: number): string => {
  const key: SoftwareKey = {
    expiresAt,
    created: Date.now(),
    id: crypto.randomBytes(16).toString('hex'),
  };

  const jsonString = JSON.stringify(key);

  return encrypt(jsonString);
};

/**
 * Decodes a software key to extract the expiration date
 * @param encodedKey The encoded and encrypted key string
 * @returns The expiration date timestamp or null if invalid
 */
export const decodeSoftwareKey = (encodedKey: string): number | null => {
  try {
    const jsonString = decrypt(encodedKey);

    if (!jsonString) {
      return null;
    }

    const key = JSON.parse(jsonString) as SoftwareKey;

    if (
      typeof key.expiresAt !== 'number' ||
      typeof key.created !== 'number' ||
      typeof key.id !== 'string'
    ) {
      log(`Invalid key structure: ${JSON.stringify(key)}`);
      return null;
    }

    return key.expiresAt;
  } catch (error) {
    log(`Error decoding key: ${error}`);
    return null;
  }
};

/**
 * Checks if a software key is valid (not expired)
 * @param encodedKey The encoded key string
 * @returns Boolean indicating if the key is valid
 */
export const isSoftwareKeyValid = (encodedKey: string): boolean => {
  const expiresAt = decodeSoftwareKey(encodedKey);
  if (expiresAt === null) {
    log(`Invalid key format`);
    return false;
  }

  const now = Date.now();
  const isValid = now < expiresAt;

  if (!isValid) {
    log(
      `Key expired on ${new Date(expiresAt).toISOString()}, current time is ${new Date(now).toISOString()}`,
    );
  }

  return isValid;
};

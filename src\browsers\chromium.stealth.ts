import {
  BrowserLauncherOptions,
  ChromiumCDP,
  Logger,
  Request,
  ServerError,
  createLogger,
} from '@browserless.io/browserless';
import { spawn } from 'child_process';
import { Duplex } from 'stream';
import getPort from 'get-port';
import { chromium } from 'playwright-core';
import path from 'path';

import { SharedConfig } from '../config.js';
import { EnterpriseChromiumCDP } from './core.js';
import { parseLaunchOptions } from '../utils.js';

type childProc = ReturnType<typeof spawn>;

const startChrome = async (
  executablePath: string,
  args: string[],
  logger: Logger,
): Promise<{
  process: childProc;
  browserWSEndpoint: string;
}> =>
  new Promise(async (resolve, reject) => {
    logger.trace({ args, executablePath }, `Final Spawn`);
    const proc = spawn(executablePath, args);
    const removeListeners = () => {
      proc.off('error', onError);
      proc.off('exit', onError);
      proc.stderr.off('data', onMessage);
    };
    const onError = (err: Error) => {
      removeListeners();
      reject(err);
    };
    const onMessage = (message: Buffer) => {
      const log = message.toString();
      logger.debug(log);
      const matchWsEndpoint = log.match(/DevTools listening on .*/g);
      if (matchWsEndpoint) {
        removeListeners();
        return resolve({
          process: proc,
          browserWSEndpoint: matchWsEndpoint[0]
            .replace('DevTools listening on ', '')
            .trim(),
        });
      }
    };

    proc.once('error', onError);
    proc.stderr.on('data', onMessage);
    proc.on('exit', onError);
  });

export class ChromiumStealthBrowser extends EnterpriseChromiumCDP {
  protected debug = createLogger('browsers:stealth');
  private browserProcess?: childProc;
  protected config: SharedConfig;
  protected executablePath = chromium.executablePath();

  constructor({
    userDataDir,
    config,
    blockAds,
    logger,
  }: {
    blockAds: boolean;
    config: SharedConfig;
    record: boolean;
    userDataDir: ChromiumCDP['userDataDir'];
    logger: Logger;
  }) {
    super({
      userDataDir,
      config,
      blockAds,
      logger,
    });
    this.config = config;
  }

  protected cleanListeners() {}

  protected async onTargetCreated() {
    throw new Error(`Not yet implemented`);
  }

  public async pages() {
    return [];
  }

  public isRunning(): boolean {
    return this.running;
  }

  public async close(): Promise<void> {
    if (this.browserProcess) {
      this.debug(`Closing browser process and all listeners`);
      this.emit('close');
      this.cleanListeners();
      this.browserProcess.kill();
      this.running = false;
      this.browser = null;
      this.browserWSEndpoint = null;
    }
  }

  /**
   * The internal-only browser WS endpoint as generated by Chrome.
   * Not suitable for external or end-users to be given, but can
   * be used for proxying external calls into it.
   */
  public wsEndpoint(): string | null {
    return this.browserWSEndpoint;
  }

  /**
   * Uses the HOST and PORT options to generate the WebSocket URL for
   * internal scripts to use. Doing so avoids code from going through several
   * networking layers, but still allows for our CDP firewall and other
   * features to happen.
   */
  public privateWSEndpoint(token?: string | null): string | null {
    if (!this.browserWSEndpoint) {
      return null;
    }

    const serverURL = new URL(this.config.getServerAddress());
    const wsURL = new URL(this.browserWSEndpoint);

    wsURL.hostname = serverURL.hostname;
    wsURL.port = serverURL.port;
    wsURL.pathname = path.join(serverURL.pathname, wsURL.pathname);
    wsURL.protocol = serverURL.protocol.startsWith('https') ? 'wss:' : 'ws:';

    if (token) {
      wsURL.searchParams.set('token', token);
    }

    return wsURL.href;
  }

  /**
   * Uses the EXTERNAL config URL, if specified, to
   * generate a public endpoint suitable for exposure to end-users.
   * When this image is running on browserless.io, it also builds out the
   * /p/* path so our load-balancer can handle it appropriately.
   * Does not add the token parameter to the generated URL.
   */
  public publicWSEndpoint(): string | null {
    if (!this.browserWSEndpoint) {
      return null;
    }

    const serverURL = new URL(this.config.getExternalWebSocketAddress());
    const wsURL = new URL(this.browserWSEndpoint);

    wsURL.hostname = serverURL.hostname;
    wsURL.port = serverURL.port;
    wsURL.pathname = path.join(serverURL.pathname, wsURL.pathname);
    wsURL.protocol = serverURL.protocol;

    return wsURL.href;
  }

  public process() {
    return this.browser?.process() || null;
  }

  public async launch(options: BrowserLauncherOptions): Promise<any> {
    const finalOptions = await parseLaunchOptions(options);
    finalOptions.options.args = finalOptions.options.args || [];
    this.port = await getPort();
    this.logger.info(`${this.constructor.name} got open port ${this.port}`);

    finalOptions.options.args.push(
      '--allow-pre-commit-input',
      '--disable-add-to-shelf',
      '--disable-background-networking',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-breakpad',
      '--disable-client-side-phishing-detection',
      '--disable-component-update',
      '--disable-default-apps',
      '--disable-dev-shm-usage',
      '--disable-hang-monitor',
      '--disable-infobars',
      '--disable-ipc-flooding-protection',
      '--disable-popup-blocking',
      '--disable-prompt-on-repost',
      '--disable-renderer-backgrounding',
      '--disable-search-engine-choice-screen',
      '--disable-sync',
      '--export-tagged-pdf',
      '--generate-pdf-document-outline',
      '--force-color-profile=srgb',
      '--no-default-browser-check',
      '--no-first-run',
      `--remote-debugging-port=${this.port}`,
      '--use-mock-keychain',
      '--start-maximized',
      '--no-sandbox',
      '--webrtc-ip-handling-policy=disable_non_proxied_udp',
      '--force-webrtc-ip-handling-policy',
      '--disable-features=WebRtcHideLocalIpsWithMdns',
      '--enforce-webrtc-ip-permission-check',

      // 🥷 STEALTH-SPECIFIC FLAGS (leak protection is handled globally in utils.ts)
      `--remote-debugging-port=${this.port}`,
      '--disable-features=OptimizationGuideModelDownloading',
      this.userDataDir ? `--user-data-dir=${this.userDataDir}` : '',
      'about:blank',
    );

    const { process, browserWSEndpoint } = await startChrome(
      this.executablePath,
      finalOptions.options.args,
      this.logger,
    );
    this.running = true;
    this.browserProcess = process;
    this.browserWSEndpoint = browserWSEndpoint;
    this.logger.debug(
      `Stealth running on PID ${process.pid}, PORT: ${this.port}, ENDPOINT: ${browserWSEndpoint}`,
    );
  }

  public proxyPageWebSocket(
    req: Request,
    socket: Duplex,
    head: Buffer,
  ): Promise<void> {
    return new Promise(async (resolve, reject) => {
      if (!this.browserWSEndpoint) {
        throw new ServerError(
          `No browserWSEndpoint found, did you launch first?`,
        );
      }
      socket.once('close', resolve);
      this.logger.info(
        `Proxying ${req.parsed.href} to ${this.constructor.name}`,
      );

      const pathname = req.parsed.pathname;
      const target = new URL(pathname, this.browserWSEndpoint).href;
      req.url = '';

      // Delete headers known to cause issues
      delete req.headers.origin;

      this.proxy.ws(
        req,
        socket,
        head,
        {
          changeOrigin: true,
          target,
        },
        (error) => {
          this.logger.error(
            `Error proxying session to ${this.constructor.name}: ${error}`,
          );
          this.close();
          return reject(error);
        },
      );
    });
  }
}

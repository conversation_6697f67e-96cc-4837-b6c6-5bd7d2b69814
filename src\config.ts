import path, { join } from 'path';
import crypto from 'crypto-js';
import { tmpdir } from 'os';

import { Config, exists } from '@browserless.io/browserless';
import { randomID } from './utils.js';

export class SharedConfig extends Config {
  protected heartBeatInterval = +(process.env.HEARTBEAT_INTERVAL || '30000');
  protected amplitudeKey = process.env.AMPLITUDE_KEY || '';

  protected stateFile =
    process.env.STATE_FILE || path.join(tmpdir(), 'browserless-state.json');

  protected sessionEndedEventsQueueUrl =
    process.env.SESSION_ENDED_EVENTS_QUEUE_URL || '';

  protected sessionEndedEventsQueueRegion =
    process.env.SESSION_ENDED_EVENTS_QUEUE_REGION || '';

  protected workerId = process.env.WORKER_ID;

  protected encryptExternalAddress(address: string, encryptionKey: string) {
    const b64 = crypto.AES.encrypt(address, encryptionKey).toString();
    const e64 = crypto.enc.Base64.parse(b64);
    const eHex = e64.toString(crypto.enc.Hex);
    return eHex;
  }

  public async getStateFile(): Promise<string> {
    return this.stateFile;
  }

  public async setStatePath(stateFile: string): Promise<string> {
    if (await exists(stateFile)) {
      throw new Error(`File "${stateFile}" already exists.`);
    }
    this.stateFile = stateFile;
    this.emit('state-file', stateFile);
    return this.stateFile;
  }

  public getHeartBeatInterval() {
    return this.heartBeatInterval;
  }

  public setHeartBeatInterval(time: number) {
    return (this.heartBeatInterval = time);
  }

  public getSessionEndedEventsQueueUrl() {
    return this.sessionEndedEventsQueueUrl;
  }

  public setSessionEndedEventsQueueUrl(url: string) {
    return (this.sessionEndedEventsQueueUrl = url);
  }

  public getSessionEndedEventsQueueRegion() {
    return this.sessionEndedEventsQueueRegion;
  }

  public setSessionEndedEventsQueueRegion(region: string) {
    return (this.sessionEndedEventsQueueRegion = region);
  }

  public getWorkerId() {
    return this.workerId;
  }

  public setWorkerId(workerId: string) {
    return (this.workerId = workerId);
  }

  public allowLiveURLs(_apiKey: string) {
    return false;
  }

  public allowCaptchaSolving(_apiKey: string) {
    return false;
  }

  public allowReconnect(_apiKey: string) {
    return false;
  }

  public allowRecord(_apiKey: string) {
    return false;
  }

  public allowCityProxying(_apiKey: string) {
    return false;
  }

  public allowStateProxying(_apiKey: string) {
    return false;
  }

  public allowThirdPartyProxy(_apiKey: string) {
    return false;
  }

  public getMaxReconnectTime(apiKey: string | null): number {
    if (!apiKey) {
      return 0;
    }
    return 30_000;
  }

  public isFreePlan(_apiKey: string | null) {
    return false;
  }

  public async setExternalEncryptedAddress(
    externalBaseUrl: string,
    encryptionKey: string,
  ) {
    try {
      const response = await fetch('https://httpbin.org/ip');
      const ip = (await response.json()).origin;
      if (!ip) {
        throw new Error('Could not get external ip from httpbin');
      }
      const encryptedAddress = this.encryptExternalAddress(
        `${ip}:${this.port}`,
        encryptionKey,
      );
      this.setExternalAddress(`${externalBaseUrl}/e/${encryptedAddress}`);
    } catch {
      try {
        const response = await fetch('https://checkip.amazonaws.com');
        const ip = (await response.text()).replace('\n', '');
        if (!ip) {
          throw new Error('Could not get external ip from amazonaws');
        }
        const encryptedAddress = this.encryptExternalAddress(
          `${ip}:${this.port}`,
          encryptionKey,
        );
        this.setExternalAddress(
          `https://${externalBaseUrl}/e/${encryptedAddress}`,
        );
      } catch (error) {
        console.warn(`Could not set external encrypted address.`, error);
      }
    }
  }

  public getProxyUsername() {
    return process.env.PROXY_USERNAME ?? 'browserlessio';
  }

  public getProxyPassword() {
    return process.env.PROXY_PASSWORD ?? process.env.IP_ROYAL_PW;
  }

  public getMassiveUsername() {
    return process.env.MASSIVE_USERNAME ?? 'browserlessio';
  }

  public getMassivePassword() {
    return process.env.MASSIVE_PASSWORD;
  }

  public getAmplitudeKey() {
    return this.amplitudeKey;
  }

  public disableBlocklist() {
    return false;
  }

  public getMaxRetention(_apiKey: string | null) {
    return 365 * 24 * 60 * 60 * 1000;
  }

  public getSessionEncryptionKey() {
    return process.env.SESSION_ENCRYPTION_KEY ?? randomID(32);
  }

  public getSessionsDir() {
    return process.env.SESSIONS_DIR || join(process.cwd(), 'data', 'sessions');
  }
}

export default new SharedConfig();

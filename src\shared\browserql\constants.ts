export const hCaptcha = {
  input: `[name='h-captcha-response']`,
  iframe: `iframe[data-hcaptcha-response]`,
  url: `*hcaptcha*#frame*sitekey=*`,
  param: 'sitekey',
};

export const recaptcha = {
  input: `[name='g-recaptcha-response']`,
  url: `*google.com/recaptcha*k=*`,
  param: 'k',
  callback: '[data-callback]',
};

export const cloudFlareCDP = {
  keySelector: '.cf-turnstile[data-sitekey]',
  responseSelector: '[name="cf-turnstile-response"]',
};

export const cloudFlare = {
  selector: '< *challenges.cloudflare.com* label.cb-lb',
  solved: '< *challenges.cloudflare.com* #success',
};

export const funcaptcha = {
  url: `https://client-api.arkoselabs.com/fc/`,
  input: `#fc-token`,
  iframe: `"[data-e2e=\"enforcement-frame\"]"`,
  param: 'pk',
};

export const friendlyCaptcha = {
  input: `[name='frc-captcha-solution']`,
  keySelector: '.frc-captcha[data-sitekey]',
  callback: '[data-callback]',
  solutionFieldName: '[data-solution-field-name]',
  url: `*friendlycaptcha*`,
  param: 'data-sitekey',
};

export const geetest = {
  url: `*api.geetest.com/get.php?gt=*`,
  gtParam: 'gt',
  challengeParam: 'challenge',
};

export const textCaptcha = {
  questionSelectors: [
    '.captcha-question',
    '[class*="captcha-question"]',
    '[id*="captcha-question"]',
    '.text-captcha-question',
    '[class*="text-captcha"]',
    '[aria-label*="captcha question"]',
    '[data-captcha-question]',
    'label[for*="captcha"]',
    '.math-captcha',
    '[class*="math-captcha"]',
    // Common patterns for text captcha questions
    'span:contains("What")',
    'div:contains("What")',
    'p:contains("What")',
    'label:contains("What")',
    // Question patterns
    '*:contains("solve")',
    '*:contains("answer")',
    '*:contains("calculate")',
    '*:contains("question")',
  ],
  inputSelectors: [
    'input[name*="captcha"]',
    'input[id*="captcha"]',
    'input[name*="answer"]',
    'input[id*="answer"]',
    'input[name*="question"]',
    'input[id*="question"]',
    'input[name*="challenge"]',
    'input[id*="challenge"]',
    'input[name*="verification"]',
    'input[id*="verification"]',
    'input[name*="text-captcha"]',
    'input[id*="text-captcha"]',
    'input[name*="math"]',
    'input[id*="math"]',
    '.captcha input[type="text"]',
    '.text-captcha input[type="text"]',
    '.math-captcha input[type="text"]',
  ],
};

export const isDeepSelector = (selector: string) => selector.startsWith('< ');

export const paperFormats = {
  letter: { width: 8.5, height: 11 },
  legal: { width: 8.5, height: 14 },
  tabloid: { width: 11, height: 17 },
  ledger: { width: 17, height: 11 },
  a0: { width: 33.1102, height: 46.811 },
  a1: { width: 23.3858, height: 33.1102 },
  a2: { width: 16.5354, height: 23.3858 },
  a3: { width: 11.6929, height: 16.5354 },
  a4: { width: 8.2677, height: 11.6929 },
  a5: { width: 5.8268, height: 8.2677 },
  a6: { width: 4.1339, height: 5.8268 },
};

// NOTE: We do a strict equality comparison on cloud to ensure that this query works, otherwise queries aren't allowed.
// Any changes, whitespace or otherwise, will require updates to the cloud-unit service in order for free accounts to work.
export const editorStart = `mutation bqlstart{reconnect(timeout:10000){browserQLEndpoint devtoolsFrontendUrl webSocketDebuggerUrl}}`;

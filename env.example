DEBUG="browserless*"
TOKEN=
TIMEOUT=60000

#CORS
CORS=1

# Binding
PORT=5555

# For communicating back with our APIs
API_URL=""
API_TOKEN=""
PROXY_USERNAME=""
PROXY_PASSWORD=""
ENTERPRISE_API_TOKEN=""
IP_ROYAL_PW=""
AWS_ACCESS_KEY_ID="notanid"
AWS_SECRET_ACCESS_KEY="notakey"
KEY=
SESSION_ENDED_EVENTS_QUEUE_URL="your-aws-sqs-queue-url"
SESSION_ENDED_EVENTS_QUEUE_REGION="your-aws-region"
SESSION_ENCRYPTION_KEY=tEgo0gUcbE7dAM6g4WaKgsMluzRsjt67iThk+KAoXDw=
PYLON_API_KEY="op://test_server_secrets/Pylon_Creds/password"
PYLON_API_URL="op://test_server_secrets/Pylon_Creds/URL"
